# Due Diligence Nexus Platform

A comprehensive due diligence management platform designed to streamline M&A, investment, and partnership transactions.

## Project Overview

The Due Diligence Nexus Platform is a full-stack application that provides:
- Deal management and tracking
- Document management with AI-powered analysis
- Multi-persona AI review system
- Requisition template management
- Real-time collaboration tools
- Comprehensive reporting and analytics

## Public Pages

The platform includes several public pages that users can access without authentication:

- **Landing Page** (`/`) - Main homepage with platform overview and features
- **About Page** (`/about`) - Information about the platform and team
- **Features Page** (`/features`) - Detailed feature descriptions and capabilities
- **Contact Page** (`/contact`) - Contact form and company information

These pages provide a comprehensive introduction to the platform before users sign up or log in.

## Getting Started

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- PostgreSQL database
- Redis (for caching and job queues)

### Installation

```bash
git clone <YOUR_GIT_URL>
cd due-diligence-nexus-platform
npm install
```

### Environment Setup

1. Copy the environment files:
```bash
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

2. Update the environment variables in both `.env` files with your database credentials and other configuration.

3. Set up the database:
```bash
cd backend
npm run db:migrate
npm run db:seed
```

### Running the Application

Start both frontend and backend:
```bash
npm run dev
```

Or run them separately:
```bash
# Backend (from backend directory)
npm run dev

# Frontend (from frontend directory)
npm run dev
```

## Technology Stack

### Backend
- Node.js with Express
- TypeScript
- Prisma ORM
- PostgreSQL
- Redis
- JWT Authentication
- Bull (Job Queues)
- Winston (Logging)

### Frontend
- React 18
- TypeScript
- Vite
- Tailwind CSS
- shadcn/ui
- React Router
- React Hook Form
- Recharts

## Project Structure

```
due-diligence-nexus-platform/
├── backend/                 # Backend API server
│   ├── src/
│   │   ├── config/         # Configuration files
│   │   ├── controllers/    # Route controllers
│   │   ├── middlewares/    # Express middlewares
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── prisma/             # Database schema and migrations
│   └── tests/              # Backend tests
├── frontend/               # React frontend application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services
│   │   └── types/          # TypeScript type definitions
└── docs/                   # Project documentation
```

## Features

- **Deal Management**: Create, track, and manage M&A deals and transactions
- **Document Management**: Upload, organize, and review due diligence documents
- **AI-Powered Analysis**: Multi-persona AI system for document review and analysis
- **Requisition Templates**: Customizable templates for different deal types
- **User Management**: Role-based access control and organization management
- **Real-time Collaboration**: Comments, notifications, and activity tracking
- **Reporting**: Comprehensive analytics and reporting capabilities

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
6. Discuss and refine until ready for merge

## License

This project is proprietary software. All rights reserved.
