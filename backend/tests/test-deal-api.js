// Quick test script to verify Deal API integration
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8000/api';

// Test user credentials (you'll need to create a user first)
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

let authToken = '';

async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser),
    });

    const data = await response.json();
    
    if (data.success) {
      authToken = data.data.accessToken;
      console.log('✅ Login successful');
      return true;
    } else {
      console.log('❌ Login failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return false;
  }
}

async function testCreateDeal() {
  try {
    const dealData = {
      title: 'Test API Deal',
      type: 'MA',
      value: 50000000,
      jurisdiction: 'Delaware',
      buyer: 'Test Buyer Corp',
      seller: 'Test Seller Inc',
      notes: 'This is a test deal created via API'
    };

    const response = await fetch(`${BASE_URL}/deals`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(dealData),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Deal created successfully:', data.data.title);
      return data.data.id;
    } else {
      console.log('❌ Deal creation failed:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Deal creation error:', error.message);
    return null;
  }
}

async function testGetDeals() {
  try {
    const response = await fetch(`${BASE_URL}/deals`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Retrieved deals successfully:', data.data.deals.length, 'deals found');
      return true;
    } else {
      console.log('❌ Get deals failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Get deals error:', error.message);
    return false;
  }
}

async function testGetDealStats() {
  try {
    const response = await fetch(`${BASE_URL}/deals/stats`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Retrieved deal stats successfully:', data.data);
      return true;
    } else {
      console.log('❌ Get deal stats failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Get deal stats error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Starting Deal API Tests...\n');

  // Test login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  // Test create deal
  console.log('\n📝 Testing deal creation...');
  const dealId = await testCreateDeal();

  // Test get deals
  console.log('\n📋 Testing get deals...');
  await testGetDeals();

  // Test get deal stats
  console.log('\n📊 Testing deal statistics...');
  await testGetDealStats();

  console.log('\n✅ All tests completed!');
}

// Run the tests
runTests().catch(console.error);
