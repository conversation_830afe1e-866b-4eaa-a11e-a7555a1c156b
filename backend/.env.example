# Server Configuration
PORT=8000
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/due_diligence_db"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (SendGrid)
EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=your-sendgrid-api-key-here
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Due Diligence Platform

# Application URLs
APP_URL=http://localhost:8000
FRONTEND_URL=http://localhost:8080

# File Upload Configuration
UPLOAD_MAX_SIZE=10mb
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv

# Storage Configuration (choose one)
STORAGE_TYPE=local
# For AWS S3:
# STORAGE_TYPE=s3
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1
# AWS_BUCKET=your-bucket-name

# Redis Configuration
REDIS_URL=redis://localhost:6379

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
AI_ANALYSIS_ENABLED=false

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=false
