{"name": "due-diligence-nexus-backend", "version": "1.0.0", "description": "Due Diligence Nexus Platform Backend", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "@prisma/client": "^6.12.0", "@sendgrid/mail": "^8.1.5", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.1", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "pdf-parse": "^1.1.1", "pino": "^9.7.0", "pino-pretty": "^13.1.0", "prisma": "^6.12.0", "redis": "^5.6.0", "textract": "^2.5.0", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.15", "@types/nodemailer": "^6.4.17", "@types/pdf-parse": "^1.1.5", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "supertest": "^7.1.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}