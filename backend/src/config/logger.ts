import pino from 'pino';

// Create logger configuration based on environment
const createLogger = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const logLevel = process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info');

  const baseConfig = {
    level: logLevel,
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label: string) => {
        return { level: label };
      },
    },
  };

  if (isDevelopment) {
    // Pretty print for development
    return pino({
      ...baseConfig,
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss',
          ignore: 'pid,hostname',
          messageFormat: '{msg}',
        },
      },
    });
  } else {
    // JSON format for production
    return pino(baseConfig);
  }
};

// Create and export the logger instance
export const logger = createLogger();

// Helper functions for common logging patterns
export const loggers = {
  // AI Review specific logger
  aiReview: logger.child({ module: 'ai-review' }),

  // Document processing logger
  document: logger.child({ module: 'document' }),

  // Gemini API logger
  gemini: logger.child({ module: 'gemini' }),

  // Database logger
  database: logger.child({ module: 'database' }),

  // Authentication logger
  auth: logger.child({ module: 'auth' }),

  // API logger
  api: logger.child({ module: 'api' }),

  // Server logger
  server: logger.child({ module: 'server' }),

  // Queue logger
  queue: logger.child({ module: 'queue' }),

  // Report generation logger
  report: logger.child({ module: 'report' }),
};

// Export default logger
export default logger;
