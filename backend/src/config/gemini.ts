import { GoogleGenerativeA<PERSON>, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';

// Initialize Google AI client
export const getGeminiClient = () => {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error("GEMINI_API_KEY is not defined in environment variables");
  }
  return new GoogleGenerativeAI(apiKey);
};

// Gemini configuration
export const GEMINI_CONFIG = {
  model: process.env.GEMINI_MODEL || "gemini-2.0-flash-exp",
  maxTokens: 8192,
  temperature: 0.1, // Low temperature for consistent analysis
  topP: 0.8,
  topK: 40,

  // Safety settings for document analysis
  safetySettings: [
    {
      category: HarmCategory.HARM_CATEGORY_HARASSMENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
  ],

  // Generation config for consistent output
  generationConfig: {
    temperature: 0.1,
    topK: 40,
    topP: 0.8,
    maxOutputTokens: 8192,
  },
};

// Helper function to get model with configuration
export const getConfiguredModel = () => {
  const genAI = getGeminiClient();
  return genAI.getGenerativeModel({
    model: GEMINI_CONFIG.model,
    generationConfig: GEMINI_CONFIG.generationConfig,
    safetySettings: GEMINI_CONFIG.safetySettings,
  });
};

// Error handling for Gemini API
export class GeminiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'GeminiError';
  }
}

// Helper to handle Gemini API errors
export const handleGeminiError = (error: any): GeminiError => {
  if (error.message?.includes('API key')) {
    return new GeminiError('Invalid or missing Gemini API key', 'API_KEY_ERROR', error);
  }

  if (error.message?.includes('quota')) {
    return new GeminiError('Gemini API quota exceeded', 'QUOTA_EXCEEDED', error);
  }

  if (error.message?.includes('rate limit')) {
    return new GeminiError('Gemini API rate limit exceeded', 'RATE_LIMIT', error);
  }

  if (error.message?.includes('safety')) {
    return new GeminiError('Content blocked by safety filters', 'SAFETY_FILTER', error);
  }

  return new GeminiError(
    error.message || 'Unknown Gemini API error',
    'UNKNOWN_ERROR',
    error
  );
};
