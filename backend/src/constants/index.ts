// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  // Authentication
  INVALID_CREDENTIALS: 'Invalid email or password',
  TOKEN_EXPIRED: 'Token has expired',
  TOKEN_INVALID: 'Invalid token',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',

  // User Management
  USER_NOT_FOUND: 'User not found',
  USER_ALREADY_EXISTS: 'User already exists',
  EMAIL_ALREADY_REGISTERED: 'Email is already registered',
  INVALID_EMAIL_FORMAT: 'Invalid email format',
  PASSWORD_TOO_WEAK: 'Password must be at least 8 characters long',

  // Deal Management
  DEAL_NOT_FOUND: 'Deal not found',
  DEAL_ACCESS_DENIED: 'Access to this deal is denied',
  INVALID_DEAL_STATUS: 'Invalid deal status',
  DEAL_ALREADY_EXISTS: 'Deal with this title already exists',

  // Document Management
  DOCUMENT_NOT_FOUND: 'Document not found',
  FILE_TOO_LARGE: 'File size exceeds maximum allowed size',
  INVALID_FILE_TYPE: 'File type not allowed',
  UPLOAD_FAILED: 'File upload failed',
  DOCUMENT_ACCESS_DENIED: 'Access to this document is denied',

  // Review System
  REVIEW_NOT_FOUND: 'Review not found',
  REVIEW_ALREADY_COMPLETED: 'Review is already completed',
  INVALID_REVIEW_STATUS: 'Invalid review status',

  // General
  VALIDATION_ERROR: 'Validation error',
  INTERNAL_ERROR: 'Internal server error',
  NOT_FOUND: 'Resource not found',
  BAD_REQUEST: 'Bad request',
  RATE_LIMIT_EXCEEDED: 'Too many requests, please try again later',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  // Authentication
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  PASSWORD_RESET_SENT: 'Password reset email sent',
  PASSWORD_RESET_SUCCESS: 'Password reset successful',
  EMAIL_VERIFIED: 'Email verified successfully',

  // User Management
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',

  // Deal Management
  DEAL_CREATED: 'Deal created successfully',
  DEAL_UPDATED: 'Deal updated successfully',
  DEAL_DELETED: 'Deal deleted successfully',
  DEAL_ASSIGNED: 'Deal assigned successfully',
  DEAL_STATUS_UPDATED: 'Deal status updated successfully',

  // Document Management
  DOCUMENT_UPLOADED: 'Document uploaded successfully',
  DOCUMENT_UPDATED: 'Document updated successfully',
  DOCUMENT_DELETED: 'Document deleted successfully',

  // Review System
  REVIEW_STARTED: 'Review started successfully',
  REVIEW_COMPLETED: 'Review completed successfully',
  REVIEW_APPROVED: 'Review approved successfully',

  // Reports
  REPORT_GENERATED: 'Report generated successfully',
  REPORT_DOWNLOADED: 'Report downloaded successfully',
} as const;

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 50 * 1024 * 1024, // 50MB in bytes
  ALLOWED_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/jpg',
    'image/png',
  ],
  ALLOWED_EXTENSIONS: [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.jpg', '.jpeg', '.png'
  ],
} as const;

// Pagination Constants
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// Date Formats
export const DATE_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE_ONLY: 'YYYY-MM-DD',
  DISPLAY: 'MMM DD, YYYY',
  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',
} as const;

// Email Templates
export const EMAIL_TEMPLATES = {
  WELCOME: 'welcome',
  PASSWORD_RESET: 'password-reset',
  EMAIL_VERIFICATION: 'email-verification',
  DEAL_CREATED: 'deal-created',
  DEAL_UPDATED: 'deal-updated',
  REVIEW_COMPLETED: 'review-completed',
  REPORT_READY: 'report-ready',
  DEADLINE_REMINDER: 'deadline-reminder',
} as const;

// AI Persona Types
export const AI_PERSONAS = {
  LEGAL: 'LEGAL',
  FINANCIAL: 'FINANCIAL',
  TECHNICAL: 'TECHNICAL',
  OPERATIONAL: 'OPERATIONAL',
  COMPLIANCE: 'COMPLIANCE',
  STRATEGIC: 'STRATEGIC',
  RISK: 'RISK',
} as const;

// Review Confidence Thresholds
export const CONFIDENCE_THRESHOLDS = {
  LOW: 60,
  MEDIUM: 80,
  HIGH: 95,
} as const;

// Cache Keys
export const CACHE_KEYS = {
  USER_SESSION: (userId: string) => `user:session:${userId}`,
  DEAL_DATA: (dealId: string) => `deal:${dealId}`,
  REVIEW_STATUS: (dealId: string) => `review:status:${dealId}`,
  ANALYTICS: (type: string) => `analytics:${type}`,
} as const;

// Queue Names
export const QUEUE_NAMES = {
  EMAIL: 'email-queue',
  DOCUMENT_PROCESSING: 'document-processing-queue',
  AI_ANALYSIS: 'ai-analysis-queue',
  REPORT_GENERATION: 'report-generation-queue',
  NOTIFICATIONS: 'notifications-queue',
} as const;

// Job Types
export const JOB_TYPES = {
  SEND_EMAIL: 'send-email',
  PROCESS_DOCUMENT: 'process-document',
  AI_ANALYSIS: 'ai-analysis',
  GENERATE_REPORT: 'generate-report',
  SEND_NOTIFICATION: 'send-notification',
  CLEANUP_FILES: 'cleanup-files',
} as const;
