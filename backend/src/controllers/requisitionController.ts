import { Request, Response } from 'express';
import { RequisitionService } from '../services/requisitionService';
import { HTTP_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../constants';
import {
  CreateRequisitionTemplateRequest,
  UpdateDealRequisitionRequest,
  CreateDealRequisitionRequest,
  RequisitionTemplateListQuery,
} from '../types/requisition';

export class RequisitionController {
  // GET /api/requisitions/templates
  static async getRequisitionTemplates(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const query: RequisitionTemplateListQuery = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        dealType: req.query.dealType as any,
        industry: req.query.industry as string,
        search: req.query.search as string,
        isDefault: req.query.isDefault ? req.query.isDefault === 'true' : undefined,
      };

      const result = await RequisitionService.getRequisitionTemplates(
        query,
        req.user.organizationId
      );

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Get requisition templates error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to get requisition templates',
      });
    }
  }

  // GET /api/requisitions/templates/:id
  static async getRequisitionTemplateById(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Template ID is required',
        });
      }

      const template = await RequisitionService.getRequisitionTemplateById(
        id,
        req.user.organizationId
      );

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: template,
      });
    } catch (error) {
      console.error('Get requisition template by ID error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.NOT_FOUND 
        ? HTTP_STATUS.NOT_FOUND 
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to get requisition template',
      });
    }
  }

  // POST /api/requisitions/templates
  static async createRequisitionTemplate(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const templateData: CreateRequisitionTemplateRequest = req.body;

      // Validate required fields
      if (!templateData.name || !templateData.dealType || !templateData.categories) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Name, deal type, and categories are required',
        });
      }

      const template = await RequisitionService.createRequisitionTemplate(
        templateData,
        req.user.id
      );

      res.status(HTTP_STATUS.CREATED).json({
        success: true,
        message: 'Requisition template created successfully',
        data: template,
      });
    } catch (error) {
      console.error('Create requisition template error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to create requisition template',
      });
    }
  }

  // GET /api/requisitions/deals/:dealId
  static async getDealRequisition(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { dealId } = req.params;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const dealRequisition = await RequisitionService.getDealRequisition(
        dealId,
        req.user.id
      );

      if (!dealRequisition) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          error: ERROR_MESSAGES.NOT_FOUND,
          message: 'Deal requisition not found',
        });
      }

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: dealRequisition,
      });
    } catch (error) {
      console.error('Get deal requisition error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to get deal requisition',
      });
    }
  }

  // POST /api/requisitions/deals
  static async createDealRequisition(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const requisitionData: CreateDealRequisitionRequest = req.body;

      // Validate required fields
      if (!requisitionData.dealId || !requisitionData.templateId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID and template ID are required',
        });
      }

      const dealRequisition = await RequisitionService.createDealRequisition(
        requisitionData,
        req.user.id
      );

      res.status(HTTP_STATUS.CREATED).json({
        success: true,
        message: 'Deal requisition created successfully',
        data: dealRequisition,
      });
    } catch (error) {
      console.error('Create deal requisition error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND 
        ? HTTP_STATUS.NOT_FOUND 
        : message.includes('already exists')
        ? HTTP_STATUS.CONFLICT
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to create deal requisition',
      });
    }
  }

  // PUT /api/requisitions/deals/:dealId
  static async updateDealRequisition(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { dealId } = req.params;
      const updateData: UpdateDealRequisitionRequest = req.body;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      if (!updateData.customData) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Custom data is required',
        });
      }

      const dealRequisition = await RequisitionService.updateDealRequisition(
        dealId,
        updateData,
        req.user.id
      );

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Deal requisition updated successfully',
        data: dealRequisition,
      });
    } catch (error) {
      console.error('Update deal requisition error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message.includes('not found') 
        ? HTTP_STATUS.NOT_FOUND 
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to update deal requisition',
      });
    }
  }

  // GET /api/requisitions/deals/:dealId/progress
  static async getDealRequisitionProgress(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { dealId } = req.params;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const dealRequisition = await RequisitionService.getDealRequisition(
        dealId,
        req.user.id
      );

      if (!dealRequisition) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          error: ERROR_MESSAGES.NOT_FOUND,
          message: 'Deal requisition not found',
        });
      }

      // Calculate progress statistics
      const stats = {
        totalItems: 0,
        completedItems: 0,
        pendingItems: 0,
        requiredItems: 0,
        optionalItems: 0,
        completionPercentage: 0,
        lastUpdated: dealRequisition.updatedAt,
      };

      const categoryProgress = dealRequisition.customData.categories.map(category => {
        const categoryStats = {
          totalItems: category.items.length,
          completedItems: category.items.filter(item => 
            item.status === 'uploaded' || item.status === 'approved'
          ).length,
          requiredItems: category.items.filter(item => item.isRequired).length,
        };

        stats.totalItems += categoryStats.totalItems;
        stats.completedItems += categoryStats.completedItems;
        stats.requiredItems += categoryStats.requiredItems;
        stats.optionalItems += categoryStats.totalItems - categoryStats.requiredItems;
        stats.pendingItems += categoryStats.totalItems - categoryStats.completedItems;

        return {
          categoryId: category.id,
          categoryName: category.name,
          totalItems: categoryStats.totalItems,
          completedItems: categoryStats.completedItems,
          requiredItems: categoryStats.requiredItems,
          completionPercentage: categoryStats.totalItems > 0 
            ? Math.round((categoryStats.completedItems / categoryStats.totalItems) * 100)
            : 0,
          items: category.items.map(item => ({
            id: item.id,
            name: item.name,
            isRequired: item.isRequired,
            status: item.status,
          })),
        };
      });

      stats.completionPercentage = stats.totalItems > 0 
        ? Math.round((stats.completedItems / stats.totalItems) * 100)
        : 0;

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: {
          stats,
          categoryProgress,
        },
      });
    } catch (error) {
      console.error('Get deal requisition progress error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to get deal requisition progress',
      });
    }
  }
}
