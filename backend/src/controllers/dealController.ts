import { Request, Response } from 'express';
import { DealService } from '../services/dealService';
import { HTTP_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../constants';
import {
  CreateDealRequest,
  UpdateDealRequest,
  DealListQuery,
  DealAssignmentRequest,
  DealStatusUpdateRequest,
  AddDealNoteRequest,
} from '../types/deal';

export class DealController {
  // POST /api/deals
  static async createDeal(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const dealData: CreateDealRequest = req.body;

      // Validate required fields
      if (!dealData.title || !dealData.type) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Title and type are required',
        });
      }

      const deal = await DealService.createDeal(
        dealData,
        req.user.id,
        req.user.organizationId
      );

      res.status(HTTP_STATUS.CREATED).json({
        success: true,
        message: SUCCESS_MESSAGES.DEAL_CREATED,
        data: deal,
      });
    } catch (error) {
      console.error('Create deal error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_ALREADY_EXISTS
        ? HTTP_STATUS.CONFLICT
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to create deal',
      });
    }
  }

  // GET /api/deals
  static async getDeals(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const query: DealListQuery = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        search: req.query.search as string,
        status: req.query.status as any,
        type: req.query.type as any,
        assignedToId: req.query.assignedToId as string,
        createdById: req.query.createdById as string,
        sortBy: req.query.sortBy as any,
        sortOrder: req.query.sortOrder as any,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
      };

      const result = await DealService.getDeals(
        query,
        req.user.id,
        req.user.organizationId
      );

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Get deals error:', error);

      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to get deals',
      });
    }
  }

  // GET /api/deals/:id
  static async getDealById(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const deal = await DealService.getDealById(id, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: deal,
      });
    } catch (error) {
      console.error('Get deal by ID error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to get deal',
      });
    }
  }

  // PUT /api/deals/:id
  static async updateDeal(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;
      const updateData: UpdateDealRequest = req.body;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const deal = await DealService.updateDeal(id, updateData, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DEAL_UPDATED,
        data: deal,
      });
    } catch (error) {
      console.error('Update deal error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to update deal',
      });
    }
  }

  // DELETE /api/deals/:id
  static async deleteDeal(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      await DealService.deleteDeal(id, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DEAL_DELETED,
      });
    } catch (error) {
      console.error('Delete deal error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to delete deal',
      });
    }
  }

  // POST /api/deals/:id/assign
  static async assignDeal(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;
      const assignmentData: DealAssignmentRequest = req.body;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      if (!assignmentData.assignedToId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Assigned user ID is required',
        });
      }

      const deal = await DealService.assignDeal(id, assignmentData, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DEAL_ASSIGNED,
        data: deal,
      });
    } catch (error) {
      console.error('Assign deal error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to assign deal',
      });
    }
  }

  // PUT /api/deals/:id/status
  static async updateDealStatus(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;
      const statusData: DealStatusUpdateRequest = req.body;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      if (!statusData.status) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Status is required',
        });
      }

      const deal = await DealService.updateDealStatus(id, statusData, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.DEAL_UPDATED,
        data: deal,
      });
    } catch (error) {
      console.error('Update deal status error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to update deal status',
      });
    }
  }

  // POST /api/deals/:id/submit-documents
  static async submitDocumentsForReview(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const deal = await DealService.submitDocumentsForReview(id, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Documents submitted for AI review successfully',
        data: deal,
      });
    } catch (error) {
      console.error('Submit documents error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : message.includes('No documents have been uploaded')
          ? HTTP_STATUS.BAD_REQUEST
          : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to submit documents for review',
      });
    }
  }

  // POST /api/deals/:id/notes
  static async addDealNote(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { id } = req.params;
      const noteData: AddDealNoteRequest = req.body;

      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      if (!noteData.note || noteData.note.trim().length === 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Note content is required',
        });
      }

      const deal = await DealService.addDealNote(id, noteData, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Note added successfully',
        data: deal,
      });
    } catch (error) {
      console.error('Add deal note error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.DEAL_NOT_FOUND
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to add note',
      });
    }
  }

  // GET /api/deals/stats
  static async getDealStats(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const stats = await DealService.getDealStats(
        req.user.id,
        req.user.organizationId
      );

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Get deal stats error:', error);

      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to get deal statistics',
      });
    }
  }
}
