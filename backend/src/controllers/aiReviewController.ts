import { Request, Response } from 'express';
import { AIReviewOrchestrator } from '../services/aiReviewOrchestrator';
import { AIPersonaService } from '../services/aiPersonaService';
import { GeminiService } from '../services/geminiService';
import { HTTP_STATUS, ERROR_MESSAGES } from '../constants';

export class AIReviewController {
  // POST /api/ai-review/start
  static async startReview(req: Request, res: Response) {
    try {
      const { dealId } = req.body;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      // Check if review is already running by checking the results
      const data = await AIReviewOrchestrator.getReviewResults(dealId);
      if (data.summary.isRunning) {
        return res.status(HTTP_STATUS.CONFLICT).json({
          error: 'Review already running',
          message: 'AI review is already in progress for this deal',
        });
      }

      // Ensure user is authenticated
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required'
        });
      }

      // Start the AI review process
      await AIReviewOrchestrator.startAIReview(dealId, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'AI review started successfully',
        data: {
          dealId,
          status: 'started'
        }
      });

    } catch (error) {
      console.error('Start AI review error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message.includes('not found')
        ? HTTP_STATUS.NOT_FOUND
        : message.includes('No documents')
          ? HTTP_STATUS.BAD_REQUEST
          : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to start AI review',
      });
    }
  }



  // GET /api/ai-review/results/:dealId
  static async getResults(req: Request, res: Response) {
    try {
      const { dealId } = req.params;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const data = await AIReviewOrchestrator.getReviewResults(dealId);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: data.results,
        summary: data.summary
      });

    } catch (error) {
      console.error('Get AI review results error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to get AI review results',
      });
    }
  }

  // GET /api/ai-review/personas
  static async getPersonas(req: Request, res: Response) {
    try {
      const personas = await AIPersonaService.getActivePersonas();

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: personas
      });

    } catch (error) {
      console.error('Get personas error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to get AI personas',
      });
    }
  }

  // POST /api/ai-review/test-connection
  static async testConnection(req: Request, res: Response) {
    try {
      const isConnected = await GeminiService.testConnection();

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: {
          connected: isConnected,
          message: isConnected ? 'Gemini API connection successful' : 'Gemini API connection failed'
        }
      });

    } catch (error) {
      console.error('Test Gemini connection error:', error);

      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Connection test failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  }



  // POST /api/ai-review/initialize-personas
  static async initializePersonas(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      await AIPersonaService.initializeDefaultPersonas();

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'AI personas initialized successfully'
      });

    } catch (error) {
      console.error('Initialize personas error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to initialize AI personas',
      });
    }
  }

  // POST /api/ai-review/reset/:dealId
  static async resetReview(req: Request, res: Response) {
    try {
      const { dealId } = req.params;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      await AIReviewOrchestrator.resetReview(dealId);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'AI review reset successfully'
      });

    } catch (error) {
      console.error('Reset AI review error:', error);

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to reset AI review',
      });
    }
  }
}
