import { Request, Response } from 'express';
import { ReportGenerationService } from '../services/reportGenerationService';
import { ERROR_MESSAGES, HTTP_STATUS } from '../constants';
import { loggers } from '../config/logger';

export class ReportController {
  // POST /api/deals/:dealId/reports/generate
  static async generateReport(req: Request, res: Response) {
    try {
      const { dealId } = req.params;
      const { reportType = 'EXECUTIVE_SUMMARY' } = req.body;
      const userId = req.user?.id;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      if (!userId) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'User authentication required',
        });
      }

      loggers.report.info({ dealId, reportType, userId }, 'Starting report generation');

      const report = await ReportGenerationService.generateReport(dealId, reportType, userId);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Report generation started successfully',
        data: report
      });

    } catch (error) {
      loggers.report.error({ error: error instanceof Error ? error.message : error }, 'Report generation failed');

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message.includes('not found')
        ? HTTP_STATUS.NOT_FOUND
        : message.includes('No AI analysis')
          ? HTTP_STATUS.BAD_REQUEST
          : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Failed to generate report',
      });
    }
  }

  // GET /api/deals/:dealId/reports
  static async getReports(req: Request, res: Response) {
    try {
      const { dealId } = req.params;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const reports = await ReportGenerationService.getReportsByDeal(dealId);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: reports
      });

    } catch (error) {
      loggers.report.error({ error: error instanceof Error ? error.message : error }, 'Failed to get reports');

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to get reports',
      });
    }
  }

  // GET /api/reports/:reportId
  static async getReportById(req: Request, res: Response) {
    try {
      const { reportId } = req.params;

      if (!reportId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Report ID is required',
        });
      }

      const report = await ReportGenerationService.getReportById(reportId);

      if (!report) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          error: 'Report not found',
          message: 'The requested report does not exist',
        });
      }

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: report
      });

    } catch (error) {
      loggers.report.error({ error: error instanceof Error ? error.message : error }, 'Failed to get report');

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to get report',
      });
    }
  }

  // GET /api/reports/:reportId/download
  static async downloadReport(req: Request, res: Response) {
    try {
      const { reportId } = req.params;

      if (!reportId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Report ID is required',
        });
      }

      const reportData = await ReportGenerationService.downloadReport(reportId);

      if (!reportData) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          error: 'Report not found',
          message: 'The requested report does not exist or is not ready for download',
        });
      }

      // Set headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${reportData.filename}"`);
      res.setHeader('Content-Length', reportData.size);

      // Send the PDF data
      res.send(reportData.data);

    } catch (error) {
      loggers.report.error({ error: error instanceof Error ? error.message : error }, 'Failed to download report');

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to download report',
      });
    }
  }

  // GET /api/reports/:reportId/status
  static async getReportStatus(req: Request, res: Response) {
    try {
      const { reportId } = req.params;

      if (!reportId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Report ID is required',
        });
      }

      const status = await ReportGenerationService.getReportStatus(reportId);

      if (!status) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          error: 'Report not found',
          message: 'The requested report does not exist',
        });
      }

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: status
      });

    } catch (error) {
      loggers.report.error({ error: error instanceof Error ? error.message : error }, 'Failed to get report status');

      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: message,
        message: 'Failed to get report status',
      });
    }
  }
}
