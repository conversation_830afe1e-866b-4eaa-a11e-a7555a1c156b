import { Request, Response } from "express";
import { PersonaType, UserRole } from "@prisma/client";
import prisma from "../config/database";
import { loggers } from "../config/logger";
import { AIPersonaService } from "../services/aiPersonaService";

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    organizationId: string;
  };
}

export class PersonaController {
  // Get all available personas
  static async getAllPersonas(req: Request, res: Response) {
    try {
      const personas = await AIPersonaService.getActivePersonas();

      res.json({
        success: true,
        data: personas,
      });
    } catch (error) {
      loggers.server.error({ error }, "Error fetching personas");
      res.status(500).json({
        success: false,
        message: "Failed to fetch personas",
      });
    }
  }

  // Get user's selected personas
  static async getUserSelectedPersonas(
    req: AuthenticatedRequest,
    res: Response
  ) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { selectedPersonas: true },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }

      res.json({
        success: true,
        data: {
          selectedPersonas: user.selectedPersonas,
        },
      });
    } catch (error) {
      loggers.server.error({ error }, "Error fetching user selected personas");
      res.status(500).json({
        success: false,
        message: "Failed to fetch selected personas",
      });
    }
  }

  // Update user's selected personas
  static async updateUserSelectedPersonas(
    req: AuthenticatedRequest,
    res: Response
  ) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      const { selectedPersonas } = req.body;

      // Validate that selectedPersonas is an array of valid PersonaType values
      if (!Array.isArray(selectedPersonas)) {
        return res.status(400).json({
          success: false,
          message: "selectedPersonas must be an array",
        });
      }

      // Validate each persona type
      const validPersonaTypes = Object.values(PersonaType);
      const invalidPersonas = selectedPersonas.filter(
        (persona) => !validPersonaTypes.includes(persona)
      );

      if (invalidPersonas.length > 0) {
        return res.status(400).json({
          success: false,
          message: `Invalid persona types: ${invalidPersonas.join(", ")}`,
        });
      }

      // Update user's selected personas in database
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { selectedPersonas },
        select: { selectedPersonas: true },
      });

      loggers.server.info(
        {
          userId,
          selectedPersonas: updatedUser.selectedPersonas,
        },
        "Updated user selected personas"
      );

      res.json({
        success: true,
        data: {
          selectedPersonas: updatedUser.selectedPersonas,
        },
        message: "Selected personas updated successfully",
      });
    } catch (error) {
      loggers.server.error({ error }, "Error updating user selected personas");
      res.status(500).json({
        success: false,
        message: "Failed to update selected personas",
      });
    }
  }

  // Get persona definitions for frontend
  static async getPersonaDefinitions(req: Request, res: Response) {
    try {
      const personaDefinitions = Object.values(PersonaType).map((type) => {
        const definition = AIPersonaService.getPersonaDefinition(type);
        return {
          type,
          name: definition.name,
          description: definition.description,
          focusAreas: definition.focusAreas,
          expertise: definition.expertise,
        };
      });

      res.json({
        success: true,
        data: personaDefinitions,
      });
    } catch (error) {
      loggers.server.error({ error }, "Error fetching persona definitions");
      res.status(500).json({
        success: false,
        message: "Failed to fetch persona definitions",
      });
    }
  }
}
