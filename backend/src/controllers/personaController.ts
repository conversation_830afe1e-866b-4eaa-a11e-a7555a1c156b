import { Request, Response } from 'express';
import { PersonaType, UserRole } from '@prisma/client';
import prisma from '../config/database';
import { loggers } from '../config/logger';
import { AIPersonaService } from '../services/aiPersonaService';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    organizationId: string;
  };
}

export class PersonaController {
  // Get all available personas
  static async getAllPersonas(req: Request, res: Response) {
    try {
      const personas = await AIPersonaService.getActivePersonas();

      res.json({
        success: true,
        data: personas
      });
    } catch (error) {
      loggers.server.error({ error }, 'Error fetching personas');
      res.status(500).json({
        success: false,
        message: 'Failed to fetch personas'
      });
    }
  }

  // Get user's selected personas
  static async getUserSelectedPersonas(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // TODO: Implement selectedPersonas when user preferences feature is added
      res.json({
        success: true,
        data: {
          selectedPersonas: [] // Return empty array for now
        }
      });
    } catch (error) {
      loggers.server.error({ error }, 'Error fetching user selected personas');
      res.status(500).json({
        success: false,
        message: 'Failed to fetch selected personas'
      });
    }
  }

  // Update user's selected personas
  static async updateUserSelectedPersonas(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }

      const { selectedPersonas } = req.body;

      // Validate that selectedPersonas is an array of valid PersonaType values
      if (!Array.isArray(selectedPersonas)) {
        return res.status(400).json({
          success: false,
          message: 'selectedPersonas must be an array'
        });
      }

      // Validate each persona type
      const validPersonaTypes = Object.values(PersonaType);
      const invalidPersonas = selectedPersonas.filter(persona => !validPersonaTypes.includes(persona));

      if (invalidPersonas.length > 0) {
        return res.status(400).json({
          success: false,
          message: `Invalid persona types: ${invalidPersonas.join(', ')}`
        });
      }

      // TODO: Implement selectedPersonas update when user preferences feature is added
      // For now, just validate the user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      loggers.server.info({
        userId,
        selectedPersonas
      }, 'User selected personas update requested (feature not yet implemented)');

      res.json({
        success: true,
        data: {
          selectedPersonas // Return the requested personas for now
        },
        message: 'Selected personas updated successfully'
      });
    } catch (error) {
      loggers.server.error({ error }, 'Error updating user selected personas');
      res.status(500).json({
        success: false,
        message: 'Failed to update selected personas'
      });
    }
  }

  // Get persona definitions for frontend
  static async getPersonaDefinitions(req: Request, res: Response) {
    try {
      const personaDefinitions = Object.values(PersonaType).map(type => {
        const definition = AIPersonaService.getPersonaDefinition(type);
        return {
          type,
          name: definition.name,
          description: definition.description,
          focusAreas: definition.focusAreas,
          expertise: definition.expertise
        };
      });

      res.json({
        success: true,
        data: personaDefinitions
      });
    } catch (error) {
      loggers.server.error({ error }, 'Error fetching persona definitions');
      res.status(500).json({
        success: false,
        message: 'Failed to fetch persona definitions'
      });
    }
  }
}
