import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { HTTP_STATUS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../constants';
import { omit } from '../utils';

export class AuthController {
  // POST /api/auth/login
  static async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Email and password are required',
        });
      }

      const result = await AuthService.login({ email, password });

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.LOGIN_SUCCESS,
        data: result,
      });
    } catch (error) {
      console.error('Login error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message === ERROR_MESSAGES.INVALID_CREDENTIALS 
        ? HTTP_STATUS.UNAUTHORIZED 
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: message,
        message: 'Login failed',
      });
    }
  }

  // POST /api/auth/register
  static async register(req: Request, res: Response) {
    try {
      const { email, password, firstName, lastName, organizationId, role } = req.body;

      if (!email || !password || !firstName || !lastName) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Email, password, first name, and last name are required',
        });
      }

      const result = await AuthService.register({
        email,
        password,
        firstName,
        lastName,
        organizationId,
        role,
      });

      res.status(HTTP_STATUS.CREATED).json({
        success: true,
        message: SUCCESS_MESSAGES.USER_CREATED,
        data: result,
      });
    } catch (error) {
      console.error('Registration error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode = message.includes('already') 
        ? HTTP_STATUS.CONFLICT 
        : HTTP_STATUS.BAD_REQUEST;

      res.status(statusCode).json({
        error: message,
        message: 'Registration failed',
      });
    }
  }

  // POST /api/auth/logout
  static async logout(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const token = req.headers.authorization?.substring(7); // Remove 'Bearer '
      if (token) {
        await AuthService.logout(req.user.id, token);
      }

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.LOGOUT_SUCCESS,
      });
    } catch (error) {
      console.error('Logout error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Logout failed',
      });
    }
  }

  // POST /api/auth/logout-all
  static async logoutAll(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      await AuthService.logoutAll(req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Logged out from all devices successfully',
      });
    } catch (error) {
      console.error('Logout all error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Logout failed',
      });
    }
  }

  // POST /api/auth/refresh
  static async refreshToken(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Refresh token is required',
        });
      }

      const result = await AuthService.refreshToken(refreshToken);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Token refreshed successfully',
        data: result,
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        error: ERROR_MESSAGES.TOKEN_INVALID,
        message: 'Token refresh failed',
      });
    }
  }

  // POST /api/auth/forgot-password
  static async forgotPassword(req: Request, res: Response) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Email is required',
        });
      }

      await AuthService.requestPasswordReset(email);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.PASSWORD_RESET_SENT,
      });
    } catch (error) {
      console.error('Forgot password error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Password reset request failed',
      });
    }
  }

  // POST /api/auth/reset-password
  static async resetPassword(req: Request, res: Response) {
    try {
      const { token, password } = req.body;

      if (!token || !password) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Token and new password are required',
        });
      }

      await AuthService.resetPassword(token, password);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESS,
      });
    } catch (error) {
      console.error('Reset password error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.BAD_REQUEST).json({
        error: message,
        message: 'Password reset failed',
      });
    }
  }

  // GET /api/auth/me
  static async getProfile(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const user = await AuthService.getProfile(req.user.id);

      if (!user) {
        return res.status(HTTP_STATUS.NOT_FOUND).json({
          error: ERROR_MESSAGES.USER_NOT_FOUND,
          message: 'User not found',
        });
      }

      // Remove sensitive information
      const safeUser = omit(user, ['passwordHash']);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        data: safeUser,
      });
    } catch (error) {
      console.error('Get profile error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to get user profile',
      });
    }
  }

  // PUT /api/auth/me
  static async updateProfile(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { firstName, lastName } = req.body;

      if (!firstName && !lastName) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'At least one field must be provided for update',
        });
      }

      const updatedUser = await AuthService.updateProfile(req.user.id, {
        firstName,
        lastName,
      });

      // Remove sensitive information
      const safeUser = omit(updatedUser, ['passwordHash']);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: SUCCESS_MESSAGES.USER_UPDATED,
        data: safeUser,
      });
    } catch (error) {
      console.error('Update profile error:', error);
      
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: 'Failed to update user profile',
      });
    }
  }

  // POST /api/auth/change-password
  static async changePassword(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Current password and new password are required',
        });
      }

      await AuthService.changePassword(req.user.id, currentPassword, newPassword);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      console.error('Change password error:', error);
      
      const message = error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      res.status(HTTP_STATUS.BAD_REQUEST).json({
        error: message,
        message: 'Password change failed',
      });
    }
  }
}
