import { Request, Response } from 'express';
import { DocumentService } from '../services/documentService';
import { HTTP_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../constants';
import { DocumentListQuery, UpdateDocumentStatusRequest } from '../types/document';

export class DocumentController {
  // POST /api/deals/:dealId/documents/upload
  static async uploadDocuments(req: Request, res: Response) {
    try {
      console.log('📤 Upload request received:', {
        dealId: req.params.dealId,
        filesCount: req.files ? (req.files as Express.Multer.File[]).length : 0,
        body: req.body,
        user: req.user?.id
      });

      if (!req.user) {
        console.log('❌ Upload failed: No authentication');
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { dealId } = req.params;
      const { requisitionItemId, remarks } = req.body;
      const files = req.files as Express.Multer.File[];

      if (!dealId) {
        console.log('❌ Upload failed: No deal ID');
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      if (!files || files.length === 0) {
        console.log('❌ Upload failed: No files provided');
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'At least one file is required',
        });
      }

      console.log('🔄 Processing upload with DocumentService...');
      const documentService = new DocumentService();
      const result = await documentService.uploadDocuments(
        files,
        dealId,
        req.user.id,
        requisitionItemId,
        remarks
      );

      console.log('✅ Upload processing completed:', {
        success: result.success,
        documentsCount: result.documents?.length || 0,
        failedCount: result.failed?.length || 0
      });

      const statusCode = result.success ? HTTP_STATUS.CREATED : HTTP_STATUS.BAD_REQUEST;

      res.status(statusCode).json({
        success: result.success,
        message: result.message,
        data: result.documents,
        failed: result.failed,
      });
    } catch (error) {
      console.error('💥 Document upload error:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        dealId: req.params.dealId,
        user: req.user?.id,
        filesCount: req.files ? (req.files as Express.Multer.File[]).length : 0
      });

      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Document upload failed',
        details: process.env.NODE_ENV === 'development' ? {
          stack: error instanceof Error ? error.stack : undefined
        } : undefined
      });
    }
  }

  // GET /api/deals/:dealId/documents
  static async getDocuments(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { dealId } = req.params;
      const query: DocumentListQuery = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        status: req.query.status as any,
        requisitionItemId: req.query.requisitionItemId as string,
        search: req.query.search as string,
        sortBy: req.query.sortBy as any,
        sortOrder: req.query.sortOrder as any,
      };

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const documentService = new DocumentService();
      const result = await documentService.getDocuments(dealId, req.user.id, query);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Documents retrieved successfully',
        data: result.documents,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Get documents error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Failed to retrieve documents',
      });
    }
  }

  // GET /api/documents/:documentId
  static async getDocumentById(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { documentId } = req.params;

      if (!documentId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Document ID is required',
        });
      }

      const documentService = new DocumentService();
      const document = await documentService.getDocumentById(documentId, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Document retrieved successfully',
        data: document,
      });
    } catch (error) {
      console.error('Get document error:', error);
      const statusCode = error instanceof Error && error.message.includes('not found')
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: statusCode === HTTP_STATUS.NOT_FOUND ? 'DOCUMENT_NOT_FOUND' : ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Failed to retrieve document',
      });
    }
  }

  // GET /api/documents/:documentId/download
  static async downloadDocument(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { documentId } = req.params;

      if (!documentId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Document ID is required',
        });
      }

      const documentService = new DocumentService();
      const { buffer, filename, mimeType } = await documentService.downloadDocument(
        documentId,
        req.user.id
      );

      res.setHeader('Content-Type', mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', buffer.length);

      res.send(buffer);
    } catch (error) {
      console.error('Download document error:', error);
      const statusCode = error instanceof Error && error.message.includes('not found')
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: statusCode === HTTP_STATUS.NOT_FOUND ? 'DOCUMENT_NOT_FOUND' : ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Failed to download document',
      });
    }
  }

  // PUT /api/documents/:documentId/status
  static async updateDocumentStatus(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { documentId } = req.params;
      const updateData: UpdateDocumentStatusRequest = req.body;

      if (!documentId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Document ID is required',
        });
      }

      if (!updateData.status) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Status is required',
        });
      }

      const documentService = new DocumentService();
      const document = await documentService.updateDocumentStatus(
        documentId,
        req.user.id,
        updateData
      );

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Document status updated successfully',
        data: document,
      });
    } catch (error) {
      console.error('Update document status error:', error);
      const statusCode = error instanceof Error && error.message.includes('not found')
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: statusCode === HTTP_STATUS.NOT_FOUND ? 'DOCUMENT_NOT_FOUND' : ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Failed to update document status',
      });
    }
  }

  // DELETE /api/documents/:documentId
  static async deleteDocument(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { documentId } = req.params;

      if (!documentId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Document ID is required',
        });
      }

      const documentService = new DocumentService();
      await documentService.deleteDocument(documentId, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Document deleted successfully',
      });
    } catch (error) {
      console.error('Delete document error:', error);
      const statusCode = error instanceof Error && error.message.includes('not found')
        ? HTTP_STATUS.NOT_FOUND
        : HTTP_STATUS.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        error: statusCode === HTTP_STATUS.NOT_FOUND ? 'DOCUMENT_NOT_FOUND' : ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Failed to delete document',
      });
    }
  }

  // GET /api/deals/:dealId/documents/stats
  static async getDocumentStats(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'Authentication required',
        });
      }

      const { dealId } = req.params;

      if (!dealId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json({
          error: ERROR_MESSAGES.VALIDATION_ERROR,
          message: 'Deal ID is required',
        });
      }

      const documentService = new DocumentService();
      const stats = await documentService.getDocumentStats(dealId, req.user.id);

      res.status(HTTP_STATUS.OK).json({
        success: true,
        message: 'Document statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      console.error('Get document stats error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        error: ERROR_MESSAGES.INTERNAL_ERROR,
        message: error instanceof Error ? error.message : 'Failed to retrieve document statistics',
      });
    }
  }
}
