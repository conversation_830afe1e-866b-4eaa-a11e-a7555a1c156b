import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { Deal<PERSON>ontroller } from '../controllers/dealController';
import { authenticate } from '../middlewares/auth';
import { HTTP_STATUS, ERROR_MESSAGES } from '../constants';
import { DealType, DealStatus, PartyType, DateType } from '@prisma/client';

const router = Router();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json({
      error: ERROR_MESSAGES.VALIDATION_ERROR,
      message: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Validation schemas
const createDealValidation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('type')
    .isIn(Object.values(DealType))
    .withMessage('Invalid deal type'),
  body('value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Value must be a positive number'),
  body('jurisdiction')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Jurisdiction must be less than 100 characters'),
  body('buyer')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Buyer must be less than 200 characters'),
  body('seller')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Seller must be less than 200 characters'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('expectedSigningDate')
    .optional()
    .isISO8601()
    .withMessage('Expected signing date must be a valid ISO 8601 date'),
  body('expectedClosingDate')
    .optional()
    .isISO8601()
    .withMessage('Expected closing date must be a valid ISO 8601 date'),
  body('assignedToId')
    .optional()
    .isString()
    .withMessage('Assigned user ID must be a string'),
  body('parties')
    .optional()
    .isArray()
    .withMessage('Parties must be an array'),
  body('parties.*.partyType')
    .optional()
    .isIn(Object.values(PartyType))
    .withMessage('Invalid party type'),
  body('parties.*.name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Party name is required and must be less than 200 characters'),
  body('dates')
    .optional()
    .isArray()
    .withMessage('Dates must be an array'),
  body('dates.*.dateType')
    .optional()
    .isIn(Object.values(DateType))
    .withMessage('Invalid date type'),
  body('dates.*.dateValue')
    .optional()
    .isISO8601()
    .withMessage('Date value must be a valid ISO 8601 date'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must be less than 1000 characters'),
];

const updateDealValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('type')
    .optional()
    .isIn(Object.values(DealType))
    .withMessage('Invalid deal type'),
  body('value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Value must be a positive number'),
  body('jurisdiction')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Jurisdiction must be less than 100 characters'),
  body('buyer')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Buyer must be less than 200 characters'),
  body('seller')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Seller must be less than 200 characters'),
  body('status')
    .optional()
    .isIn(Object.values(DealStatus))
    .withMessage('Invalid deal status'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('expectedSigningDate')
    .optional()
    .isISO8601()
    .withMessage('Expected signing date must be a valid ISO 8601 date'),
  body('expectedClosingDate')
    .optional()
    .isISO8601()
    .withMessage('Expected closing date must be a valid ISO 8601 date'),
  body('actualClosingDate')
    .optional()
    .isISO8601()
    .withMessage('Actual closing date must be a valid ISO 8601 date'),
  body('progress')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Progress must be between 0 and 100'),
  body('assignedToId')
    .optional()
    .isString()
    .withMessage('Assigned user ID must be a string'),
];

const dealListValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Search query must be less than 200 characters'),
  query('status')
    .optional()
    .isIn(Object.values(DealStatus))
    .withMessage('Invalid deal status'),
  query('type')
    .optional()
    .isIn(Object.values(DealType))
    .withMessage('Invalid deal type'),
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'updatedAt', 'title', 'value', 'progress'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
];

const dealIdValidation = [
  param('id')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Deal ID is required'),
];

const assignDealValidation = [
  body('assignedToId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Assigned user ID is required'),
  body('note')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Note must be less than 500 characters'),
];

const updateStatusValidation = [
  body('status')
    .isIn(Object.values(DealStatus))
    .withMessage('Valid status is required'),
  body('note')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Note must be less than 500 characters'),
];

const addNoteValidation = [
  body('note')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Note is required and must be less than 1000 characters'),
];

// Routes (all routes require authentication)

// GET /api/deals/stats - Get deal statistics (must be before /:id route)
router.get('/stats', authenticate, DealController.getDealStats);

// GET /api/deals - Get deals list with filtering and pagination
router.get('/', authenticate, dealListValidation, handleValidationErrors, DealController.getDeals);

// POST /api/deals - Create new deal
router.post('/', authenticate, createDealValidation, handleValidationErrors, DealController.createDeal);

// GET /api/deals/:id - Get deal by ID
router.get('/:id', authenticate, dealIdValidation, handleValidationErrors, DealController.getDealById);

// PUT /api/deals/:id - Update deal
router.put('/:id', authenticate, dealIdValidation, updateDealValidation, handleValidationErrors, DealController.updateDeal);

// DELETE /api/deals/:id - Delete deal
router.delete('/:id', authenticate, dealIdValidation, handleValidationErrors, DealController.deleteDeal);

// POST /api/deals/:id/assign - Assign deal to user
router.post('/:id/assign', authenticate, dealIdValidation, assignDealValidation, handleValidationErrors, DealController.assignDeal);

// PUT /api/deals/:id/status - Update deal status
router.put('/:id/status', authenticate, dealIdValidation, updateStatusValidation, handleValidationErrors, DealController.updateDealStatus);

// POST /api/deals/:id/submit-documents - Submit documents for AI review
router.post('/:id/submit-documents', authenticate, dealIdValidation, handleValidationErrors, DealController.submitDocumentsForReview);

// POST /api/deals/:id/notes - Add note to deal
router.post('/:id/notes', authenticate, dealIdValidation, addNoteValidation, handleValidationErrors, DealController.addDealNote);

export default router;
