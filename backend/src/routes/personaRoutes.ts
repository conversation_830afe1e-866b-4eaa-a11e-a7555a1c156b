import { Router } from 'express';
import { PersonaController } from '../controllers/personaController';
import { authenticate } from '../middlewares/auth';

const router = Router();

// Get all available personas
router.get('/personas', PersonaController.getAllPersonas);

// Get persona definitions
router.get('/personas/definitions', PersonaController.getPersonaDefinitions);

// Get user's selected personas (requires authentication)
router.get('/user/selected-personas', authenticate, PersonaController.getUserSelectedPersonas);

// Update user's selected personas (requires authentication)
router.put('/user/selected-personas', authenticate, PersonaController.updateUserSelectedPersonas);

export default router;
