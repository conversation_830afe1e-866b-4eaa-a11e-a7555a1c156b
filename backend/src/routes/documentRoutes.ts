import { Router } from 'express';
import multer from 'multer';
import { body, param, query } from 'express-validator';
import { DocumentController } from '../controllers/documentController';
import { authenticate } from '../middlewares/auth';
import { handleValidationErrors } from '../utils';
import { FILE_UPLOAD } from '../constants';
import { DocumentStatus } from '@prisma/client';

const router = Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: FILE_UPLOAD.MAX_SIZE,
    files: 10, // Maximum 10 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Check MIME type
    if (FILE_UPLOAD.ALLOWED_TYPES.includes(file.mimetype as any)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} is not allowed`));
    }
  },
});

// Validation middleware
const dealIdValidation = [
  param('dealId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Deal ID is required'),
];

const documentIdValidation = [
  param('documentId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Document ID is required'),
];

const uploadDocumentValidation = [
  ...dealIdValidation,
  body('requisitionItemId')
    .optional()
    .isString()
    .withMessage('Requisition item ID must be a string'),
  body('remarks')
    .optional()
    .isString()
    .isLength({ max: 1000 })
    .withMessage('Remarks must be a string with maximum 1000 characters'),
];

const getDocumentsValidation = [
  ...dealIdValidation,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(Object.values(DocumentStatus))
    .withMessage('Invalid status value'),
  query('requisitionItemId')
    .optional()
    .isString()
    .withMessage('Requisition item ID must be a string'),
  query('search')
    .optional()
    .isString()
    .isLength({ max: 100 })
    .withMessage('Search query must be a string with maximum 100 characters'),
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'filename', 'status', 'fileSize'])
    .withMessage('Invalid sortBy value'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

const updateDocumentStatusValidation = [
  ...documentIdValidation,
  body('status')
    .isIn(Object.values(DocumentStatus))
    .withMessage('Invalid status value'),
  body('remarks')
    .optional()
    .isString()
    .isLength({ max: 1000 })
    .withMessage('Remarks must be a string with maximum 1000 characters'),
];

// Error handler for multer
const handleMulterError = (error: any, req: any, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'FILE_TOO_LARGE',
        message: `File size exceeds maximum allowed size of ${Math.round(FILE_UPLOAD.MAX_SIZE / (1024 * 1024))}MB`,
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'TOO_MANY_FILES',
        message: 'Maximum 10 files allowed per upload',
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'UNEXPECTED_FILE',
        message: 'Unexpected file field',
      });
    }
  }

  if (error.message && error.message.includes('File type')) {
    return res.status(400).json({
      error: 'INVALID_FILE_TYPE',
      message: error.message,
    });
  }

  next(error);
};

// Routes (all routes require authentication)

// POST /api/deals/:dealId/documents/upload - Upload documents
router.post(
  '/deals/:dealId/documents/upload',
  authenticate,
  upload.array('files', 10),
  handleMulterError,
  uploadDocumentValidation,
  handleValidationErrors,
  DocumentController.uploadDocuments
);

// GET /api/deals/:dealId/documents - Get documents for a deal
router.get(
  '/deals/:dealId/documents',
  authenticate,
  getDocumentsValidation,
  handleValidationErrors,
  DocumentController.getDocuments
);

// GET /api/deals/:dealId/documents/stats - Get document statistics for a deal
router.get(
  '/deals/:dealId/documents/stats',
  authenticate,
  dealIdValidation,
  handleValidationErrors,
  DocumentController.getDocumentStats
);

// GET /api/documents/:documentId - Get document by ID
router.get(
  '/documents/:documentId',
  authenticate,
  documentIdValidation,
  handleValidationErrors,
  DocumentController.getDocumentById
);

// GET /api/documents/:documentId/download - Download document
router.get(
  '/documents/:documentId/download',
  authenticate,
  documentIdValidation,
  handleValidationErrors,
  DocumentController.downloadDocument
);

// PUT /api/documents/:documentId/status - Update document status
router.put(
  '/documents/:documentId/status',
  authenticate,
  updateDocumentStatusValidation,
  handleValidationErrors,
  DocumentController.updateDocumentStatus
);

// DELETE /api/documents/:documentId - Delete document
router.delete(
  '/documents/:documentId',
  authenticate,
  documentIdValidation,
  handleValidationErrors,
  DocumentController.deleteDocument
);

export default router;
