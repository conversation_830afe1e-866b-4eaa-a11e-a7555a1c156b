import express from 'express';
import { body, param } from 'express-validator';
import { authenticate } from '../middlewares/auth';
import { handleValidationErrors } from '../middlewares/validation';
import { ReportController } from '../controllers/reportController';

const router = express.Router();

// Validation middleware
const dealIdValidation = [
  param('dealId')
    .isString()
    .notEmpty()
    .withMessage('Deal ID must be a valid string')
];

const reportIdValidation = [
  param('reportId')
    .isString()
    .notEmpty()
    .withMessage('Report ID must be a valid string')
];

const generateReportValidation = [
  body('reportType')
    .optional()
    .isIn(['EXECUTIVE_SUMMARY', 'DETAILED_ANALYSIS', 'RISK_ASSESSMENT', 'COMPLIANCE_REPORT', 'FINANCIAL_ANALYSIS', 'LEGAL_REVIEW', 'TECHNICAL_ASSESSMENT', 'CUSTOM'])
    .withMessage('Invalid report type')
];

// Routes

// POST /api/deals/:dealId/reports/generate - Generate new report
router.post('/deals/:dealId/reports/generate',
  authenticate,
  dealIdValidation,
  generateReportValidation,
  handleValidationErrors,
  ReportController.generateReport
);

// GET /api/deals/:dealId/reports - Get all reports for a deal
router.get('/deals/:dealId/reports',
  authenticate,
  dealIdValidation,
  handleValidationErrors,
  ReportController.getReports
);

// GET /api/reports/:reportId - Get specific report
router.get('/reports/:reportId',
  authenticate,
  reportIdValidation,
  handleValidationErrors,
  ReportController.getReportById
);

// GET /api/reports/:reportId/status - Get report generation status
router.get('/reports/:reportId/status',
  authenticate,
  reportIdValidation,
  handleValidationErrors,
  ReportController.getReportStatus
);

// GET /api/reports/:reportId/download - Download report as PDF
router.get('/reports/:reportId/download',
  authenticate,
  reportIdValidation,
  handleValidationErrors,
  ReportController.downloadReport
);

export default router;
