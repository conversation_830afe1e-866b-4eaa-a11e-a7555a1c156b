import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { RequisitionController } from '../controllers/requisitionController';
import { authenticate } from '../middlewares/auth';
import { HTTP_STATUS, ERROR_MESSAGES } from '../constants';
import { DealType } from '@prisma/client';

const router = Router();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json({
      error: ERROR_MESSAGES.VALIDATION_ERROR,
      message: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Validation schemas
const templateListValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('dealType')
    .optional()
    .isIn(Object.values(DealType))
    .withMessage('Invalid deal type'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Search query must be less than 200 characters'),
  query('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
];

const createTemplateValidation = [
  body('name')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Name must be between 3 and 200 characters'),
  body('dealType')
    .isIn(Object.values(DealType))
    .withMessage('Valid deal type is required'),
  body('industry')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Industry must be less than 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
  body('categories')
    .isArray({ min: 1 })
    .withMessage('At least one category is required'),
  body('categories.*.name')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Category name is required and must be less than 200 characters'),
  body('categories.*.orderIndex')
    .isInt({ min: 0 })
    .withMessage('Category order index must be a non-negative integer'),
  body('categories.*.isRequired')
    .isBoolean()
    .withMessage('Category isRequired must be a boolean'),
  body('categories.*.items')
    .isArray({ min: 1 })
    .withMessage('Each category must have at least one item'),
  body('categories.*.items.*.name')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Item name is required and must be less than 200 characters'),
  body('categories.*.items.*.orderIndex')
    .isInt({ min: 0 })
    .withMessage('Item order index must be a non-negative integer'),
  body('categories.*.items.*.isRequired')
    .isBoolean()
    .withMessage('Item isRequired must be a boolean'),
];

const dealIdValidation = [
  param('dealId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Deal ID is required'),
];

const templateIdValidation = [
  param('id')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Template ID is required'),
];

const createDealRequisitionValidation = [
  body('dealId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Deal ID is required'),
  body('templateId')
    .isString()
    .isLength({ min: 1 })
    .withMessage('Template ID is required'),
  body('customData')
    .optional()
    .isObject()
    .withMessage('Custom data must be an object'),
];

const updateDealRequisitionValidation = [
  body('customData')
    .isObject()
    .withMessage('Custom data is required and must be an object'),
  body('customData.categories')
    .isArray()
    .withMessage('Categories must be an array'),
  body('customData.metadata')
    .isObject()
    .withMessage('Metadata is required'),
  body('isFinalized')
    .optional()
    .isBoolean()
    .withMessage('isFinalized must be a boolean'),
];

// Routes (all routes require authentication)

// GET /api/requisitions/templates - Get requisition templates
router.get('/templates', 
  authenticate, 
  templateListValidation, 
  handleValidationErrors, 
  RequisitionController.getRequisitionTemplates
);

// GET /api/requisitions/templates/:id - Get requisition template by ID
router.get('/templates/:id', 
  authenticate, 
  templateIdValidation, 
  handleValidationErrors, 
  RequisitionController.getRequisitionTemplateById
);

// POST /api/requisitions/templates - Create requisition template
router.post('/templates', 
  authenticate, 
  createTemplateValidation, 
  handleValidationErrors, 
  RequisitionController.createRequisitionTemplate
);

// GET /api/requisitions/deals/:dealId - Get deal requisition
router.get('/deals/:dealId', 
  authenticate, 
  dealIdValidation, 
  handleValidationErrors, 
  RequisitionController.getDealRequisition
);

// POST /api/requisitions/deals - Create deal requisition
router.post('/deals', 
  authenticate, 
  createDealRequisitionValidation, 
  handleValidationErrors, 
  RequisitionController.createDealRequisition
);

// PUT /api/requisitions/deals/:dealId - Update deal requisition
router.put('/deals/:dealId', 
  authenticate, 
  dealIdValidation, 
  updateDealRequisitionValidation, 
  handleValidationErrors, 
  RequisitionController.updateDealRequisition
);

// GET /api/requisitions/deals/:dealId/progress - Get deal requisition progress
router.get('/deals/:dealId/progress', 
  authenticate, 
  dealIdValidation, 
  handleValidationErrors, 
  RequisitionController.getDealRequisitionProgress
);

export default router;
