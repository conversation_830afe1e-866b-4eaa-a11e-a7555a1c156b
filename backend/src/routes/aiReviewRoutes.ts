import express from 'express';
import { body, param } from 'express-validator';
import { authenticate } from '../middlewares/auth';
import { handleValidationErrors } from '../middlewares/validation';
import { AIReviewController } from '../controllers/aiReviewController';

const router = express.Router();

// Validation middleware
const dealIdValidation = [
  param('dealId')
    .isString()
    .notEmpty()
    .withMessage('Deal ID must be a valid string')
];

const startReviewValidation = [
  body('dealId')
    .isString()
    .notEmpty()
    .withMessage('Deal ID is required')
];

// Routes

// POST /api/ai-review/start - Start AI review for a deal
router.post('/start',
  authenticate,
  startReviewValidation,
  handleValidationErrors,
  AIReviewController.startReview
);

// GET /api/ai-review/results/:dealId - Get AI review results with progress and status (public for testing)
router.get('/results/:dealId',
  dealIdValidation,
  handleValidationErrors,
  AIReviewController.getResults
);

// GET /api/ai-review/personas - Get all AI personas (public for testing)
router.get('/personas',
  AIReviewController.getPersonas
);

// POST /api/ai-review/test-connection - Test Gemini API connection (public for testing)
router.post('/test-connection',
  AIReviewController.testConnection
);

// POST /api/ai-review/initialize-personas - Initialize default personas
router.post('/initialize-personas',
  authenticate,
  AIReviewController.initializePersonas
);

// POST /api/ai-review/reset/:dealId - Reset stuck AI reviews (public for testing)
router.post('/reset/:dealId',
  dealIdValidation,
  handleValidationErrors,
  AIReviewController.resetReview
);

export default router;
