import { DealType, DealStatus, PartyType, DateType } from "@prisma/client";

// Deal interfaces
export interface CreateDealSupplierRequest {
  name: string;
  jurisdiction: string;
  contactInfo?: any;
  description?: string;
}

export interface CreateDealRequest {
  title: string;
  type: DealType;
  currency?: string;
  value?: number;
  jurisdiction?: string;
  buyer?: string;
  seller?: string;
  suppliers?: CreateDealSupplierRequest[];
  startDate?: string;
  expectedSigningDate?: string;
  expectedClosingDate?: string;
  assignedToId?: string;
  parties?: CreateDealPartyRequest[];
  dates?: CreateDealDateRequest[];
  notes?: string;
  // Additional fields from frontend
  industry?: string;
  description?: string;
  riskLevel?: "LOW" | "MEDIUM" | "HIGH";
  confidentialityLevel?: "PUBLIC" | "INTERNAL" | "CONFIDENTIAL" | "RESTRICTED";
  // Requisition setup
  createRequisition?: boolean;
  createDefaultRequisition?: boolean;
  requisitionTemplateId?: string;
  customRequisitionData?: {
    categories: Array<{
      id: string;
      title: string;
      items: string[];
    }>;
  };
}

export interface UpdateDealRequest {
  title?: string;
  type?: DealType;
  currency?: string;
  value?: number;
  jurisdiction?: string;
  buyer?: string;
  seller?: string;
  status?: DealStatus;
  startDate?: string;
  expectedSigningDate?: string;
  expectedClosingDate?: string;
  actualClosingDate?: string;
  progress?: number;
  assignedToId?: string;
}

export interface CreateDealPartyRequest {
  partyType: PartyType;
  name: string;
  role?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
    contactPerson?: string;
  };
}

export interface CreateDealDateRequest {
  dateType: DateType;
  dateValue: string;
  description?: string;
}

export interface DealResponse {
  id: string;
  title: string;
  type: DealType;
  currency?: string;
  value?: number;
  jurisdiction?: string;
  buyer?: string;
  seller?: string;
  status: DealStatus;
  startDate?: string;
  expectedSigningDate?: string;
  expectedClosingDate?: string;
  actualClosingDate?: string;
  progress: number;
  industry?: string;
  description?: string;
  riskLevel?: "LOW" | "MEDIUM" | "HIGH";
  confidentialityLevel?: "PUBLIC" | "INTERNAL" | "CONFIDENTIAL" | "RESTRICTED";
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  createdById: string;
  assignedToId?: string;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  organization: {
    id: string;
    name: string;
  };
  parties: DealPartyResponse[];
  suppliers: DealSupplierResponse[];
  dates: DealDateResponse[];
  notes: DealNoteResponse[];
  _count?: {
    documents: number;
    reviews: number;
    comments: number;
  };
}

export interface DealPartyResponse {
  id: string;
  partyType: PartyType;
  name: string;
  role?: string;
  contactInfo?: any;
  createdAt: string;
}

export interface DealSupplierResponse {
  id: string;
  name: string;
  jurisdiction: string;
  contactInfo?: any;
  description?: string;
  createdAt: string;
}

export interface DealDateResponse {
  id: string;
  dateType: DateType;
  dateValue: string;
  description?: string;
  createdAt: string;
}

export interface DealNoteResponse {
  id: string;
  note: string;
  createdAt: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface DealListQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: DealStatus;
  type?: DealType;
  assignedToId?: string;
  createdById?: string;
  organizationId?: string;
  sortBy?: "createdAt" | "updatedAt" | "title" | "value" | "progress";
  sortOrder?: "asc" | "desc";
  startDate?: string;
  endDate?: string;
}

export interface DealListResponse {
  deals: DealResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    status?: DealStatus;
    type?: DealType;
    assignedToId?: string;
    createdById?: string;
  };
}

export interface DealAssignmentRequest {
  assignedToId: string;
  note?: string;
}

export interface DealStatusUpdateRequest {
  status: DealStatus;
  note?: string;
}

export interface DealStatsResponse {
  total: number;
  byStatus: Record<DealStatus, number>;
  byType: Record<DealType, number>;
  totalValue: number;
  averageProgress: number;
  recentActivity: number;
}

// Deal validation schemas
export interface DealValidationRules {
  title: {
    required: true;
    minLength: 3;
    maxLength: 200;
  };
  type: {
    required: true;
    enum: DealType[];
  };
  value: {
    min: 0;
    max: 999999999999; // 1 trillion
  };
  jurisdiction: {
    maxLength: 100;
  };
  buyer: {
    maxLength: 200;
  };
  seller: {
    maxLength: 200;
  };
  progress: {
    min: 0;
    max: 100;
  };
}

// Deal permissions
export interface DealPermissions {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canAssign: boolean;
  canChangeStatus: boolean;
  canAddDocuments: boolean;
  canStartReview: boolean;
  canGenerateReports: boolean;
}

// Deal activity log
export interface DealActivity {
  id: string;
  action: string;
  description: string;
  userId: string;
  user: {
    firstName: string;
    lastName: string;
  };
  metadata?: any;
  createdAt: string;
}

export interface AddDealNoteRequest {
  note: string;
}

export interface UpdateDealNoteRequest {
  note: string;
}
