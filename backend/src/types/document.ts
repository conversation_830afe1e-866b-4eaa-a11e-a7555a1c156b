import { DocumentStatus } from '@prisma/client';

// Document interfaces
export interface Document {
  id: string;
  filename: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  status: DocumentStatus;
  version: number;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  dealId: string;
  requisitionItemId?: string;
  uploadedById: string;
  uploadedBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  requisitionItem?: {
    id: string;
    name: string;
    description?: string;
    isRequired: boolean;
    acceptedFormats?: string[];
    maxFileSize?: number;
  };
}

export interface DocumentVersion {
  id: string;
  version: number;
  filePath: string;
  fileSize: number;
  createdAt: string;
  documentId: string;
  uploadedById: string;
  uploadedBy: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

// Request interfaces
export interface UploadDocumentRequest {
  dealId: string;
  requisitionItemId?: string;
  files: Express.Multer.File[];
  remarks?: string;
}

export interface UpdateDocumentStatusRequest {
  status: DocumentStatus;
  remarks?: string;
}

export interface CreateDocumentVersionRequest {
  file: Express.Multer.File;
  remarks?: string;
}

// Response interfaces
export interface DocumentResponse {
  id: string;
  filename: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  status: DocumentStatus;
  version: number;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  dealId: string;
  requisitionItemId?: string;
  templateItemId?: string; // Frontend template item ID for matching
  uploadedBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  requisitionItem?: {
    id: string;
    name: string;
    description?: string;
    isRequired: boolean;
  };
  downloadUrl?: string;
}

export interface UploadDocumentResponse {
  success: boolean;
  message: string;
  documents: DocumentResponse[];
  failed?: {
    filename: string;
    error: string;
  }[];
}

export interface DocumentListResponse {
  documents: DocumentResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Query interfaces
export interface DocumentListQuery {
  page?: number;
  limit?: number;
  status?: DocumentStatus;
  requisitionItemId?: string;
  search?: string;
  sortBy?: 'createdAt' | 'filename' | 'status' | 'fileSize';
  sortOrder?: 'asc' | 'desc';
}

// File storage interfaces
export interface FileStorageConfig {
  type: 'local' | 's3' | 'azure';
  local?: {
    uploadPath: string;
  };
  aws?: {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucket: string;
  };
}

export interface StoredFile {
  filename: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  fileInfo?: {
    size: number;
    mimeType: string;
    extension: string;
  };
}

// Document statistics
export interface DocumentStats {
  totalDocuments: number;
  documentsByStatus: {
    [key in DocumentStatus]: number;
  };
  totalFileSize: number;
  averageFileSize: number;
  documentsByRequisitionItem: {
    itemId: string;
    itemName: string;
    documentCount: number;
    isRequired: boolean;
    hasDocuments: boolean;
  }[];
}

// Audit trail
export interface DocumentAuditLog {
  id: string;
  documentId: string;
  action: 'UPLOADED' | 'STATUS_CHANGED' | 'VERSION_ADDED' | 'DOWNLOADED' | 'DELETED';
  oldValue?: string;
  newValue?: string;
  userId: string;
  userEmail: string;
  timestamp: string;
  metadata?: Record<string, any>;
}
