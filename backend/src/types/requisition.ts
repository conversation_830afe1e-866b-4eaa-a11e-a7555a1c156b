import { DealType } from '@prisma/client';

// Requisition Template interfaces
export interface RequisitionTemplate {
  id: string;
  name: string;
  dealType: DealType;
  industry?: string;
  description?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  createdById: string;
  categories: RequisitionCategory[];
}

export interface RequisitionCategory {
  id: string;
  name: string;
  description?: string;
  orderIndex: number;
  isRequired: boolean;
  templateId: string;
  items: RequisitionItem[];
}

export interface RequisitionItem {
  id: string;
  name: string;
  description?: string;
  isRequired: boolean;
  orderIndex: number;
  categoryId: string;
  acceptedFormats?: string[];
  maxFileSize?: number;
  examples?: string[];
}

// Deal-specific requisition interfaces
export interface DealRequisition {
  id: string;
  dealId: string;
  templateId: string;
  userId: string;
  customData: RequisitionCustomData;
  isFinalized: boolean;
  createdAt: string;
  updatedAt: string;
  template: RequisitionTemplate;
  deal: {
    id: string;
    title: string;
    type: DealType;
  };
}

export interface RequisitionCustomData {
  categories: CustomRequisitionCategory[];
  metadata: {
    lastModified: string;
    modifiedBy: string;
    version: number;
    notes?: string;
  };
}

export interface CustomRequisitionCategory {
  id: string;
  name: string;
  description?: string;
  orderIndex: number;
  isRequired: boolean;
  isCustom: boolean; // true if user added this category
  items: CustomRequisitionItem[];
}

export interface CustomRequisitionItem {
  id: string;
  name: string;
  description?: string;
  isRequired: boolean;
  orderIndex: number;
  isCustom: boolean; // true if user added this item
  acceptedFormats?: string[];
  maxFileSize?: number;
  examples?: string[];
  status: 'pending' | 'uploaded' | 'approved' | 'rejected';
  uploadedDocuments?: {
    id: string;
    filename: string;
    uploadedAt: string;
  }[];
}

// Request/Response interfaces
export interface CreateRequisitionTemplateRequest {
  name: string;
  dealType: DealType;
  industry?: string;
  description?: string;
  isDefault?: boolean;
  categories: CreateRequisitionCategoryRequest[];
}

export interface CreateRequisitionCategoryRequest {
  name: string;
  description?: string;
  orderIndex: number;
  isRequired: boolean;
  items: CreateRequisitionItemRequest[];
}

export interface CreateRequisitionItemRequest {
  name: string;
  description?: string;
  isRequired: boolean;
  orderIndex: number;
  acceptedFormats?: string[];
  maxFileSize?: number;
  examples?: string[];
}

export interface UpdateDealRequisitionRequest {
  customData: RequisitionCustomData;
  isFinalized?: boolean;
}

export interface CreateDealRequisitionRequest {
  dealId: string;
  templateId: string;
  customData?: Partial<RequisitionCustomData>;
}

export interface RequisitionTemplateListQuery {
  page?: number;
  limit?: number;
  dealType?: DealType;
  industry?: string;
  search?: string;
  isDefault?: boolean;
}

export interface RequisitionTemplateListResponse {
  templates: RequisitionTemplate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Default template data structure
export type DefaultTemplateData = {
  [key in DealType]: {
    name: string;
    description: string;
    categories: {
      name: string;
      description: string;
      orderIndex: number;
      isRequired: boolean;
      items: {
        name: string;
        description?: string;
        isRequired: boolean;
        orderIndex: number;
        acceptedFormats?: string[];
        maxFileSize?: number;
        examples?: string[];
      }[];
    }[];
  };
}

// Validation interfaces
export interface RequisitionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  completionPercentage: number;
  missingRequiredItems: string[];
}

// Statistics interfaces
export interface RequisitionStats {
  totalItems: number;
  completedItems: number;
  pendingItems: number;
  requiredItems: number;
  optionalItems: number;
  completionPercentage: number;
  lastUpdated: string;
}

export interface RequisitionProgress {
  categoryId: string;
  categoryName: string;
  totalItems: number;
  completedItems: number;
  requiredItems: number;
  completionPercentage: number;
  items: {
    id: string;
    name: string;
    isRequired: boolean;
    status: 'pending' | 'uploaded' | 'approved' | 'rejected';
  }[];
}
