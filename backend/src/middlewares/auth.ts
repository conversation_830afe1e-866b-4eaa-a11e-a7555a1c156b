import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utils';
import { HTTP_STATUS, ERROR_MESSAGES } from '../constants';
import prisma from '../config/database';
import { UserRole } from '@prisma/client';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        organizationId: string;
      };
    }
  }
}

// Authentication middleware
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        message: 'No token provided or invalid format',
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      const decoded = verifyToken(token) as any;

      // Check if user still exists and session is valid
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: { organization: true },
      });

      if (!user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.USER_NOT_FOUND,
          message: 'User no longer exists',
        });
      }

      // Check if session exists and is valid
      const session = await prisma.userSession.findFirst({
        where: {
          userId: user.id,
          token: token,
          expiresAt: { gt: new Date() },
        },
      });

      if (!session) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json({
          error: ERROR_MESSAGES.TOKEN_EXPIRED,
          message: 'Session expired or invalid',
        });
      }

      // Attach user info to request
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        organizationId: user.organizationId,
      };

      next();
    } catch (jwtError) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        error: ERROR_MESSAGES.TOKEN_INVALID,
        message: 'Invalid token',
      });
    }
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
      error: ERROR_MESSAGES.INTERNAL_ERROR,
      message: 'Authentication failed',
    });
  }
};

// Role-based authorization middleware
export const authorize = (allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        message: 'Authentication required',
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(HTTP_STATUS.FORBIDDEN).json({
        error: ERROR_MESSAGES.FORBIDDEN,
        message: 'Insufficient permissions',
      });
    }

    next();
  };
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);

    try {
      const decoded = verifyToken(token) as any;

      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
      });

      if (user) {
        req.user = {
          id: user.id,
          email: user.email,
          role: user.role,
          organizationId: user.organizationId,
        };
      }
    } catch (jwtError) {
      // Ignore JWT errors for optional auth
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    next(); // Continue even if there's an error
  }
};

// Organization access middleware
export const requireOrganizationAccess = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(HTTP_STATUS.UNAUTHORIZED).json({
      error: ERROR_MESSAGES.UNAUTHORIZED,
      message: 'Authentication required',
    });
  }

  // Check if user has access to the organization
  const organizationId = req.params.organizationId || req.body.organizationId;

  if (organizationId && organizationId !== req.user.organizationId) {
    // Only admins can access other organizations
    if (req.user.role !== UserRole.ADMIN) {
      return res.status(HTTP_STATUS.FORBIDDEN).json({
        error: ERROR_MESSAGES.FORBIDDEN,
        message: 'Access to this organization is denied',
      });
    }
  }

  next();
};

// Deal access middleware
export const requireDealAccess = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        message: 'Authentication required',
      });
    }

    const dealId = req.params.dealId || req.params.id;

    if (!dealId) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json({
        error: ERROR_MESSAGES.BAD_REQUEST,
        message: 'Deal ID is required',
      });
    }

    const deal = await prisma.deal.findUnique({
      where: { id: dealId },
      select: {
        id: true,
        organizationId: true,
        createdById: true,
        assignedToId: true
      },
    });

    if (!deal) {
      return res.status(HTTP_STATUS.NOT_FOUND).json({
        error: ERROR_MESSAGES.DEAL_NOT_FOUND,
        message: 'Deal not found',
      });
    }

    // Check access permissions
    const hasAccess =
      deal.organizationId === req.user.organizationId && (
        req.user.role === UserRole.ADMIN ||
        deal.createdById === req.user.id ||
        deal.assignedToId === req.user.id ||
        req.user.role === UserRole.SENIOR_ANALYST
      );

    if (!hasAccess) {
      return res.status(HTTP_STATUS.FORBIDDEN).json({
        error: ERROR_MESSAGES.DEAL_ACCESS_DENIED,
        message: 'Access to this deal is denied',
      });
    }

    next();
  } catch (error) {
    console.error('Deal access middleware error:', error);
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
      error: ERROR_MESSAGES.INTERNAL_ERROR,
      message: 'Failed to verify deal access',
    });
  }
};
