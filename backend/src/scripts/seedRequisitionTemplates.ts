import { PrismaClient, DealType } from '@prisma/client';
import { DEFAULT_REQUISITION_TEMPLATES } from '../data/defaultRequisitionTemplates';

const prisma = new PrismaClient();

async function seedRequisitionTemplates() {
  console.log('🌱 Seeding default requisition templates...');

  try {
    // Create a system user for default templates if it doesn't exist
    let systemUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
    });

    if (!systemUser) {
      // Create system organization first
      const systemOrg = await prisma.organization.create({
        data: {
          name: 'System',
          domain: 'system.duediligence.com',
          settings: {},
        },
      });

      systemUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'System',
          lastName: 'User',
          passwordHash: 'system', // This won't be used for login
          role: 'ADMIN',
          organizationId: systemOrg.id,
          emailVerified: true,
        },
      });
    }

    // Seed templates for each deal type
    for (const [dealType, templateData] of Object.entries(DEFAULT_REQUISITION_TEMPLATES)) {
      console.log(`📋 Creating template for ${dealType}...`);

      // Check if template already exists
      const existingTemplate = await prisma.requisitionTemplate.findFirst({
        where: {
          dealType: dealType as DealType,
          isActive: true,
        },
      });

      if (existingTemplate) {
        console.log(`⏭️  Template for ${dealType} already exists, skipping...`);
        continue;
      }

      // Create the template
      const template = await prisma.requisitionTemplate.create({
        data: {
          name: templateData.name,
          dealType: dealType as DealType,
          description: templateData.description,
          isActive: true, // Using isActive instead of isDefault
          createdById: systemUser.id,
          categories: {
            create: templateData.categories.map((category: any) => ({
              name: category.name,
              description: category.description,
              orderIndex: category.orderIndex,
              isRequired: category.isRequired,
              items: {
                create: category.items.map((item: any) => ({
                  name: item.name,
                  description: item.description,
                  isRequired: item.isRequired,
                  orderIndex: item.orderIndex,
                  acceptedFormats: item.acceptedFormats || [],
                  maxFileSize: item.maxFileSize,
                  examples: item.examples || [],
                })),
              },
            })),
          },
        },
        include: {
          categories: {
            include: {
              items: true,
            },
          },
        },
      });

      console.log(`✅ Created template: ${template.name} with ${template.categories.length} categories`);
    }

    console.log('🎉 Default requisition templates seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding requisition templates:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder
if (require.main === module) {
  seedRequisitionTemplates()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedRequisitionTemplates };
