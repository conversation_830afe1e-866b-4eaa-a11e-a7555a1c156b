import { PrismaClient } from '@prisma/client';
import { AIPersonaService } from '../services/aiPersonaService';

const prisma = new PrismaClient();

async function initializePersonas() {
  try {
    console.log('🤖 Initializing AI personas...');
    
    await AIPersonaService.initializeDefaultPersonas();
    
    console.log('✅ AI personas initialized successfully!');
    
    // List the created personas
    const personas = await prisma.aIPersona.findMany({
      select: {
        name: true,
        type: true,
        isActive: true
      },
      orderBy: { name: 'asc' }
    });
    
    console.log('\n📊 Created personas:');
    personas.forEach(persona => {
      console.log(`- ${persona.name} (${persona.type}) - ${persona.isActive ? 'Active' : 'Inactive'}`);
    });
    
  } catch (error) {
    console.error('❌ Error initializing personas:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

initializePersonas();
