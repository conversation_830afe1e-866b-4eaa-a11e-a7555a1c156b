import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migratePersonas() {
  console.log('Starting persona migration...');

  try {
    // First, delete all existing AI reviews and personas to avoid conflicts
    console.log('Cleaning up existing AI reviews...');
    await prisma.aIReview.deleteMany({});
    
    console.log('Cleaning up existing AI personas...');
    await prisma.aIPersona.deleteMany({});
    
    console.log('Cleanup completed. Database is ready for new persona types.');
    
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

migratePersonas()
  .then(() => {
    console.log('Migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
