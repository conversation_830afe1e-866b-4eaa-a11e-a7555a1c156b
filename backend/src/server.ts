import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import rateLimit from "express-rate-limit";

import config from './config';
import { connectDatabase, disconnectDatabase } from './config/database';
import { HTTP_STATUS, ERROR_MESSAGES } from './constants';
import authRoutes from './routes/authRoutes';
import dealRoutes from './routes/dealRoutes';
import requisitionRoutes from './routes/requisitionRoutes';
import documentRoutes from './routes/documentRoutes';
import aiReviewRoutes from './routes/aiReviewRoutes';
import reportRoutes from './routes/reportRoutes';
import personaRoutes from './routes/personaRoutes';
import { loggers } from './config/logger';

const app = express();

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimit.windowMs,
  max: config.security.rateLimit.maxRequests,
  message: {
    error: ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,
  },
});

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: [config.app.frontendUrl, "http://localhost:8081"],
    credentials: true,
  })
);

if (config.logging.enableRequestLogging) {
  app.use(morgan("combined"));
}

app.use(limiter);
app.use(express.json({ limit: config.app.uploadMaxSize }));
app.use(
  express.urlencoded({ extended: true, limit: config.app.uploadMaxSize })
);

// Health check route
app.get("/api/v1/health", (_req: Request, res: Response) => {
  res.json({
    status: "OK",
    message: "Due Diligence Nexus Platform API is running",
    timestamp: new Date().toISOString(),
    environment: config.nodeEnv,
    version: "v1",
  });
});

// API info route
app.get("/", (_req: Request, res: Response) => {
  res.json({
    name: "Due Diligence Nexus Platform API",
    version: "1.0.0",
    description:
      "AI-powered due diligence platform for M&A and investment processes",
    apiVersion: "v1",
    endpoints: {
      health: '/api/v1/health',
      auth: '/api/v1/auth',
      deals: '/api/v1/deals',
      documents: '/api/v1/documents',
      reviews: '/api/v1/reviews',
      reports: '/api/v1/reports',
      users: '/api/v1/users',
      aiReview: '/api/v1/ai-review',
      personas: '/api/v1/personas',
    },
  });
});

// API Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/deals', dealRoutes);
app.use('/api/v1/requisitions', requisitionRoutes);
app.use('/api/v1', documentRoutes);
app.use('/api/v1/ai-review', aiReviewRoutes);
app.use('/api/v1', reportRoutes);
app.use('/api/v1', personaRoutes);

// TODO: Add remaining route handlers
// app.use('/api/v1/reviews', reviewRoutes);
// app.use('/api/v1/users', userRoutes);

// Error handling middleware
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  loggers.server.error(
    { error: err.message, stack: err.stack },
    "Unhandled error"
  );

  const statusCode =
    (err as any).statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR;
  const message = err.message || ERROR_MESSAGES.INTERNAL_ERROR;

  res.status(statusCode).json({
    error: message,
    ...(config.nodeEnv === "development" && { stack: err.stack }),
  });
});

// 404 handler
app.use((_req: Request, res: Response) => {
  res.status(HTTP_STATUS.NOT_FOUND).json({
    error: ERROR_MESSAGES.NOT_FOUND,
    message: "The requested endpoint does not exist",
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal: string) => {
  loggers.server.info({ signal }, "Starting graceful shutdown");

  try {
    await disconnectDatabase();
    loggers.server.info("Graceful shutdown completed");
    process.exit(0);
  } catch (error) {
    loggers.server.error({ error }, "Error during shutdown");
    process.exit(1);
  }
};

process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// Start server
const startServer = async () => {
  try {
    // Connect to database
    await connectDatabase();

    // TODO: Initialize AI personas when user configures them in settings
    // await AIPersonaService.initializeDefaultPersonas();

    // Start HTTP server
    app.listen(config.port, () => {
      loggers.server.info(
        {
          port: config.port,
          environment: config.nodeEnv,
          apiUrl: config.app.url,
          frontendUrl: config.app.frontendUrl,
        },
        "Server started successfully"
      );
    });
  } catch (error) {
    loggers.server.error({ error }, "Failed to start server");
    process.exit(1);
  }
};

startServer();

// Export app for testing
export { app };
