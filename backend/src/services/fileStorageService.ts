import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { FILE_UPLOAD } from '../constants';
import config from '../config';
import { FileStorageConfig, StoredFile, FileValidationResult } from '../types/document';

export class FileStorageService {
  private config: FileStorageConfig;

  constructor() {
    this.config = config.storage;
  }

  /**
   * Validate uploaded file
   */
  validateFile(file: Express.Multer.File, acceptedFormats?: string[], maxFileSize?: number): FileValidationResult {
    try {
      // Check file size
      const maxSize = maxFileSize || FILE_UPLOAD.MAX_SIZE;
      if (file.size > maxSize) {
        return {
          isValid: false,
          error: `File size exceeds maximum allowed size of ${Math.round(maxSize / (1024 * 1024))}MB`,
        };
      }

      // Check MIME type
      if (!FILE_UPLOAD.ALLOWED_TYPES.includes(file.mimetype as any)) {
        return {
          isValid: false,
          error: `File type ${file.mimetype} is not allowed`,
        };
      }

      // Check file extension
      const extension = path.extname(file.originalname).toLowerCase();
      if (!FILE_UPLOAD.ALLOWED_EXTENSIONS.includes(extension as any)) {
        return {
          isValid: false,
          error: `File extension ${extension} is not allowed`,
        };
      }

      // Check accepted formats if specified
      if (acceptedFormats && acceptedFormats.length > 0) {
        const fileExtension = extension.substring(1); // Remove the dot
        if (!acceptedFormats.includes(fileExtension)) {
          return {
            isValid: false,
            error: `File format ${fileExtension} is not accepted for this item. Accepted formats: ${acceptedFormats.join(', ')}`,
          };
        }
      }

      return {
        isValid: true,
        fileInfo: {
          size: file.size,
          mimeType: file.mimetype,
          extension,
        },
      };
    } catch (error) {
      return {
        isValid: false,
        error: 'File validation failed',
      };
    }
  }

  /**
   * Generate unique filename
   */
  generateUniqueFilename(originalName: string, documentId?: string): string {
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    const uniqueId = documentId || uuidv4();
    const timestamp = Date.now();

    // Sanitize filename
    const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9\-_]/g, '_');

    return `${uniqueId}_${timestamp}_${sanitizedBaseName}${extension}`;
  }

  /**
   * Generate file path structure
   */
  generateFilePath(dealId: string, requisitionItemId?: string, filename?: string): string {
    const basePath = 'uploads/deals';

    if (requisitionItemId) {
      return path.join(basePath, dealId, 'items', requisitionItemId, filename || '');
    }

    return path.join(basePath, dealId, 'general', filename || '');
  }

  /**
   * Store file locally
   */
  async storeFileLocally(
    file: Express.Multer.File,
    dealId: string,
    requisitionItemId?: string,
    documentId?: string
  ): Promise<StoredFile> {
    try {
      const filename = this.generateUniqueFilename(file.originalname, documentId);
      const relativePath = this.generateFilePath(dealId, requisitionItemId, filename);
      const uploadDir = path.join(process.cwd(), 'uploads');
      const fullPath = path.join(uploadDir, relativePath);
      const dirPath = path.dirname(fullPath);

      // Ensure directory exists
      await fs.promises.mkdir(dirPath, { recursive: true });

      // Write file
      await fs.promises.writeFile(fullPath, file.buffer);

      return {
        filename,
        originalName: file.originalname,
        filePath: relativePath,
        fileSize: file.size,
        mimeType: file.mimetype,
      };
    } catch (error) {
      throw new Error(`Failed to store file locally: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Store file in S3 (placeholder for future implementation)
   */
  async storeFileInS3(
    file: Express.Multer.File,
    dealId: string,
    requisitionItemId?: string,
    documentId?: string
  ): Promise<StoredFile> {
    // TODO: Implement S3 storage
    throw new Error('S3 storage not implemented yet');
  }

  /**
   * Store file based on configuration
   */
  async storeFile(
    file: Express.Multer.File,
    dealId: string,
    requisitionItemId?: string,
    documentId?: string
  ): Promise<StoredFile> {
    switch (this.config.type) {
      case 'local':
        return this.storeFileLocally(file, dealId, requisitionItemId, documentId);
      case 's3':
        return this.storeFileInS3(file, dealId, requisitionItemId, documentId);
      default:
        throw new Error(`Unsupported storage type: ${this.config.type}`);
    }
  }

  /**
   * Get file from local storage
   */
  async getFileLocally(filePath: string): Promise<Buffer> {
    try {
      const fullPath = path.join(process.cwd(), 'uploads', filePath);

      // Check if file exists
      await fs.promises.access(fullPath, fs.constants.F_OK);

      return await fs.promises.readFile(fullPath);
    } catch (error) {
      throw new Error(`File not found: ${filePath}`);
    }
  }

  /**
   * Get file from S3 (placeholder)
   */
  async getFileFromS3(filePath: string): Promise<Buffer> {
    // TODO: Implement S3 file retrieval
    throw new Error('S3 file retrieval not implemented yet');
  }

  /**
   * Get file based on configuration
   */
  async getFile(filePath: string): Promise<Buffer> {
    switch (this.config.type) {
      case 'local':
        return this.getFileLocally(filePath);
      case 's3':
        return this.getFileFromS3(filePath);
      default:
        throw new Error(`Unsupported storage type: ${this.config.type}`);
    }
  }

  /**
   * Delete file from local storage
   */
  async deleteFileLocally(filePath: string): Promise<void> {
    try {
      const fullPath = path.join(process.cwd(), 'uploads', filePath);
      await fs.promises.unlink(fullPath);
    } catch (error) {
      // File might not exist, which is okay for deletion
      console.warn(`Could not delete file ${filePath}:`, error);
    }
  }

  /**
   * Delete file from S3 (placeholder)
   */
  async deleteFileFromS3(filePath: string): Promise<void> {
    // TODO: Implement S3 file deletion
    throw new Error('S3 file deletion not implemented yet');
  }

  /**
   * Delete file based on configuration
   */
  async deleteFile(filePath: string): Promise<void> {
    switch (this.config.type) {
      case 'local':
        return this.deleteFileLocally(filePath);
      case 's3':
        return this.deleteFileFromS3(filePath);
      default:
        throw new Error(`Unsupported storage type: ${this.config.type}`);
    }
  }

  /**
   * Check if file exists
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      switch (this.config.type) {
        case 'local':
          const fullPath = path.join(process.cwd(), 'uploads', filePath);
          await fs.promises.access(fullPath, fs.constants.F_OK);
          return true;
        case 's3':
          // TODO: Implement S3 file existence check
          return false;
        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  /**
   * Get file stats
   */
  async getFileStats(filePath: string): Promise<{ size: number; mtime: Date } | null> {
    try {
      switch (this.config.type) {
        case 'local':
          const fullPath = path.join(process.cwd(), 'uploads', filePath);
          const stats = await fs.promises.stat(fullPath);
          return {
            size: stats.size,
            mtime: stats.mtime,
          };
        case 's3':
          // TODO: Implement S3 file stats
          return null;
        default:
          return null;
      }
    } catch {
      return null;
    }
  }
}
