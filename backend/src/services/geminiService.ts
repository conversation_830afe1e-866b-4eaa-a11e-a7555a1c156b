import { getCon<PERSON><PERSON><PERSON><PERSON>, handle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GeminiError } from '../config/gemini';
import { PersonaType } from '@prisma/client';
import { loggers } from '../config/logger';
import { TextExtractionService } from './textExtractionService';

export interface DocumentAnalysisRequest {
  documentContent: string; // Now contains extracted text instead of base64
  documentName: string;
  mimeType: string;
  prompt: string;
  personaType: PersonaType;
  dealContext: {
    dealType: string;
    jurisdiction?: string;
    industry?: string;
    dealValue?: string;
  };
  extractionMetadata?: {
    pageCount?: number;
    wordCount?: number;
    extractionMethod: string;
    originalMimeType: string;
  };
}

export interface DocumentAnalysisResponse {
  summary: string;
  confidence_score: number;
  risks: Array<{
    id?: string;
    title: string;
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    category: string;
    recommendation: string;
    source_documents?: string[];
  }>;
  recommendations: Array<{
    id?: string;
    title: string;
    description: string;
    priority: 'LOW' | 'MEDIUM' | 'HIGH';
    action_required: boolean;
    source_documents?: string[];
  }>;
  extracted_data: Array<{
    field: string;
    value: string;
    confidence: number;
    source_document: string;
    page_reference?: string;
    verification_status: 'VERIFIED' | 'FLAGGED' | 'PENDING';
  }>;
  compliance_issues?: Array<{
    regulation: string;
    issue: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
    remediation: string;
  }>;
  document_insights: {
    key_findings: string[];
    risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
    completeness_score: number;
    document_quality: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  };
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  estimatedCost: number;
}

export class GeminiService {
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second

  /**
   * Generate a comprehensive due diligence report using Gemini AI
   */
  static async generateReport(prompt: string): Promise<any> {
    try {
      loggers.gemini.info('Starting report generation with Gemini');

      const model = getConfiguredModel();

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      loggers.gemini.info('Report generation completed successfully');

      // Try to parse as JSON, fallback to structured text
      try {
        return JSON.parse(text);
      } catch (parseError) {
        loggers.gemini.warn('Failed to parse report as JSON, returning as structured text');
        return {
          content: text,
          format: 'text',
          generated_at: new Date().toISOString()
        };
      }

    } catch (error) {
      loggers.gemini.error({ error: error instanceof Error ? error.message : error }, 'Report generation failed');
      throw handleGeminiError(error);
    }
  }

  // Analyze document with Gemini
  static async analyzeDocument(request: DocumentAnalysisRequest): Promise<{
    analysis: DocumentAnalysisResponse;
    tokenUsage: TokenUsage;
  }> {
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        loggers.gemini.info({
          attempt,
          maxRetries: this.MAX_RETRIES,
          documentName: request.documentName,
          mimeType: request.mimeType,
          contentLength: request.documentContent.length,
          personaType: request.personaType
        }, 'Starting Gemini analysis attempt');

        const model = getConfiguredModel();

        // Prepare the content parts - now using extracted text instead of binary data
        const parts = [
          {
            text: `${request.prompt}\n\n--- DOCUMENT CONTENT ---\nDocument: ${request.documentName}\nType: ${request.mimeType}\n${request.extractionMetadata ? `Extraction Method: ${request.extractionMetadata.extractionMethod}\nWord Count: ${request.extractionMetadata.wordCount}\n` : ''}\nContent:\n${request.documentContent}`
          }
        ];

        loggers.gemini.debug({ documentName: request.documentName }, 'Sending request to Gemini');

        // Generate content
        const result = await model.generateContent(parts);
        const response = await result.response;
        const text = response.text();

        loggers.gemini.info({
          documentName: request.documentName,
          responseLength: text.length
        }, 'Gemini analysis completed');

        // Parse the JSON response
        const analysis = this.parseAnalysisResponse(text, request.personaType);

        // Calculate token usage (approximate)
        const tokenUsage = this.calculateTokenUsage(request.prompt, text);

        return {
          analysis,
          tokenUsage
        };

      } catch (error) {
        lastError = error as Error;
        loggers.gemini.error({
          attempt,
          documentName: request.documentName,
          error: (error as Error).message,
          errorName: (error as Error).name,
          stack: (error as Error).stack?.substring(0, 500)
        }, 'Gemini analysis attempt failed');

        const geminiError = handleGeminiError(error);

        // Don't retry on certain errors
        if (geminiError.code === 'API_KEY_ERROR' || geminiError.code === 'SAFETY_FILTER') {
          loggers.gemini.error({
            errorCode: geminiError.code
          }, 'Non-retryable Gemini error');
          throw geminiError;
        }

        // Wait before retry (exponential backoff)
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAY * Math.pow(2, attempt - 1);
          loggers.gemini.warn({
            delay,
            nextAttempt: attempt + 1
          }, 'Waiting before retry');
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new GeminiError(
      `Failed to analyze document after ${this.MAX_RETRIES} attempts: ${lastError?.message || 'Unknown error'}`,
      'MAX_RETRIES_EXCEEDED',
      lastError
    );
  }

  // Parse Gemini response into structured format
  private static parseAnalysisResponse(text: string, personaType: PersonaType): DocumentAnalysisResponse {
    try {
      // Clean the response text
      let cleanText = text.trim();

      // Remove markdown code blocks if present
      cleanText = cleanText.replace(/```json\s*/g, '').replace(/```\s*$/g, '');

      // Try to extract JSON from the response
      let jsonMatch = cleanText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // Try alternative patterns
        jsonMatch = cleanText.match(/(\{.*\})/s);
        if (!jsonMatch) {
          throw new Error('No JSON found in response');
        }
      }

      let parsed;
      try {
        parsed = JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        // Try to fix common JSON issues
        let fixedJson = jsonMatch[0]
          .replace(/,\s*}/g, '}')  // Remove trailing commas
          .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
          .replace(/([{,]\s*)(\w+):/g, '$1"$2":'); // Add quotes to unquoted keys

        parsed = JSON.parse(fixedJson);
      }

      // Normalize the response structure
      const normalizedResponse = this.normalizeAnalysisResponse(parsed, personaType);

      loggers.gemini.info({
        personaType,
        risksCount: normalizedResponse.risks.length,
        recommendationsCount: normalizedResponse.recommendations.length,
        extractedDataCount: normalizedResponse.extracted_data.length,
        confidenceScore: normalizedResponse.confidence_score
      }, 'Successfully parsed Gemini response');

      return normalizedResponse;

    } catch (error) {
      loggers.gemini.error({
        error: (error as Error).message,
        responseLength: text.length,
        personaType
      }, 'Failed to parse Gemini response');

      // Return fallback response
      return this.createFallbackResponse(text, personaType);
    }
  }

  // Normalize and validate the parsed response
  private static normalizeAnalysisResponse(parsed: any, personaType: PersonaType): DocumentAnalysisResponse {
    // Extract summary
    const summary = this.extractSummary(parsed);

    // Extract confidence score
    const confidence_score = this.extractConfidenceScore(parsed);

    // Extract and normalize risks
    const risks = this.normalizeRisks(parsed.risks || []);

    // Extract and normalize recommendations
    const recommendations = this.normalizeRecommendations(parsed.recommendations || []);

    // Extract and normalize extracted data
    const extracted_data = this.normalizeExtractedData(parsed.extracted_data || []);

    return {
      summary,
      confidence_score,
      risks,
      recommendations,
      extracted_data,
      document_insights: {
        key_findings: this.extractKeyFindings(parsed),
        risk_level: this.determineRiskLevel(risks),
        completeness_score: confidence_score,
        document_quality: confidence_score > 70 ? 'GOOD' : confidence_score > 40 ? 'FAIR' : 'POOR'
      }
    };
  }

  // Helper methods for normalizing response data
  private static extractSummary(parsed: any): string {
    if (typeof parsed.summary === 'string') return parsed.summary;
    if (parsed.analysis_summary) return parsed.analysis_summary;
    if (parsed.overview) return parsed.overview;
    return 'Analysis completed successfully';
  }

  private static extractConfidenceScore(parsed: any): number {
    const score = parsed.confidence_score || parsed.confidence || parsed.score || 50;
    return Math.max(0, Math.min(100, Number(score)));
  }

  private static normalizeRisks(risks: any[]): any[] {
    if (!Array.isArray(risks)) return [];

    return risks.map((risk, index) => {
      // Handle malformed risk objects (like character arrays)
      if (Array.isArray(risk) || typeof risk !== 'object') {
        return {
          id: `risk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: 'Unstructured Risk Identified',
          description: 'Risk data could not be properly parsed',
          severity: 'MEDIUM',
          category: 'General',
          recommendation: 'Manual review required',
          source_documents: []
        };
      }

      return {
        id: risk.id || `risk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: this.cleanString(risk.title || risk.name || `Risk ${index + 1}`),
        description: this.cleanString(risk.description || risk.details || 'No description available'),
        severity: this.normalizeSeverity(risk.severity || risk.level),
        category: this.cleanString(risk.category || 'General'),
        recommendation: this.cleanString(risk.recommendation || risk.mitigation || 'Review recommended'),
        source_documents: Array.isArray(risk.source_documents) ? risk.source_documents : []
      };
    });
  }

  private static normalizeRecommendations(recommendations: any[]): any[] {
    if (!Array.isArray(recommendations)) return [];

    return recommendations.map((rec, index) => {
      // Handle malformed recommendation objects
      if (Array.isArray(rec) || typeof rec !== 'object') {
        return {
          id: `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: 'Unstructured Recommendation',
          description: 'Recommendation data could not be properly parsed',
          priority: 'MEDIUM',
          action_required: false,
          source_documents: []
        };
      }

      return {
        id: rec.id || `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: this.cleanString(rec.title || rec.name || `Recommendation ${index + 1}`),
        description: this.cleanString(rec.description || rec.details || 'No description available'),
        priority: this.normalizePriority(rec.priority || rec.importance),
        action_required: Boolean(rec.action_required || rec.urgent),
        source_documents: Array.isArray(rec.source_documents) ? rec.source_documents : []
      };
    });
  }

  private static normalizeExtractedData(extractedData: any[]): any[] {
    if (!Array.isArray(extractedData)) return [];

    return extractedData.map((data, index) => {
      if (typeof data !== 'object') return null;

      return {
        field: this.cleanString(data.field || data.name || `Field ${index + 1}`),
        value: this.cleanString(data.value || data.content || 'No value'),
        confidence: Math.max(0, Math.min(100, Number(data.confidence || 50))),
        source_document: this.cleanString(data.source_document || data.source || 'Unknown'),
        verification_status: this.normalizeVerificationStatus(data.verification_status || data.status)
      };
    }).filter(Boolean);
  }

  private static extractKeyFindings(parsed: any): string[] {
    if (Array.isArray(parsed.key_findings)) return parsed.key_findings.map((f: any) => this.cleanString(f));
    if (Array.isArray(parsed.findings)) return parsed.findings.map((f: any) => this.cleanString(f));
    if (Array.isArray(parsed.highlights)) return parsed.highlights.map((f: any) => this.cleanString(f));
    return [];
  }

  private static determineRiskLevel(risks: any[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (!risks.length) return 'LOW';
    const highRisks = risks.filter(r => r.severity === 'HIGH' || r.severity === 'CRITICAL');
    if (highRisks.length > 0) return 'HIGH';
    const mediumRisks = risks.filter(r => r.severity === 'MEDIUM');
    if (mediumRisks.length > 0) return 'MEDIUM';
    return 'LOW';
  }

  // Utility methods
  private static cleanString(value: any): string {
    if (typeof value === 'string') return value.trim();
    if (Array.isArray(value)) {
      // Handle character arrays by joining them
      return value.join('').trim();
    }
    if (value && typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value || '').trim();
  }

  private static normalizeSeverity(severity: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const s = String(severity || '').toUpperCase();
    if (['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'].includes(s)) return s as any;
    return 'MEDIUM';
  }

  private static normalizePriority(priority: any): 'LOW' | 'MEDIUM' | 'HIGH' {
    const p = String(priority || '').toUpperCase();
    if (['HIGH', 'MEDIUM', 'LOW'].includes(p)) return p as any;
    return 'MEDIUM';
  }

  private static normalizeVerificationStatus(status: any): 'VERIFIED' | 'FLAGGED' | 'PENDING' {
    const s = String(status || '').toUpperCase();
    if (['VERIFIED', 'FLAGGED', 'PENDING'].includes(s)) return s as any;
    return 'PENDING';
  }

  // Create fallback response when parsing fails
  private static createFallbackResponse(text: string, personaType: PersonaType): DocumentAnalysisResponse {
    return {
      summary: `Analysis completed by ${personaType} analyst. Unable to parse structured response.`,
      confidence_score: 30,
      risks: [{
        id: `risk_${Date.now()}_fallback`,
        title: 'Analysis Parsing Issue',
        description: 'Unable to parse structured analysis from AI response',
        severity: 'MEDIUM',
        category: 'Technical',
        recommendation: 'Manual review recommended',
        source_documents: []
      }],
      recommendations: [{
        id: `rec_${Date.now()}_fallback`,
        title: 'Manual Review Required',
        description: 'AI analysis could not be properly structured',
        priority: 'MEDIUM',
        action_required: true,
        source_documents: []
      }],
      extracted_data: [],
      document_insights: {
        key_findings: ['Analysis parsing failed'],
        risk_level: 'MEDIUM',
        completeness_score: 30,
        document_quality: 'POOR'
      }
    };
  }

  // Calculate approximate token usage and cost
  private static calculateTokenUsage(prompt: string, response: string): TokenUsage {
    // Rough estimation: 1 token ≈ 4 characters for English text
    const promptTokens = Math.ceil(prompt.length / 4);
    const completionTokens = Math.ceil(response.length / 4);
    const totalTokens = promptTokens + completionTokens;

    // Gemini pricing (approximate): $0.00025 per 1K tokens for input, $0.00075 per 1K tokens for output
    const inputCost = (promptTokens / 1000) * 0.00025;
    const outputCost = (completionTokens / 1000) * 0.00075;
    const estimatedCost = inputCost + outputCost;

    return {
      promptTokens,
      completionTokens,
      totalTokens,
      estimatedCost
    };
  }

  // Test Gemini connection
  static async testConnection(): Promise<boolean> {
    try {
      const model = getConfiguredModel();
      const result = await model.generateContent("Test connection. Respond with 'OK'.");
      const response = await result.response;
      const text = response.text();

      loggers.gemini.info({ response: text }, 'Gemini connection test');
      return text.toLowerCase().includes('ok');
    } catch (error) {
      loggers.gemini.error({ error: (error as Error).message }, 'Gemini connection test failed');
      return false;
    }
  }



  // Validate document for analysis
  static validateDocument(buffer: Buffer, mimeType: string): { valid: boolean; error?: string } {
    // Check file size (increased limit since we're extracting text)
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (buffer.length > maxSize) {
      return { valid: false, error: 'Document too large (max 20MB)' };
    }

    // Check if the MIME type is supported by our text extraction service
    if (!TextExtractionService.isSupportedMimeType(mimeType)) {
      return {
        valid: false,
        error: `Unsupported file type: ${mimeType}. Supported formats: PDF, DOCX, DOC, XLSX, XLS, PPTX, PPT, TXT, CSV, RTF, and images.`
      };
    }

    return { valid: true };
  }

  // Extract text from document for analysis
  static async extractDocumentText(buffer: Buffer, mimeType: string, filename: string): Promise<{
    text: string;
    metadata: any;
  }> {
    try {
      const result = await TextExtractionService.extractText(buffer, mimeType, filename);

      // Truncate text if too long for Gemini
      const truncatedText = TextExtractionService.truncateText(result.text);

      return {
        text: truncatedText,
        metadata: result.metadata
      };
    } catch (error) {
      loggers.gemini.error({
        filename,
        mimeType,
        error: (error as Error).message
      }, 'Text extraction failed');

      throw new Error(`Failed to extract text from document: ${(error as Error).message}`);
    }
  }
}
