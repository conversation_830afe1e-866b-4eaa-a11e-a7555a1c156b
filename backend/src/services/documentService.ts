import prisma from '../config/database';
import { DocumentStatus } from '@prisma/client';
import { FileStorageService } from './fileStorageService';
import { PAGINATION, ERROR_MESSAGES } from '../constants';
import crypto from 'crypto';
import {
  DocumentResponse,
  DocumentListResponse,
  DocumentListQuery,
  UploadDocumentResponse,
  DocumentStats,
  UpdateDocumentStatusRequest,
} from '../types/document';

export class DocumentService {
  private fileStorage: FileStorageService;

  constructor() {
    this.fileStorage = new FileStorageService();
  }

  /**
   * Upload documents for a deal and requisition item
   */
  async uploadDocuments(
    files: Express.Multer.File[],
    dealId: string,
    uploadedById: string,
    requisitionItemId?: string,
    remarks?: string
  ): Promise<UploadDocumentResponse> {
    const uploadedDocuments: DocumentResponse[] = [];
    const failedUploads: { filename: string; error: string }[] = [];

    // Validate deal exists and user has access
    const deal = await this.validateDealAccess(dealId, uploadedById);

    // Validate requisition item if provided
    let requisitionItem = null;
    if (requisitionItemId) {
      try {
        requisitionItem = await this.validateRequisitionItem(requisitionItemId, dealId);
      } catch (error) {
        console.log('⚠️ Requisition item validation failed, proceeding without it:', error instanceof Error ? error.message : error);
        // Continue without requisition item instead of failing the entire upload
        requisitionItem = null;
      }
    }

    for (const file of files) {
      try {
        // Check for empty file buffer
        if (!file.buffer || file.buffer.length === 0) {
          console.log(`❌ Empty file buffer for: ${file.originalname}`);
          failedUploads.push({
            filename: file.originalname,
            error: 'File is empty or could not be read',
          });
          continue;
        }

        // Validate file
        const validation = this.fileStorage.validateFile(
          file,
          requisitionItem?.acceptedFormats as string[],
          requisitionItem?.maxFileSize || undefined
        );

        if (!validation.isValid) {
          console.log(`❌ File validation failed for: ${file.originalname}`, validation.error);
          failedUploads.push({
            filename: file.originalname,
            error: validation.error || 'File validation failed',
          });
          continue;
        }

        // Generate file hash for integrity and deduplication
        let fileHash: string;
        try {
          fileHash = crypto.createHash('sha256').update(file.buffer).digest('hex');
          console.log(`✅ Generated hash for ${file.originalname}: ${fileHash.substring(0, 8)}...`);
        } catch (error) {
          console.error(`❌ Failed to generate hash for ${file.originalname}:`, error);
          failedUploads.push({
            filename: file.originalname,
            error: 'Failed to process file content',
          });
          continue;
        }

        // Generate unique filename
        const timestamp = Date.now();
        const randomSuffix = crypto.randomBytes(4).toString('hex');
        const fileExtension = file.originalname.split('.').pop() || '';
        const uniqueFilename = `${timestamp}_${randomSuffix}.${fileExtension}`;

        console.log('💾 Storing file as binary data:', {
          originalName: file.originalname,
          filename: uniqueFilename,
          size: file.size,
          mimeType: file.mimetype,
          hash: fileHash.substring(0, 8) + '...'
        });

        // Create document record with binary data
        const documentData: any = {
          filename: uniqueFilename,
          originalName: file.originalname,
          fileData: file.buffer, // Store binary data directly
          // filePath is optional, omit it for binary storage
          fileSize: file.size,
          mimeType: file.mimetype,
          fileHash,
          status: DocumentStatus.UPLOADED,
          version: 1,
          remarks,
          dealId,
          uploadedById: uploadedById,
          templateItemId: requisitionItemId, // Store the original template item ID for frontend matching
        };

        // Only add requisitionItemId if we have a valid requisition item
        if (requisitionItem) {
          documentData.requisitionItemId = requisitionItem.id;
          console.log(`📎 Linking document to requisition item: ${requisitionItem.id} (${requisitionItem.name}) and template ID: ${requisitionItemId}`);
        } else {
          console.log(`📎 Creating document without database requisition item link, but storing template ID: ${requisitionItemId}`);
        }

        const document = await prisma.document.create({
          data: documentData,
          include: {
            uploadedBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            requisitionItem: {
              select: {
                id: true,
                name: true,
                description: true,
                isRequired: true,
              },
            },
          },
        });

        uploadedDocuments.push(this.formatDocumentResponse(document));
      } catch (error) {
        failedUploads.push({
          filename: file.originalname,
          error: error instanceof Error ? error.message : 'Upload failed',
        });
      }
    }

    return {
      success: uploadedDocuments.length > 0,
      message: `${uploadedDocuments.length} document(s) uploaded successfully${failedUploads.length > 0 ? `, ${failedUploads.length} failed` : ''
        }`,
      documents: uploadedDocuments,
      failed: failedUploads.length > 0 ? failedUploads : undefined,
    };
  }

  /**
   * Get documents for a deal
   */
  async getDocuments(
    dealId: string,
    userId: string,
    query: DocumentListQuery = {}
  ): Promise<DocumentListResponse> {
    // Validate deal access
    await this.validateDealAccess(dealId, userId);

    const {
      page = PAGINATION.DEFAULT_PAGE,
      limit = PAGINATION.DEFAULT_LIMIT,
      status,
      requisitionItemId,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      dealId,
    };

    if (status) {
      where.status = status;
    }

    if (requisitionItemId) {
      where.requisitionItemId = requisitionItemId;
    }

    if (search) {
      where.OR = [
        { originalName: { contains: search, mode: 'insensitive' } },
        { filename: { contains: search, mode: 'insensitive' } },
        { remarks: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get documents with pagination
    const [documents, total] = await Promise.all([
      prisma.document.findMany({
        where,
        include: {
          uploadedBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          requisitionItem: {
            select: {
              id: true,
              name: true,
              description: true,
              isRequired: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.document.count({ where }),
    ]);

    return {
      documents: documents.map((doc: any) => this.formatDocumentResponse(doc)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get document by ID
   */
  async getDocumentById(documentId: string, userId: string): Promise<DocumentResponse> {
    const document = await prisma.document.findFirst({
      where: {
        id: documentId,
        deal: {
          OR: [
            { createdById: userId },
            { assignedToId: userId },
            {
              organization: {
                users: {
                  some: { id: userId },
                },
              },
            },
          ],
        },
      },
      include: {
        uploadedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        requisitionItem: {
          select: {
            id: true,
            name: true,
            description: true,
            isRequired: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('Document not found or access denied');
    }

    return this.formatDocumentResponse(document);
  }

  /**
   * Update document status
   */
  async updateDocumentStatus(
    documentId: string,
    userId: string,
    updateData: UpdateDocumentStatusRequest
  ): Promise<DocumentResponse> {
    // Validate document access
    const existingDocument = await this.getDocumentById(documentId, userId);

    const document = await prisma.document.update({
      where: { id: documentId },
      data: {
        status: updateData.status,
        remarks: updateData.remarks || existingDocument.remarks,
        updatedAt: new Date(),
      },
      include: {
        uploadedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        requisitionItem: {
          select: {
            id: true,
            name: true,
            description: true,
            isRequired: true,
          },
        },
      },
    });

    return this.formatDocumentResponse(document);
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string, userId: string): Promise<void> {
    // Validate document access
    const document = await this.getDocumentById(documentId, userId);

    // Delete file from storage
    try {
      await this.fileStorage.deleteFile(document.filePath);
    } catch (error) {
      console.warn(`Failed to delete file ${document.filePath}:`, error);
    }

    // Delete document record
    await prisma.document.delete({
      where: { id: documentId },
    });
  }

  /**
   * Download document
   */
  async downloadDocument(documentId: string, userId: string): Promise<{
    buffer: Buffer;
    filename: string;
    mimeType: string;
  }> {
    const document = await prisma.document.findFirst({
      where: {
        id: documentId,
        deal: {
          OR: [
            { createdById: userId },
            { assignedToId: userId },
            // Add more access control logic as needed
          ],
        },
      },
    });

    if (!document) {
      throw new Error('Document not found or access denied');
    }

    // For binary storage, return the fileData directly
    if (document.fileData) {
      return {
        buffer: Buffer.from(document.fileData),
        filename: document.originalName,
        mimeType: document.mimeType,
      };
    }

    // Fallback to file system storage if fileData is not available
    if (document.filePath) {
      const buffer = await this.fileStorage.getFile(document.filePath);
      return {
        buffer,
        filename: document.originalName,
        mimeType: document.mimeType,
      };
    }

    throw new Error('Document data not available');
  }

  /**
   * Get document statistics for a deal
   */
  async getDocumentStats(dealId: string, userId: string): Promise<DocumentStats> {
    // Validate deal access
    await this.validateDealAccess(dealId, userId);

    const [documents, requisitionItems] = await Promise.all([
      prisma.document.findMany({
        where: { dealId },
        select: {
          status: true,
          fileSize: true,
          requisitionItemId: true,
        },
      }),
      prisma.requisitionItem.findMany({
        where: {
          category: {
            template: {
              dealRequisitions: {
                some: { dealId },
              },
            },
          },
        },
        select: {
          id: true,
          name: true,
          isRequired: true,
          _count: {
            select: {
              documents: {
                where: { dealId },
              },
            },
          },
        },
      }),
    ]);

    // Calculate statistics
    const totalDocuments = documents.length;
    const totalFileSize = documents.reduce((sum: number, doc: any) => sum + doc.fileSize, 0);
    const averageFileSize = totalDocuments > 0 ? totalFileSize / totalDocuments : 0;

    const documentsByStatus = documents.reduce((acc: any, doc: any) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1;
      return acc;
    }, {} as { [key in DocumentStatus]: number });

    // Ensure all statuses are represented
    Object.values(DocumentStatus).forEach(status => {
      if (!documentsByStatus[status]) {
        documentsByStatus[status] = 0;
      }
    });

    const documentsByRequisitionItem = requisitionItems.map((item: any) => ({
      itemId: item.id,
      itemName: item.name,
      documentCount: item._count.documents,
      isRequired: item.isRequired,
      hasDocuments: item._count.documents > 0,
    }));

    return {
      totalDocuments,
      documentsByStatus,
      totalFileSize,
      averageFileSize,
      documentsByRequisitionItem,
    };
  }

  /**
   * Validate deal access
   */
  private async validateDealAccess(dealId: string, userId: string) {
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!deal) {
      throw new Error('Deal not found or access denied');
    }

    return deal;
  }

  /**
   * Validate requisition item
   */
  private async validateRequisitionItem(requisitionItemId: string, dealId: string) {
    console.log('🔍 Validating requisition item:', { requisitionItemId, dealId });

    const item = await prisma.requisitionItem.findFirst({
      where: {
        id: requisitionItemId,
        category: {
          template: {
            dealRequisitions: {
              some: { dealId },
            },
          },
        },
      },
      select: {
        id: true,
        name: true,
        acceptedFormats: true,
        maxFileSize: true,
      },
    });

    if (!item) {
      // Log available items for debugging
      const availableItems = await prisma.requisitionItem.findMany({
        where: {
          category: {
            template: {
              dealRequisitions: {
                some: { dealId },
              },
            },
          },
        },
        select: {
          id: true,
          name: true,
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      console.log('❌ Requisition item not found. Available items:', availableItems);
      throw new Error(`Requisition item '${requisitionItemId}' not found or not associated with deal '${dealId}'. Available items: ${availableItems.map(i => `${i.id} (${i.name})`).join(', ')}`);
    }

    console.log('✅ Requisition item validated:', item);
    return item;
  }



  /**
   * Format document response
   */
  private formatDocumentResponse(document: any): DocumentResponse {
    return {
      id: document.id,
      filename: document.filename,
      originalName: document.originalName,
      filePath: document.filePath,
      fileSize: document.fileSize,
      mimeType: document.mimeType,
      status: document.status,
      version: document.version,
      remarks: document.remarks,
      createdAt: document.createdAt.toISOString(),
      updatedAt: document.updatedAt.toISOString(),
      dealId: document.dealId,
      requisitionItemId: document.requisitionItemId,
      templateItemId: document.templateItemId, // Include template item ID for frontend matching
      uploadedBy: document.uploadedBy,
      requisitionItem: document.requisitionItem,
    };
  }
}
