import { DealStatus, DealType, Prisma } from "@prisma/client";
import prisma from "../config/database";
import { ERROR_MESSAGES } from "../constants";
import {
  CreateDealRequest,
  UpdateDealRequest,
  DealResponse,
  DealListQuery,
  DealListResponse,
  DealAssignmentRequest,
  DealStatusUpdateRequest,
  DealStatsResponse,
  AddDealNoteRequest,
} from "../types/deal";
import { RequisitionService } from "./requisitionService";

export class DealService {
  // Create a new deal
  static async createDeal(
    data: CreateDealRequest,
    createdById: string,
    organizationId: string
  ): Promise<DealResponse> {
    // Check if deal with same title exists in organization
    const existingDeal = await prisma.deal.findFirst({
      where: {
        title: data.title,
        organizationId,
      },
    });

    if (existingDeal) {
      throw new Error(ERROR_MESSAGES.DEAL_ALREADY_EXISTS);
    }

    // Validate assigned user if provided
    if (data.assignedToId) {
      const assignedUser = await prisma.user.findFirst({
        where: {
          id: data.assignedToId,
          organizationId,
        },
      });

      if (!assignedUser) {
        throw new Error(
          "Assigned user not found or not in the same organization"
        );
      }
    }

    // Create deal with related data
    const deal = await prisma.deal.create({
      data: {
        title: data.title,
        type: data.type,
        currency: data.currency,
        value: data.value ? new Prisma.Decimal(data.value) : null,
        jurisdiction: data.jurisdiction,
        buyer: data.buyer,
        seller: data.seller,
        startDate: data.startDate ? new Date(data.startDate) : null,
        expectedSigningDate: data.expectedSigningDate
          ? new Date(data.expectedSigningDate)
          : null,
        expectedClosingDate: data.expectedClosingDate
          ? new Date(data.expectedClosingDate)
          : null,
        industry: data.industry,
        description: data.description,
        riskLevel: data.riskLevel as any,
        confidentialityLevel: data.confidentialityLevel as any,
        organizationId,
        createdById,
        assignedToId: data.assignedToId,
        parties: data.parties
          ? {
              create: data.parties.map((party) => ({
                partyType: party.partyType,
                name: party.name,
                role: party.role,
                contactInfo: party.contactInfo,
              })),
            }
          : undefined,
        suppliers: data.suppliers
          ? {
              create: data.suppliers.map((supplier) => ({
                name: supplier.name,
                jurisdiction: supplier.jurisdiction,
                contactInfo: supplier.contactInfo,
                description: supplier.description,
              })),
            }
          : undefined,
        dates: data.dates
          ? {
              create: data.dates.map((date) => ({
                dateType: date.dateType,
                dateValue: new Date(date.dateValue),
                description: date.description,
              })),
            }
          : undefined,
        notes: data.notes
          ? {
              create: {
                note: data.notes,
                userId: createdById,
              },
            }
          : undefined,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        suppliers: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    // Create default requisition automatically (unless explicitly disabled)
    if (
      data.createDefaultRequisition !== false &&
      data.createRequisition !== false
    ) {
      try {
        // Find default template for this deal type or use a generic one
        let defaultTemplate = await prisma.requisitionTemplate.findFirst({
          where: {
            dealType: data.type,
            isActive: true,
          },
        });

        // If no specific template for deal type, use any active template
        if (!defaultTemplate) {
          defaultTemplate = await prisma.requisitionTemplate.findFirst({
            where: {
              isActive: true,
            },
          });
        }

        if (defaultTemplate) {
          const templateId = data.requisitionTemplateId || defaultTemplate.id;

          // Prepare requisition data
          const requisitionData: any = {
            dealId: deal.id,
            templateId,
          };

          // If custom requisition data is provided, include it
          if (data.customRequisitionData) {
            requisitionData.customData = {
              categories: data.customRequisitionData.categories.map(
                (category, categoryIndex) => ({
                  id: category.id,
                  name: category.title,
                  description: `Custom category: ${category.title}`,
                  orderIndex: categoryIndex + 1,
                  isRequired: false,
                  isCustom: true,
                  items: category.items.map((item, itemIndex) => ({
                    id: `${category.id}_item_${itemIndex}`,
                    name: item,
                    description: item,
                    isRequired: false,
                    orderIndex: itemIndex + 1,
                    isCustom: true,
                    status: "pending",
                    examples: [],
                    acceptedFormats: [],
                    uploadedDocuments: [],
                  })),
                })
              ),
            };
          }

          await RequisitionService.createDealRequisition(
            requisitionData,
            createdById
          );

          console.log(
            `✅ Created ${
              data.customRequisitionData ? "custom" : "default"
            } requisition for deal ${deal.id} using template ${templateId}`
          );
        } else {
          console.warn(
            `⚠️ No requisition template found for deal type ${data.type}`
          );
        }
      } catch (error) {
        // Log error but don't fail deal creation
        console.error("❌ Failed to create default requisition:", error);
      }
    }

    return this.formatDealResponse(deal);
  }

  // Get deal by ID
  static async getDealById(
    dealId: string,
    userId: string
  ): Promise<DealResponse> {
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        suppliers: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    return this.formatDealResponse(deal);
  }

  // Get deals list with filtering and pagination
  static async getDeals(
    query: DealListQuery,
    userId: string,
    organizationId: string
  ): Promise<DealListResponse> {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      type,
      assignedToId,
      createdById,
      sortBy = "createdAt",
      sortOrder = "desc",
      startDate,
      endDate,
    } = query;

    const skip = (page - 1) * limit;
    const take = Math.min(limit, 100); // Max 100 items per page

    // Build where clause
    const where: Prisma.DealWhereInput = {
      organizationId,
      ...(search && {
        OR: [
          { title: { contains: search, mode: "insensitive" } },
          { buyer: { contains: search, mode: "insensitive" } },
          { seller: { contains: search, mode: "insensitive" } },
          { jurisdiction: { contains: search, mode: "insensitive" } },
        ],
      }),
      ...(status && { status }),
      ...(type && { type }),
      ...(assignedToId && { assignedToId }),
      ...(createdById && { createdById }),
      ...(startDate &&
        endDate && {
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
    };

    // Get total count
    const total = await prisma.deal.count({ where });

    // Get deals
    const deals = await prisma.deal.findMany({
      where,
      skip,
      take,
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        suppliers: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1, // Only latest note for list view
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(total / take);

    return {
      deals: deals.map((deal) => this.formatDealResponse(deal)),
      pagination: {
        page,
        limit: take,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        status,
        type,
        assignedToId,
        createdById,
      },
    };
  }

  // Update deal
  static async updateDeal(
    dealId: string,
    data: UpdateDealRequest,
    userId: string
  ): Promise<DealResponse> {
    // Check if deal exists and user has access
    const existingDeal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!existingDeal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Validate assigned user if provided
    if (data.assignedToId) {
      const assignedUser = await prisma.user.findFirst({
        where: {
          id: data.assignedToId,
          organizationId: existingDeal.organizationId,
        },
      });

      if (!assignedUser) {
        throw new Error(
          "Assigned user not found or not in the same organization"
        );
      }
    }

    // Update deal
    const updatedDeal = await prisma.deal.update({
      where: { id: dealId },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.type && { type: data.type }),
        ...(data.currency !== undefined && { currency: data.currency }),
        ...(data.value !== undefined && {
          value: data.value ? new Prisma.Decimal(data.value) : null,
        }),
        ...(data.jurisdiction !== undefined && {
          jurisdiction: data.jurisdiction,
        }),
        ...(data.buyer !== undefined && { buyer: data.buyer }),
        ...(data.seller !== undefined && { seller: data.seller }),
        ...(data.status && { status: data.status }),
        ...(data.startDate !== undefined && {
          startDate: data.startDate ? new Date(data.startDate) : null,
        }),
        ...(data.expectedSigningDate !== undefined && {
          expectedSigningDate: data.expectedSigningDate
            ? new Date(data.expectedSigningDate)
            : null,
        }),
        ...(data.expectedClosingDate !== undefined && {
          expectedClosingDate: data.expectedClosingDate
            ? new Date(data.expectedClosingDate)
            : null,
        }),
        ...(data.actualClosingDate !== undefined && {
          actualClosingDate: data.actualClosingDate
            ? new Date(data.actualClosingDate)
            : null,
        }),
        ...(data.progress !== undefined && { progress: data.progress }),
        ...(data.assignedToId !== undefined && {
          assignedToId: data.assignedToId,
        }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    return this.formatDealResponse(updatedDeal);
  }

  // Delete deal
  static async deleteDeal(dealId: string, userId: string): Promise<void> {
    // Check if deal exists and user has access (only creator or admin can delete)
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        createdById: userId, // Only creator can delete
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Delete deal (cascade will handle related records)
    await prisma.deal.delete({
      where: { id: dealId },
    });
  }

  // Assign deal to user
  static async assignDeal(
    dealId: string,
    data: DealAssignmentRequest,
    userId: string
  ): Promise<DealResponse> {
    // Check if deal exists and user has access
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Validate assigned user
    const assignedUser = await prisma.user.findFirst({
      where: {
        id: data.assignedToId,
        organizationId: deal.organizationId,
      },
    });

    if (!assignedUser) {
      throw new Error(
        "Assigned user not found or not in the same organization"
      );
    }

    // Update assignment and add note if provided
    const updatedDeal = await prisma.deal.update({
      where: { id: dealId },
      data: {
        assignedToId: data.assignedToId,
        ...(data.note && {
          notes: {
            create: {
              note: `Deal assigned to ${assignedUser.firstName} ${assignedUser.lastName}. ${data.note}`,
              userId,
            },
          },
        }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    return this.formatDealResponse(updatedDeal);
  }

  // Update deal status
  static async updateDealStatus(
    dealId: string,
    data: DealStatusUpdateRequest,
    userId: string
  ): Promise<DealResponse> {
    // Check if deal exists and user has access
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Update status and add note if provided
    const updatedDeal = await prisma.deal.update({
      where: { id: dealId },
      data: {
        status: data.status,
        ...(data.status === DealStatus.COMPLETED && {
          actualClosingDate: new Date(),
          progress: 100,
        }),
        ...(data.note && {
          notes: {
            create: {
              note: `Status changed to ${data.status}. ${data.note}`,
              userId,
            },
          },
        }),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    return this.formatDealResponse(updatedDeal);
  }

  // Submit documents for review (changes status to DOCUMENTS_SUBMITTED)
  static async submitDocumentsForReview(
    dealId: string,
    userId: string
  ): Promise<DealResponse> {
    // Check if deal exists and user has access
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Check if there are any documents uploaded
    const documentCount = await prisma.document.count({
      where: { dealId },
    });

    if (documentCount === 0) {
      throw new Error(
        "Cannot submit for review: No documents have been uploaded yet"
      );
    }

    // Update status to DOCUMENTS_SUBMITTED and add note
    const updatedDeal = await prisma.deal.update({
      where: { id: dealId },
      data: {
        status: DealStatus.DOCUMENTS_SUBMITTED,
        notes: {
          create: {
            note: `Documents submitted for AI review. ${documentCount} document(s) ready for analysis.`,
            userId,
          },
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        parties: true,
        dates: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            documents: true,
            reviews: true,
            comments: true,
          },
        },
      },
    });

    return this.formatDealResponse(updatedDeal);
  }

  // Add note to deal
  static async addDealNote(
    dealId: string,
    data: AddDealNoteRequest,
    userId: string
  ): Promise<DealResponse> {
    // Check if deal exists and user has access
    const deal = await prisma.deal.findFirst({
      where: {
        id: dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Add note
    await prisma.dealNote.create({
      data: {
        note: data.note,
        dealId,
        userId,
      },
    });

    // Return updated deal
    return this.getDealById(dealId, userId);
  }

  // Get deal statistics
  static async getDealStats(
    userId: string,
    organizationId: string
  ): Promise<DealStatsResponse> {
    const deals = await prisma.deal.findMany({
      where: {
        organizationId,
      },
      select: {
        status: true,
        type: true,
        value: true,
        progress: true,
        createdAt: true,
      },
    });

    const total = deals.length;
    const byStatus = deals.reduce((acc, deal) => {
      acc[deal.status] = (acc[deal.status] || 0) + 1;
      return acc;
    }, {} as Record<DealStatus, number>);

    const byType = deals.reduce((acc, deal) => {
      acc[deal.type] = (acc[deal.type] || 0) + 1;
      return acc;
    }, {} as Record<DealType, number>);

    const totalValue = deals.reduce((sum, deal) => {
      return sum + (deal.value ? parseFloat(deal.value.toString()) : 0);
    }, 0);

    const averageProgress =
      deals.length > 0
        ? deals.reduce((sum, deal) => sum + deal.progress, 0) / deals.length
        : 0;

    // Recent activity (deals created in last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentActivity = deals.filter(
      (deal) => deal.createdAt >= thirtyDaysAgo
    ).length;

    return {
      total,
      byStatus,
      byType,
      totalValue,
      averageProgress: Math.round(averageProgress),
      recentActivity,
    };
  }

  // Format deal response
  private static formatDealResponse(deal: any): DealResponse {
    return {
      id: deal.id,
      title: deal.title,
      type: deal.type,
      currency: deal.currency,
      value: deal.value ? parseFloat(deal.value.toString()) : undefined,
      jurisdiction: deal.jurisdiction,
      buyer: deal.buyer,
      seller: deal.seller,
      status: deal.status,
      startDate: deal.startDate?.toISOString(),
      expectedSigningDate: deal.expectedSigningDate?.toISOString(),
      expectedClosingDate: deal.expectedClosingDate?.toISOString(),
      actualClosingDate: deal.actualClosingDate?.toISOString(),
      progress: deal.progress,
      industry: deal.industry,
      description: deal.description,
      riskLevel: deal.riskLevel,
      confidentialityLevel: deal.confidentialityLevel,
      createdAt: deal.createdAt.toISOString(),
      updatedAt: deal.updatedAt.toISOString(),
      organizationId: deal.organizationId,
      createdById: deal.createdById,
      assignedToId: deal.assignedToId,
      createdBy: deal.createdBy,
      assignedTo: deal.assignedTo,
      organization: deal.organization,
      parties:
        deal.parties?.map((party: any) => ({
          id: party.id,
          partyType: party.partyType,
          name: party.name,
          role: party.role,
          contactInfo: party.contactInfo,
          createdAt: party.createdAt.toISOString(),
        })) || [],
      suppliers:
        deal.suppliers?.map((supplier: any) => ({
          id: supplier.id,
          name: supplier.name,
          jurisdiction: supplier.jurisdiction,
          contactInfo: supplier.contactInfo,
          description: supplier.description,
          createdAt: supplier.createdAt.toISOString(),
        })) || [],
      dates:
        deal.dates?.map((date: any) => ({
          id: date.id,
          dateType: date.dateType,
          dateValue: date.dateValue.toISOString(),
          description: date.description,
          createdAt: date.createdAt.toISOString(),
        })) || [],
      notes:
        deal.notes?.map((note: any) => ({
          id: note.id,
          note: note.note,
          createdAt: note.createdAt.toISOString(),
          user: note.user,
        })) || [],
      _count: deal._count,
    };
  }
}
