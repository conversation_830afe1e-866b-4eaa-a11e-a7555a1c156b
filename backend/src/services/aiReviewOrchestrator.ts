import { PersonaType, ReviewStatus } from "@prisma/client";
import prisma from "../config/database";
import { AIPersonaService } from "./aiPersonaService";
import { GeminiService, DocumentAnalysisRequest } from "./geminiService";
import { DocumentQueueService } from "./documentQueueService";
import { loggers } from "../config/logger";

export interface CumulativeAnalysis {
  summary: string;
  confidence_score: number;
  risks: Array<{
    id: string;
    title: string;
    description: string;
    severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    category: string;
    recommendation: string;
    source_documents: string[];
    first_identified: string;
    last_updated: string;
  }>;
  recommendations: Array<{
    id: string;
    title: string;
    description: string;
    priority: "LOW" | "MEDIUM" | "HIGH";
    action_required: boolean;
    source_documents: string[];
    estimated_effort?: string;
    dependencies?: string[];
  }>;
  extracted_data: Array<{
    field: string;
    value: string;
    confidence: number;
    source_document: string;
    page_reference?: string;
    verification_status: "VERIFIED" | "FLAGGED" | "PENDING";
  }>;
  document_insights: {
    [documentId: string]: {
      processed_at: string;
      key_findings: string[];
      risk_level: "LOW" | "MEDIUM" | "HIGH";
      completeness_score: number;
    };
  };
  overall_risk_level: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  completeness_percentage: number;
}

export class AIReviewOrchestrator {
  private static activeReviews = new Map<string, boolean>(); // Track active reviews by dealId

  // Start AI review for all personas
  static async startAIReview(dealId: string, userId: string): Promise<void> {
    loggers.aiReview.info({ dealId, userId }, "Starting AI review for deal");

    // Check if review is already running
    if (this.activeReviews.get(dealId)) {
      loggers.aiReview.warn(
        { dealId },
        "AI review already running for deal, clearing and restarting"
      );
      this.activeReviews.delete(dealId);
    }

    // Verify deal exists and has documents
    const deal = await prisma.deal.findUnique({
      where: { id: dealId },
      include: { documents: true },
    });

    if (!deal) {
      throw new Error("Deal not found");
    }

    if (deal.documents.length === 0) {
      throw new Error("No documents found for this deal");
    }

    // Get user's selected personas
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { selectedPersonas: true },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Validate user has selected personas
    if (!user.selectedPersonas || user.selectedPersonas.length === 0) {
      throw new Error(
        "No personas selected. Please select AI personas in your settings before starting a review."
      );
    }

    // Get the actual persona records for user's selected types
    const personas = await prisma.aIPersona.findMany({
      where: {
        type: { in: user.selectedPersonas },
        isActive: true,
      },
    });

    if (personas.length === 0) {
      throw new Error(
        "Selected personas are not available. Please check your persona settings or initialize personas first."
      );
    }

    loggers.aiReview.info(
      {
        dealId,
        userId,
        documentCount: deal.documents.length,
        selectedPersonas: user.selectedPersonas,
        personaCount: personas.length,
        personaNames: personas.map((p) => p.name),
      },
      "Starting AI review with user-selected personas"
    );

    // Mark review as active
    this.activeReviews.set(dealId, true);

    try {
      // Initialize AI reviews for each persona
      await Promise.all(
        personas.map(async (persona) => {
          await this.initializePersonaReview(
            dealId,
            persona.id,
            deal.documents.length
          );
        })
      );

      // Start processing documents for each persona in parallel
      const processingPromises = personas.map((persona) =>
        this.processPersonaDocuments(dealId, persona.type, deal)
      );

      // Don't await - let them run in background
      Promise.all(processingPromises)
        .then(() => {
          loggers.aiReview.info({ dealId }, "All personas completed for deal");
          this.activeReviews.delete(dealId);
        })
        .catch((error) => {
          loggers.aiReview.error(
            { dealId, error: error.message },
            "Error in AI review for deal"
          );
          this.activeReviews.delete(dealId);
        });

      loggers.aiReview.info(
        {
          dealId,
          personaCount: personas.length,
        },
        "AI review started for all personas"
      );
    } catch (error) {
      this.activeReviews.delete(dealId);
      throw error;
    }
  }

  // Initialize AI review record for a persona
  private static async initializePersonaReview(
    dealId: string,
    personaId: string,
    totalDocuments: number
  ): Promise<void> {
    // Check if review already exists
    const existingReview = await prisma.aIReview.findUnique({
      where: { dealId_personaId: { dealId, personaId } },
    });

    if (existingReview) {
      // Reset existing review
      await prisma.aIReview.update({
        where: { id: existingReview.id },
        data: {
          status: "PENDING",
          totalDocuments,
          processedDocuments: 0,
          completionPercentage: 0,
          currentDocument: null,
          analysisResult: undefined,
          startedAt: null,
          completedAt: null,
          lastUpdated: new Date(),
          errorMessage: null,
          retryCount: 0,
        },
      });
    } else {
      // Create new review
      await prisma.aIReview.create({
        data: {
          dealId,
          personaId,
          status: "PENDING",
          totalDocuments,
          processedDocuments: 0,
          completionPercentage: 0,
          analysisResult: this.createInitialAnalysis() as any,
        },
      });
    }
  }

  // Process documents for a specific persona
  private static async processPersonaDocuments(
    dealId: string,
    personaType: PersonaType,
    deal: any
  ): Promise<void> {
    loggers.aiReview.info(
      { dealId, personaType },
      "Starting persona analysis for deal"
    );

    const personaId = await this.getPersonaId(personaType);

    try {
      // Update status to IN_PROGRESS
      await prisma.aIReview.update({
        where: { dealId_personaId: { dealId, personaId } },
        data: {
          status: "IN_PROGRESS",
          startedAt: new Date(),
          lastUpdated: new Date(),
        },
      });

      let processedCount = 0;
      let document = await DocumentQueueService.getNextDocument(
        dealId,
        personaType
      );

      while (document) {
        loggers.document.info(
          {
            dealId,
            personaType,
            documentId: document.id,
            documentName: document.originalName,
            fileSize: document.fileSize,
            mimeType: document.mimeType,
          },
          "Processing document for persona"
        );

        try {
          // Update current document
          await prisma.aIReview.update({
            where: { dealId_personaId: { dealId, personaId } },
            data: {
              currentDocument: document.id,
              lastUpdated: new Date(),
            },
          });

          // Process the document
          await this.processDocument(dealId, personaType, document, deal);

          processedCount++;

          // Update progress
          const totalDocuments = await prisma.document.count({
            where: { dealId },
          });
          const completionPercentage = Math.round(
            (processedCount / totalDocuments) * 100
          );

          await prisma.aIReview.update({
            where: { dealId_personaId: { dealId, personaId } },
            data: {
              processedDocuments: processedCount,
              completionPercentage,
              lastUpdated: new Date(),
            },
          });

          loggers.document.info(
            {
              dealId,
              personaType,
              documentName: document.originalName,
              processedCount,
              totalDocuments,
            },
            "Document processing completed successfully"
          );
        } catch (error) {
          loggers.document.error(
            {
              dealId,
              personaType,
              documentName: document.originalName,
              error: (error as Error).message,
              stack: (error as Error).stack?.substring(0, 500),
            },
            "Document processing failed"
          );

          // Record the failure but continue with next document
          await this.recordDocumentFailure(
            dealId,
            personaId,
            document.id,
            error as Error
          );
        }

        // Get next document
        document = await DocumentQueueService.getNextDocument(
          dealId,
          personaType
        );
      }

      // Check if any documents were successfully processed
      const totalDocuments = await prisma.document.count({ where: { dealId } });
      const finalStatus =
        processedCount > 0
          ? ReviewStatus.COMPLETED
          : ReviewStatus.REQUIRES_ATTENTION;
      const finalPercentage = processedCount > 0 ? 100 : 0;

      // Mark persona as completed or requiring attention
      await prisma.aIReview.update({
        where: { dealId_personaId: { dealId, personaId } },
        data: {
          status: finalStatus,
          completedAt: new Date(),
          currentDocument: null,
          completionPercentage: finalPercentage,
          lastUpdated: new Date(),
          errorMessage:
            processedCount === 0
              ? "No documents could be processed. Please ensure documents are in supported formats (PDF, TXT, JPG, PNG)."
              : null,
        },
      });

      loggers.aiReview.info(
        {
          dealId,
          personaType,
          processedCount,
          totalDocuments,
          finalStatus,
        },
        "Persona analysis completed for deal"
      );
    } catch (error) {
      loggers.aiReview.error(
        {
          dealId,
          personaType,
          error: (error as Error).message,
        },
        "Persona analysis failed for deal"
      );

      // Mark persona as failed - use proper enum value
      await prisma.aIReview.update({
        where: { dealId_personaId: { dealId, personaId } },
        data: {
          status: ReviewStatus.REQUIRES_ATTENTION, // Use proper enum instead of 'FAILED'
          errorMessage: (error as Error).message,
          lastUpdated: new Date(),
        },
      });
    }
  }

  // Process a single document for a persona
  private static async processDocument(
    dealId: string,
    personaType: PersonaType,
    document: any,
    deal: any
  ): Promise<void> {
    const personaId = await this.getPersonaId(personaType);

    loggers.document.info(
      {
        dealId,
        personaType,
        documentId: document.id,
        documentName: document.originalName,
        fileSize: document.fileSize,
        mimeType: document.mimeType,
      },
      "Starting document processing"
    );

    // Fetch the complete document with fileData
    const fullDocument = await prisma.document.findUnique({
      where: { id: document.id },
    });

    if (!fullDocument || !fullDocument.fileData) {
      throw new Error(`Document data not found for document ${document.id}`);
    }

    // Create document analysis record
    const documentAnalysis = await prisma.documentAnalysis.create({
      data: {
        reviewId: await this.getReviewId(dealId, personaId),
        documentId: document.id,
        analysisStatus: ReviewStatus.IN_PROGRESS, // Use proper enum
        processingOrder: await this.getNextProcessingOrder(dealId, personaId),
        startedAt: new Date(),
      },
    });

    loggers.document.debug(
      {
        documentAnalysisId: documentAnalysis.id,
        documentId: document.id,
      },
      "Created document analysis record"
    );

    try {
      // Get current cumulative analysis
      const currentReview = await prisma.aIReview.findUnique({
        where: { dealId_personaId: { dealId, personaId } },
      });

      const previousAnalysis =
        (currentReview?.analysisResult as unknown as CumulativeAnalysis) ||
        this.createInitialAnalysis();

      // Build context for AI analysis
      const context = {
        deal_type: deal.type,
        jurisdiction: deal.jurisdiction,
        industry: deal.industry,
        deal_value: deal.value ? `$${deal.value}` : undefined,
        document_name: document.originalName,
        previous_analysis: JSON.stringify(previousAnalysis, null, 2),
      };

      // Build prompt
      const prompt = AIPersonaService.buildPrompt(personaType, context);

      // Convert Uint8Array to Buffer for processing
      const fileBuffer = Buffer.from(fullDocument.fileData);

      // Validate document
      const validation = GeminiService.validateDocument(
        fileBuffer,
        fullDocument.mimeType
      );
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Extract text from document
      loggers.gemini.info(
        {
          documentName: fullDocument.originalName,
          contentSize: fileBuffer.length,
          mimeType: fullDocument.mimeType,
        },
        "Extracting text from document"
      );

      const extractionResult = await GeminiService.extractDocumentText(
        fileBuffer,
        fullDocument.mimeType,
        fullDocument.originalName
      );

      loggers.gemini.info(
        {
          documentName: fullDocument.originalName,
          textLength: extractionResult.text.length,
          wordCount: extractionResult.metadata?.wordCount,
          extractionMethod: extractionResult.metadata?.extractionMethod,
        },
        "Text extraction completed"
      );

      // Prepare analysis request with extracted text
      const analysisRequest: DocumentAnalysisRequest = {
        documentContent: extractionResult.text,
        documentName: fullDocument.originalName,
        mimeType: fullDocument.mimeType,
        prompt,
        personaType,
        dealContext: {
          dealType: deal.type,
          jurisdiction: deal.jurisdiction,
          industry: deal.industry,
          dealValue: deal.value ? `$${deal.value}` : undefined,
        },
        extractionMetadata: extractionResult.metadata,
      };

      loggers.gemini.debug(
        {
          documentName: document.originalName,
          textLength: analysisRequest.documentContent.length,
          extractionMethod: extractionResult.metadata?.extractionMethod,
        },
        "Analysis request prepared"
      );

      // Analyze with Gemini (or simulate for testing)
      let result;

      if (
        process.env.NODE_ENV === "development" &&
        process.env.SIMULATE_AI_ANALYSIS === "true"
      ) {
        // Simulate AI analysis for testing
        loggers.gemini.info(
          { documentName: fullDocument.originalName },
          "Simulating AI analysis"
        );
        result = AIReviewOrchestrator.simulateAnalysis(
          personaType,
          fullDocument.originalName
        );

        // Add a small delay to simulate processing time
        await new Promise((resolve) => setTimeout(resolve, 2000));
      } else {
        // Real Gemini analysis
        loggers.gemini.info(
          { documentName: fullDocument.originalName },
          "Sending to Gemini AI for analysis"
        );
        result = await GeminiService.analyzeDocument(analysisRequest);
        loggers.gemini.info(
          { documentName: fullDocument.originalName },
          "Received analysis result from Gemini"
        );
      }

      // Update cumulative analysis
      const updatedAnalysis = this.mergeCumulativeAnalysis(
        previousAnalysis,
        result.analysis,
        document.id,
        document.originalName
      );

      // Update AI review with new cumulative analysis
      await prisma.aIReview.update({
        where: { dealId_personaId: { dealId, personaId } },
        data: {
          analysisResult: updatedAnalysis as any,
          tokenUsage: result.tokenUsage as any,
          lastUpdated: new Date(),
        },
      });

      // Update document analysis as completed
      loggers.document.info(
        {
          documentName: fullDocument.originalName,
          documentAnalysisId: documentAnalysis.id,
        },
        "Updating document analysis as completed"
      );

      await prisma.documentAnalysis.update({
        where: { id: documentAnalysis.id },
        data: {
          analysisStatus: ReviewStatus.COMPLETED, // Use proper enum
          documentInsights: result.analysis.document_insights,
          completedAt: new Date(),
        },
      });

      loggers.document.info(
        {
          documentName: fullDocument.originalName,
        },
        "Document processing completed successfully"
      );
    } catch (error) {
      loggers.document.error(
        {
          documentName: fullDocument.originalName,
          error: (error as Error).message,
        },
        "Document processing failed"
      );

      // Update document analysis as failed
      await prisma.documentAnalysis.update({
        where: { id: documentAnalysis.id },
        data: {
          analysisStatus: ReviewStatus.REQUIRES_ATTENTION, // Use proper enum instead of 'FAILED'
          errorMessage: (error as Error).message,
        },
      });

      throw error;
    }
  }

  // Create initial empty analysis structure
  private static createInitialAnalysis(): CumulativeAnalysis {
    return {
      summary: "Analysis in progress...",
      confidence_score: 0,
      risks: [],
      recommendations: [],
      extracted_data: [],
      document_insights: {},
      overall_risk_level: "LOW",
      completeness_percentage: 0,
    };
  }

  // Merge new analysis into cumulative analysis
  private static mergeCumulativeAnalysis(
    previous: CumulativeAnalysis,
    newAnalysis: any,
    documentId: string,
    documentName: string
  ): CumulativeAnalysis {
    const now = new Date().toISOString();

    // Merge risks (avoid duplicates and clean data)
    const mergedRisks = [...previous.risks];
    newAnalysis.risks?.forEach((risk: any) => {
      // Skip if risk is empty or has obvious placeholder data
      if (
        !risk ||
        !risk.title ||
        risk.title.trim() === "" ||
        risk.title === "Unstructured Risk Identified" ||
        risk.title === "Analysis Parsing Issue"
      ) {
        return;
      }

      const existingRisk = mergedRisks.find((r) => r.title === risk.title);
      if (existingRisk) {
        // Update existing risk - deduplicate source documents
        if (!existingRisk.source_documents.includes(documentName)) {
          existingRisk.source_documents.push(documentName);
        }
        existingRisk.last_updated = now;
        if (risk.severity === "CRITICAL" || risk.severity === "HIGH") {
          existingRisk.severity = risk.severity;
        }
      } else {
        // Add new risk with clean data
        mergedRisks.push({
          id: `risk_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          title: risk.title,
          description: risk.description || risk.title,
          severity: risk.severity || "MEDIUM",
          category: risk.category || "General",
          recommendation: risk.recommendation || "Review recommended",
          source_documents: [documentName],
          first_identified: now,
          last_updated: now,
        });
      }
    });

    // Merge recommendations (avoid duplicates and clean data)
    const mergedRecommendations = [...previous.recommendations];
    newAnalysis.recommendations?.forEach((rec: any) => {
      // Skip if recommendation is empty or has obvious placeholder data
      if (
        !rec ||
        !rec.title ||
        rec.title.trim() === "" ||
        rec.title === "Unstructured Recommendation" ||
        rec.title === "Manual Review Required"
      ) {
        return;
      }

      const existingRec = mergedRecommendations.find(
        (r) => r.title === rec.title
      );
      if (!existingRec) {
        mergedRecommendations.push({
          id: `rec_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          title: rec.title,
          description: rec.description || rec.title,
          priority: rec.priority || "MEDIUM",
          action_required: Boolean(rec.action_required),
          source_documents: [documentName],
        });
      } else {
        // Update existing recommendation - deduplicate source documents
        if (!existingRec.source_documents.includes(documentName)) {
          existingRec.source_documents.push(documentName);
        }
      }
    });

    // Merge extracted data (avoid duplicates and placeholder data)
    const mergedData = [...previous.extracted_data];
    newAnalysis.extracted_data?.forEach((data: any) => {
      // Skip obvious placeholder or empty data
      if (
        !data ||
        !data.field ||
        data.field.trim() === "" ||
        data.field.startsWith("Field ") ||
        data.value === "No value" ||
        !data.value ||
        data.value.trim() === ""
      ) {
        return;
      }

      const existingData = mergedData.find((d) => d.field === data.field);
      if (existingData) {
        // Update with higher confidence data
        if ((data.confidence || 0) > (existingData.confidence || 0)) {
          existingData.value = data.value;
          existingData.confidence = data.confidence;
          existingData.source_document = documentName;
          existingData.verification_status =
            data.verification_status || "PENDING";
        }
      } else {
        mergedData.push({
          field: data.field,
          value: data.value,
          confidence: data.confidence || 50,
          source_document: documentName,
          verification_status: data.verification_status || "PENDING",
        });
      }
    });

    // Add document insights with clean structure
    const documentInsights = {
      ...previous.document_insights,
      [documentId]: {
        processed_at: now,
        key_findings: Array.isArray(newAnalysis.key_findings)
          ? newAnalysis.key_findings.filter(
              (f: string) => f && f.trim() && !f.includes("No findings")
            )
          : [],
        risk_level: ["LOW", "MEDIUM", "HIGH", "CRITICAL"].includes(
          newAnalysis.risk_level
        )
          ? newAnalysis.risk_level
          : "MEDIUM",
        completeness_score: Math.max(
          0,
          Math.min(100, newAnalysis.completeness_score || 50)
        ),
        confidence_score: Math.max(
          0,
          Math.min(100, newAnalysis.confidence_score || 50)
        ),
      },
    };

    // Calculate updated confidence score
    const documentCount = Object.keys(documentInsights).length;
    const avgConfidence = newAnalysis.confidence_score || 50;
    const updatedConfidence = Math.round(
      (previous.confidence_score * (documentCount - 1) + avgConfidence) /
        documentCount
    );

    // Update summary
    const updatedSummary = `Analysis of ${documentCount} documents completed. ${
      newAnalysis.summary || ""
    }`;

    return {
      summary: updatedSummary,
      confidence_score: updatedConfidence,
      risks: mergedRisks,
      recommendations: mergedRecommendations,
      extracted_data: mergedData,
      document_insights: documentInsights,
      overall_risk_level: this.calculateOverallRiskLevel(mergedRisks),
      completeness_percentage: this.calculateCompletenessPercentage(
        mergedData,
        documentInsights
      ),
    };
  }

  // Calculate overall risk level based on individual risks
  private static calculateOverallRiskLevel(
    risks: any[]
  ): "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" {
    if (!risks || risks.length === 0) return "LOW";

    const riskCounts = {
      CRITICAL: risks.filter((r) => r.severity === "CRITICAL").length,
      HIGH: risks.filter((r) => r.severity === "HIGH").length,
      MEDIUM: risks.filter((r) => r.severity === "MEDIUM").length,
      LOW: risks.filter((r) => r.severity === "LOW").length,
    };

    if (riskCounts.CRITICAL > 0) return "CRITICAL";
    if (riskCounts.HIGH > 0) return "HIGH";
    if (riskCounts.MEDIUM > 0) return "MEDIUM";
    return "LOW";
  }

  // Calculate completeness percentage based on extracted data and document insights
  private static calculateCompletenessPercentage(
    extractedData: any[],
    documentInsights: any
  ): number {
    if (!extractedData || extractedData.length === 0) return 0;

    // Calculate based on data quality and document insights
    const dataQuality =
      extractedData.reduce((sum, data) => sum + (data.confidence || 0), 0) /
      extractedData.length;

    // Factor in document insights if available
    let insightQuality = 50; // default
    if (documentInsights && Object.keys(documentInsights).length > 0) {
      const insights = Object.values(documentInsights) as any[];
      insightQuality =
        insights.reduce(
          (sum: number, insight: any) =>
            sum + (insight.completeness_score || 0),
          0
        ) / insights.length;
    }

    return Math.round((dataQuality + insightQuality) / 2);
  }

  // Helper methods
  private static async getPersonaId(personaType: PersonaType): Promise<string> {
    const persona = await prisma.aIPersona.findFirst({
      where: { type: personaType, isActive: true },
    });
    if (!persona) throw new Error(`Persona not found: ${personaType}`);
    return persona.id;
  }

  private static async getReviewId(
    dealId: string,
    personaId: string
  ): Promise<string> {
    const review = await prisma.aIReview.findUnique({
      where: { dealId_personaId: { dealId, personaId } },
    });
    if (!review) throw new Error("Review not found");
    return review.id;
  }

  private static async getNextProcessingOrder(
    dealId: string,
    personaId: string
  ): Promise<number> {
    const count = await prisma.documentAnalysis.count({
      where: {
        review: {
          dealId,
          personaId,
        },
      },
    });
    return count + 1;
  }

  private static async recordDocumentFailure(
    dealId: string,
    personaId: string,
    documentId: string,
    error: Error
  ): Promise<void> {
    loggers.document.warn(
      {
        dealId,
        documentId,
        error: error.message,
      },
      "Recording document failure"
    );

    const reviewId = await this.getReviewId(dealId, personaId);

    await prisma.documentAnalysis.upsert({
      where: {
        reviewId_documentId: { reviewId, documentId },
      },
      update: {
        analysisStatus: ReviewStatus.REQUIRES_ATTENTION, // Use proper enum instead of 'FAILED'
        errorMessage: error.message,
        retryCount: { increment: 1 },
      },
      create: {
        reviewId,
        documentId,
        analysisStatus: ReviewStatus.REQUIRES_ATTENTION, // Use proper enum instead of 'FAILED'
        errorMessage: error.message,
        processingOrder: await this.getNextProcessingOrder(dealId, personaId),
        retryCount: 1,
      },
    });

    loggers.document.info({ documentId }, "Document failure recorded");
  }

  // Clean malformed analysis result data
  private static cleanAnalysisResult(analysisResult: any): any {
    if (!analysisResult) return null;

    const cleaned = { ...analysisResult };

    // Clean risks array
    if (Array.isArray(cleaned.risks)) {
      cleaned.risks = cleaned.risks
        .map((risk: any) => {
          if (typeof risk !== "object") return null;

          // Check if risk has character array properties (malformed data)
          const hasCharacterArrays = Object.keys(risk).some(
            (key) => !isNaN(Number(key)) && typeof risk[key] === "string"
          );

          if (hasCharacterArrays) {
            // Try to reconstruct the string from character array
            const chars = Object.keys(risk)
              .filter((key) => !isNaN(Number(key)))
              .sort((a, b) => Number(a) - Number(b))
              .map((key) => risk[key]);

            const reconstructedText = chars.join("");

            return {
              id: risk.id || `risk_${Date.now()}_cleaned`,
              title:
                reconstructedText.length > 100
                  ? reconstructedText.substring(0, 100) + "..."
                  : reconstructedText,
              description: reconstructedText,
              severity: risk.severity || "MEDIUM",
              category: risk.category || "General",
              recommendation:
                "Manual review recommended due to data parsing issues",
              source_documents: Array.isArray(risk.source_documents)
                ? risk.source_documents
                : [],
            };
          }

          return risk;
        })
        .filter(Boolean);
    }

    // Clean recommendations array
    if (Array.isArray(cleaned.recommendations)) {
      cleaned.recommendations = cleaned.recommendations
        .map((rec: any) => {
          if (typeof rec !== "object") return null;

          // Check if recommendation has character array properties
          const hasCharacterArrays = Object.keys(rec).some(
            (key) => !isNaN(Number(key)) && typeof rec[key] === "string"
          );

          if (hasCharacterArrays) {
            // Try to reconstruct the string from character array
            const chars = Object.keys(rec)
              .filter((key) => !isNaN(Number(key)))
              .sort((a, b) => Number(a) - Number(b))
              .map((key) => rec[key]);

            const reconstructedText = chars.join("");

            return {
              id: rec.id || `rec_${Date.now()}_cleaned`,
              title:
                reconstructedText.length > 100
                  ? reconstructedText.substring(0, 100) + "..."
                  : reconstructedText,
              description: reconstructedText,
              priority: rec.priority || "MEDIUM",
              action_required: Boolean(rec.action_required),
              source_documents: Array.isArray(rec.source_documents)
                ? rec.source_documents
                : [],
            };
          }

          return rec;
        })
        .filter(Boolean);
    }

    return cleaned;
  }

  // Get AI review results for a deal (includes progress and status)
  static async getReviewResults(dealId: string) {
    const reviews = await prisma.aIReview.findMany({
      where: { dealId },
      include: {
        persona: true,
        documentAnalyses: {
          include: {
            document: true,
          },
        },
      },
    });

    const totalDocuments = await prisma.document.count({ where: { dealId } });
    let totalProcessedDocuments = 0;
    let overallProgress = 0;

    const results = reviews.map((review) => {
      const completedAnalyses = review.documentAnalyses.filter(
        (da) => da.analysisStatus === "COMPLETED"
      ).length;

      totalProcessedDocuments += completedAnalyses;

      return {
        persona: {
          id: review.persona.id,
          name: review.persona.name,
          type: review.persona.type,
        },
        status: review.status,
        progress: review.completionPercentage,
        analysis: this.cleanAnalysisResult(review.analysisResult),
        documentsAnalyzed: completedAnalyses,
        totalDocuments,
        currentDocument: review.currentDocument,
        errorMessage: review.errorMessage,
        startedAt: review.startedAt,
        completedAt: review.completedAt,
        lastUpdated: review.lastUpdated,
        estimatedTimeRemaining: Math.max(
          0,
          (totalDocuments - completedAnalyses) * 45
        ), // 45 seconds per doc
      };
    });

    // Calculate overall progress
    if (totalDocuments > 0 && reviews.length > 0) {
      overallProgress = Math.round(
        (totalProcessedDocuments / (totalDocuments * reviews.length)) * 100
      );
    }

    // Check if any review is running
    const isRunning =
      this.activeReviews.has(dealId) ||
      reviews.some((r) => r.status === "IN_PROGRESS");

    return {
      results,
      summary: {
        dealId,
        isRunning,
        overallProgress,
        totalDocuments,
        totalProcessedDocuments,
        totalPersonas: reviews.length,
        completedPersonas: results.filter((r) => r.status === "COMPLETED")
          .length,
        failedPersonas: results.filter((r) => r.status === "REQUIRES_ATTENTION")
          .length,
        estimatedCompletionTime: isRunning
          ? new Date(
              Date.now() +
                (totalDocuments * reviews.length - totalProcessedDocuments) *
                  45 *
                  1000
            )
          : null,
      },
    };
  }

  // Reset stuck AI review
  static async resetReview(dealId: string): Promise<void> {
    loggers.aiReview.info({ dealId }, "Resetting AI review for deal");

    try {
      // Remove from active reviews
      this.activeReviews.delete(dealId);

      // Reset all AI reviews for this deal
      await prisma.aIReview.updateMany({
        where: { dealId },
        data: {
          status: ReviewStatus.PENDING,
          processedDocuments: 0,
          completionPercentage: 0,
          currentDocument: null,
          startedAt: null,
          completedAt: null,
          lastUpdated: new Date(),
          errorMessage: null,
          retryCount: 0,
        },
      });

      // Reset all document analyses for this deal
      await prisma.documentAnalysis.updateMany({
        where: {
          review: {
            dealId,
          },
        },
        data: {
          analysisStatus: ReviewStatus.PENDING,
          startedAt: null,
          completedAt: null,
          errorMessage: null,
          retryCount: 0,
        },
      });

      loggers.aiReview.info({ dealId }, "AI review reset completed for deal");
    } catch (error) {
      loggers.aiReview.error(
        {
          dealId,
          error: (error as Error).message,
        },
        "Failed to reset AI review for deal"
      );
      throw error;
    }
  }

  // Simulate AI analysis for testing
  private static simulateAnalysis(
    personaType: PersonaType,
    documentName: string
  ) {
    const mockAnalysis = {
      summary: `${personaType} analysis of ${documentName} completed successfully.`,
      confidence_score: Math.floor(Math.random() * 20) + 80, // 80-100%
      risks: [
        {
          title: `${personaType} Risk Identified`,
          description: `Potential ${personaType.toLowerCase()} risk found in ${documentName}`,
          severity: ["LOW", "MEDIUM", "HIGH"][
            Math.floor(Math.random() * 3)
          ] as any,
          category: personaType,
          recommendation: `Review ${personaType.toLowerCase()} aspects of this document`,
        },
      ],
      recommendations: [
        {
          title: `${personaType} Recommendation`,
          description: `Recommended action for ${personaType.toLowerCase()} review`,
          priority: ["LOW", "MEDIUM", "HIGH"][
            Math.floor(Math.random() * 3)
          ] as any,
          action_required: Math.random() > 0.5,
        },
      ],
      extracted_data: [
        {
          field: `${personaType} Key Metric`,
          value: `Sample value from ${documentName}`,
          confidence: Math.floor(Math.random() * 20) + 80,
          source_document: documentName,
          verification_status: "VERIFIED" as any,
        },
      ],
      document_insights: {
        key_findings: [`Key finding from ${personaType} perspective`],
        risk_level: ["LOW", "MEDIUM", "HIGH"][
          Math.floor(Math.random() * 3)
        ] as any,
        completeness_score: Math.floor(Math.random() * 20) + 80,
        document_quality: "GOOD" as any,
      },
    };

    return {
      analysis: mockAnalysis,
      tokenUsage: {
        promptTokens: 1000,
        completionTokens: 500,
        totalTokens: 1500,
        estimatedCost: 0.01,
      },
    };
  }
}
