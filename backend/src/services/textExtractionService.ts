import pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { loggers } from '../config/logger';

export interface TextExtractionResult {
  text: string;
  metadata?: {
    pageCount?: number;
    wordCount?: number;
    extractionMethod: string;
    originalMimeType: string;
  };
}

export class TextExtractionService {
  private static readonly MAX_TEXT_LENGTH = 100000; // 100KB of text
  private static readonly MIN_TEXT_LENGTH = 10; // Minimum meaningful text

  /**
   * Extract text from various document formats
   */
  static async extractText(buffer: Buffer, mimeType: string, filename: string): Promise<TextExtractionResult> {
    loggers.document.info({
      filename,
      mimeType,
      bufferSize: buffer.length
    }, 'Starting text extraction');

    try {
      let result: TextExtractionResult;

      switch (mimeType) {
        case 'application/pdf':
          result = await this.extractFromPDF(buffer);
          break;

        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          result = await this.extractFromDOCX(buffer);
          break;

        case 'application/msword':
          result = await this.extractFromDOC(buffer);
          break;

        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        case 'application/vnd.ms-excel':
          result = await this.extractFromExcel(buffer);
          break;

        case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        case 'application/vnd.ms-powerpoint':
          result = await this.extractFromPowerPoint(buffer);
          break;

        case 'text/plain':
          result = this.extractFromText(buffer);
          break;

        case 'text/csv':
          result = this.extractFromCSV(buffer);
          break;

        case 'application/rtf':
          result = await this.extractFromRTF(buffer);
          break;

        default:
          // For unsupported formats, try textract as fallback
          result = await this.extractWithTextract(buffer, mimeType);
          break;
      }

      // Add metadata
      result.metadata = {
        ...result.metadata,
        originalMimeType: mimeType,
        wordCount: this.countWords(result.text),
        extractionMethod: result.metadata?.extractionMethod || 'unknown'
      };

      // Validate extracted text
      this.validateExtractedText(result.text, filename);

      loggers.document.info({
        filename,
        mimeType,
        textLength: result.text.length,
        wordCount: result.metadata.wordCount,
        extractionMethod: result.metadata.extractionMethod
      }, 'Text extraction completed successfully');

      return result;

    } catch (error) {
      loggers.document.error({
        filename,
        mimeType,
        error: (error as Error).message
      }, 'Text extraction failed');

      throw new Error(`Failed to extract text from ${filename}: ${(error as Error).message}`);
    }
  }

  /**
   * Extract text from PDF files
   */
  private static async extractFromPDF(buffer: Buffer): Promise<TextExtractionResult> {
    try {
      const data = await pdfParse(buffer);

      return {
        text: data.text,
        metadata: {
          pageCount: data.numpages,
          extractionMethod: 'pdf-parse',
          originalMimeType: 'application/pdf'
        }
      };
    } catch (error) {
      throw new Error(`PDF extraction failed: ${(error as Error).message}`);
    }
  }

  /**
   * Extract text from DOCX files
   */
  private static async extractFromDOCX(buffer: Buffer): Promise<TextExtractionResult> {
    try {
      const result = await mammoth.extractRawText({ buffer });

      return {
        text: result.value,
        metadata: {
          extractionMethod: 'mammoth',
          originalMimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
      };
    } catch (error) {
      throw new Error(`DOCX extraction failed: ${(error as Error).message}`);
    }
  }

  /**
   * Extract text from DOC files (legacy Word format)
   */
  private static async extractFromDOC(buffer: Buffer): Promise<TextExtractionResult> {
    // For legacy DOC files, we'll use textract as fallback
    return this.extractWithTextract(buffer, 'application/msword');
  }

  /**
   * Extract text from Excel files
   */
  private static async extractFromExcel(buffer: Buffer): Promise<TextExtractionResult> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      let text = '';

      // Extract text from all sheets
      workbook.SheetNames.forEach(sheetName => {
        const sheet = workbook.Sheets[sheetName];
        const sheetText = XLSX.utils.sheet_to_txt(sheet);
        text += `\n--- Sheet: ${sheetName} ---\n${sheetText}\n`;
      });

      return {
        text: text.trim(),
        metadata: {
          extractionMethod: 'xlsx',
          pageCount: workbook.SheetNames.length,
          originalMimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      };
    } catch (error) {
      throw new Error(`Excel extraction failed: ${(error as Error).message}`);
    }
  }

  /**
   * Extract text from PowerPoint files
   */
  private static async extractFromPowerPoint(buffer: Buffer): Promise<TextExtractionResult> {
    // For PowerPoint files, we'll use textract as fallback
    return this.extractWithTextract(buffer, 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
  }

  /**
   * Extract text from plain text files
   */
  private static extractFromText(buffer: Buffer): TextExtractionResult {
    const text = buffer.toString('utf-8');

    return {
      text,
      metadata: {
        extractionMethod: 'utf-8-decode',
        originalMimeType: 'text/plain'
      }
    };
  }

  /**
   * Extract text from CSV files
   */
  private static extractFromCSV(buffer: Buffer): TextExtractionResult {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
      const text = XLSX.utils.sheet_to_txt(firstSheet);

      return {
        text,
        metadata: {
          extractionMethod: 'xlsx-csv',
          originalMimeType: 'text/csv'
        }
      };
    } catch (error) {
      // Fallback to plain text
      return this.extractFromText(buffer);
    }
  }

  /**
   * Extract text from RTF files
   */
  private static async extractFromRTF(buffer: Buffer): Promise<TextExtractionResult> {
    // For RTF files, we'll use textract as fallback
    return this.extractWithTextract(buffer, 'application/rtf');
  }

  /**
   * Fallback extraction using textract
   */
  private static async extractWithTextract(buffer: Buffer, mimeType: string): Promise<TextExtractionResult> {
    return new Promise((resolve, reject) => {
      const textract = require('textract');

      textract.fromBufferWithMime(mimeType, buffer, (error: any, text: string) => {
        if (error) {
          reject(new Error(`Textract extraction failed: ${error.message}`));
          return;
        }

        resolve({
          text: text || '',
          metadata: {
            extractionMethod: 'textract',
            originalMimeType: mimeType
          }
        });
      });
    });
  }

  /**
   * Validate extracted text
   */
  private static validateExtractedText(text: string, filename: string): void {
    if (!text || text.trim().length < this.MIN_TEXT_LENGTH) {
      throw new Error(`No meaningful text could be extracted from ${filename}`);
    }

    if (text.length > this.MAX_TEXT_LENGTH) {
      loggers.document.warn({
        filename,
        textLength: text.length,
        maxLength: this.MAX_TEXT_LENGTH
      }, 'Extracted text exceeds maximum length, will be truncated');
    }
  }

  /**
   * Count words in text
   */
  private static countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Truncate text if it's too long
   */
  static truncateText(text: string, maxLength: number = this.MAX_TEXT_LENGTH): string {
    if (text.length <= maxLength) {
      return text;
    }

    // Try to truncate at a sentence boundary
    const truncated = text.substring(0, maxLength);
    const lastSentence = truncated.lastIndexOf('.');

    if (lastSentence > maxLength * 0.8) {
      return truncated.substring(0, lastSentence + 1) + '\n\n[Text truncated for analysis]';
    }

    // Fallback to word boundary
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > maxLength * 0.8) {
      return truncated.substring(0, lastSpace) + '\n\n[Text truncated for analysis]';
    }

    return truncated + '\n\n[Text truncated for analysis]';
  }

  /**
   * Check if a MIME type is supported for text extraction
   */
  static isSupportedMimeType(mimeType: string): boolean {
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint',
      'text/plain',
      'text/csv',
      'application/rtf',
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/heic',
      'image/heif'
    ];

    return supportedTypes.includes(mimeType);
  }
}
