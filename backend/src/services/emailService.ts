import sgMail from '@sendgrid/mail';
import config from '../config';
import { EMAIL_TEMPLATES } from '../constants';

// Initialize SendGrid
if (config.email.apiKey) {
  sgMail.setApiKey(config.email.apiKey);
}

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface PasswordResetEmailData {
  firstName: string;
  resetLink: string;
}

export interface WelcomeEmailData {
  firstName: string;
  loginLink: string;
}

export class EmailService {
  private static isConfigured(): boolean {
    return !!config.email.apiKey && config.email.apiKey !== '';
  }

  private static async sendEmail(options: EmailOptions): Promise<void> {
    if (!this.isConfigured()) {
      console.log('📧 Email not configured - would send:', {
        to: options.to,
        subject: options.subject,
        preview: options.text?.substring(0, 100) || options.html.substring(0, 100)
      });
      return;
    }

    try {
      const msg = {
        to: options.to,
        from: {
          email: config.email.from,
          name: config.email.fromName,
        },
        subject: options.subject,
        html: options.html,
        text: options.text || options.html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
      };

      await sgMail.send(msg);
      console.log(`✅ Email sent successfully to ${options.to}`);
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      throw new Error('Failed to send email');
    }
  }

  static async sendPasswordResetEmail(email: string, data: PasswordResetEmailData): Promise<void> {
    const subject = 'Reset Your Password - Due Diligence Platform';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Password Reset</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .button:hover { background: #0056b3; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Password Reset Request</h1>
            </div>
            <div class="content">
              <p>Hello ${data.firstName},</p>
              
              <p>We received a request to reset your password for your Due Diligence Platform account.</p>
              
              <p>Click the button below to reset your password:</p>
              
              <div style="text-align: center;">
                <a href="${data.resetLink}" class="button">Reset Password</a>
              </div>
              
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                ${data.resetLink}
              </p>
              
              <div class="warning">
                <strong>Security Notice:</strong>
                <ul>
                  <li>This link will expire in 24 hours</li>
                  <li>If you didn't request this reset, please ignore this email</li>
                  <li>Never share this link with anyone</li>
                </ul>
              </div>
              
              <p>If you have any questions, please contact our support team.</p>
              
              <p>Best regards,<br>
              The Due Diligence Platform Team</p>
            </div>
            <div class="footer">
              <p>This email was sent from an automated system. Please do not reply to this email.</p>
              <p>&copy; 2024 Due Diligence Platform. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      Hello ${data.firstName},

      We received a request to reset your password for your Due Diligence Platform account.

      Please click the following link to reset your password:
      ${data.resetLink}

      This link will expire in 24 hours.

      If you didn't request this reset, please ignore this email.

      Best regards,
      The Due Diligence Platform Team
    `;

    await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  static async sendWelcomeEmail(email: string, data: WelcomeEmailData): Promise<void> {
    const subject = 'Welcome to Due Diligence Platform';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .button:hover { background: #218838; }
            .features { background: #f8f9fa; padding: 20px; border-radius: 4px; margin: 20px 0; }
            .features ul { margin: 0; padding-left: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Due Diligence Platform!</h1>
            </div>
            <div class="content">
              <p>Hello ${data.firstName},</p>
              
              <p>Welcome to the Due Diligence Platform! We're excited to have you on board.</p>
              
              <div class="features">
                <h3>What you can do with our platform:</h3>
                <ul>
                  <li>Manage comprehensive due diligence processes</li>
                  <li>Collaborate with team members in real-time</li>
                  <li>Generate detailed reports and analytics</li>
                  <li>Track progress with advanced dashboards</li>
                  <li>Secure document management and sharing</li>
                </ul>
              </div>
              
              <p>Ready to get started? Click the button below to access your account:</p>
              
              <div style="text-align: center;">
                <a href="${data.loginLink}" class="button">Access Your Account</a>
              </div>
              
              <p>If you have any questions or need assistance, our support team is here to help.</p>
              
              <p>Best regards,<br>
              The Due Diligence Platform Team</p>
            </div>
            <div class="footer">
              <p>This email was sent from an automated system. Please do not reply to this email.</p>
              <p>&copy; 2024 Due Diligence Platform. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      Hello ${data.firstName},

      Welcome to the Due Diligence Platform! We're excited to have you on board.

      What you can do with our platform:
      - Manage comprehensive due diligence processes
      - Collaborate with team members in real-time
      - Generate detailed reports and analytics
      - Track progress with advanced dashboards
      - Secure document management and sharing

      Ready to get started? Visit: ${data.loginLink}

      If you have any questions or need assistance, our support team is here to help.

      Best regards,
      The Due Diligence Platform Team
    `;

    await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  static async sendEmailVerification(email: string, verificationLink: string, firstName: string): Promise<void> {
    const subject = 'Verify Your Email - Due Diligence Platform';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Email Verification</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #17a2b8; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .button:hover { background: #138496; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Verify Your Email Address</h1>
            </div>
            <div class="content">
              <p>Hello ${firstName},</p>
              
              <p>Thank you for registering with Due Diligence Platform! To complete your registration, please verify your email address.</p>
              
              <div style="text-align: center;">
                <a href="${verificationLink}" class="button">Verify Email Address</a>
              </div>
              
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                ${verificationLink}
              </p>
              
              <p>This verification link will expire in 24 hours.</p>
              
              <p>If you didn't create an account with us, please ignore this email.</p>
              
              <p>Best regards,<br>
              The Due Diligence Platform Team</p>
            </div>
            <div class="footer">
              <p>This email was sent from an automated system. Please do not reply to this email.</p>
              <p>&copy; 2024 Due Diligence Platform. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    await this.sendEmail({
      to: email,
      subject,
      html,
    });
  }
}
