import { Document, PersonaType } from '@prisma/client';
import prisma from '../config/database';
import { loggers } from '../config/logger';

export interface DocumentPriority {
  documentId: string;
  priority: number; // Higher number = higher priority
  estimatedProcessingTime: number; // in seconds
  documentType: string;
  fileSize: number;
}

export interface QueueStatus {
  dealId: string;
  personaType: PersonaType;
  totalDocuments: number;
  processedDocuments: number;
  currentDocument?: string;
  queuePosition: number;
  estimatedTimeRemaining: number; // in seconds
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
}

export class DocumentQueueService {
  // Document type priority mapping
  private static readonly DOCUMENT_TYPE_PRIORITIES: Record<string, number> = {
    // High priority documents (analyzed first)
    'financial_statement': 100,
    'balance_sheet': 95,
    'income_statement': 95,
    'cash_flow': 95,
    'legal_contract': 90,
    'merger_agreement': 90,
    'loan_agreement': 85,
    'audit_report': 85,

    // Medium priority documents
    'operational_report': 70,
    'compliance_certificate': 70,
    'technical_specification': 65,
    'hr_policy': 60,
    'insurance_policy': 60,

    // Lower priority documents
    'supporting_document': 40,
    'appendix': 30,
    'correspondence': 20,
    'other': 10
  };

  // Persona-specific document relevance
  private static readonly PERSONA_DOCUMENT_RELEVANCE = {
    FINANCIAL: ['financial_statement', 'balance_sheet', 'income_statement', 'cash_flow', 'audit_report'],
    LEGAL: ['legal_contract', 'merger_agreement', 'loan_agreement', 'compliance_certificate'],
    OPERATIONAL: ['operational_report', 'hr_policy', 'insurance_policy'],
    HR: ['hr_policy', 'employment_contract', 'organizational_chart'],
    COMMERCIAL_MARKET: ['market_analysis', 'business_plan', 'customer_contracts'],
    IT_TECHNOLOGY: ['technical_specification', 'it_policy', 'cybersecurity_report'],
    ESG: ['esg_report', 'sustainability_report', 'environmental_report'],
    TAX: ['tax_return', 'tax_assessment', 'transfer_pricing_report'],
    STRATEGIC_FIT: ['business_plan', 'market_analysis', 'strategic_plan'],
    REGULATORY: ['regulatory_filing', 'license', 'compliance_certificate'],
    INSURANCE: ['insurance_policy', 'claims_report', 'coverage_analysis'],
    INTELLECTUAL_PROPERTY: ['patent_filing', 'trademark_registration', 'ip_agreement'],
    REAL_ESTATE_ASSET: ['property_deed', 'valuation_report', 'lease_agreement'],
    CUSTOMER_SUPPLIER: ['customer_contracts', 'supplier_agreements', 'vendor_analysis'],
    COMPLIANCE_RISK: ['compliance_certificate', 'audit_report', 'risk_assessment']
  };

  // Create prioritized document queue for a persona
  static async createDocumentQueue(dealId: string, personaType: PersonaType): Promise<DocumentPriority[]> {
    console.log(`📋 Creating document queue for ${personaType} analyst on deal ${dealId}`);

    // Get all documents for the deal
    const documents = await prisma.document.findMany({
      where: { dealId },
      orderBy: { createdAt: 'asc' }
    });

    if (documents.length === 0) {
      console.log(`⚠️ No documents found for deal ${dealId}`);
      return [];
    }

    // Calculate priority for each document
    const prioritizedDocuments = documents.map(doc => {
      const priority = this.calculateDocumentPriority(doc, personaType);
      const estimatedTime = this.estimateProcessingTime(doc);

      return {
        documentId: doc.id,
        priority,
        estimatedProcessingTime: estimatedTime,
        documentType: this.inferDocumentType(doc),
        fileSize: doc.fileSize
      };
    });

    // Sort by priority (highest first)
    prioritizedDocuments.sort((a, b) => b.priority - a.priority);

    console.log(`✅ Created queue with ${prioritizedDocuments.length} documents for ${personaType}`);
    console.log(`📊 Queue priorities:`, prioritizedDocuments.map(d => ({
      id: d.documentId.substring(0, 8),
      priority: d.priority,
      type: d.documentType
    })));

    return prioritizedDocuments;
  }

  // Calculate document priority for specific persona
  private static calculateDocumentPriority(document: Document, personaType: PersonaType): number {
    const documentType = this.inferDocumentType(document);
    let basePriority = this.DOCUMENT_TYPE_PRIORITIES[documentType] || 10;

    // Boost priority if document is relevant to this persona
    const relevantTypes = this.PERSONA_DOCUMENT_RELEVANCE[personaType] || [];
    if (relevantTypes.includes(documentType)) {
      basePriority += 20;
    }

    // Adjust based on file size (smaller files processed first for quick wins)
    const sizeMB = document.fileSize / (1024 * 1024);
    if (sizeMB < 1) {
      basePriority += 10; // Small files get priority boost
    } else if (sizeMB > 5) {
      basePriority -= 5; // Large files get slight penalty
    }

    // Boost priority for required documents
    if (document.status === 'UPLOADED') {
      basePriority += 5;
    }

    return Math.max(1, basePriority); // Ensure minimum priority of 1
  }

  // Infer document type from filename and metadata
  private static inferDocumentType(document: Document): string {
    const filename = document.originalName.toLowerCase();
    const mimeType = document.mimeType;

    // Financial documents
    if (filename.includes('financial') || filename.includes('statement')) {
      if (filename.includes('balance')) return 'balance_sheet';
      if (filename.includes('income') || filename.includes('p&l') || filename.includes('profit')) return 'income_statement';
      if (filename.includes('cash') || filename.includes('flow')) return 'cash_flow';
      return 'financial_statement';
    }

    // Legal documents
    if (filename.includes('contract') || filename.includes('agreement')) {
      if (filename.includes('merger') || filename.includes('acquisition')) return 'merger_agreement';
      if (filename.includes('loan') || filename.includes('credit')) return 'loan_agreement';
      return 'legal_contract';
    }

    // Audit and compliance
    if (filename.includes('audit')) return 'audit_report';
    if (filename.includes('compliance') || filename.includes('certificate')) return 'compliance_certificate';

    // Operational documents
    if (filename.includes('operational') || filename.includes('operation')) return 'operational_report';
    if (filename.includes('hr') || filename.includes('human') || filename.includes('employee')) return 'hr_policy';
    if (filename.includes('technical') || filename.includes('spec')) return 'technical_specification';
    if (filename.includes('insurance')) return 'insurance_policy';

    // Supporting documents
    if (filename.includes('appendix') || filename.includes('exhibit')) return 'appendix';
    if (filename.includes('correspondence') || filename.includes('email') || filename.includes('letter')) return 'correspondence';
    if (filename.includes('supporting') || filename.includes('support')) return 'supporting_document';

    return 'other';
  }

  // Estimate processing time based on document characteristics
  private static estimateProcessingTime(document: Document): number {
    const sizeMB = document.fileSize / (1024 * 1024);
    const mimeType = document.mimeType;

    let baseTime = 30; // 30 seconds base time

    // Adjust for file size
    if (sizeMB < 0.5) {
      baseTime = 20; // Small files
    } else if (sizeMB > 2) {
      baseTime = 60; // Large files
    }

    // Adjust for file type complexity
    if (mimeType.includes('pdf')) {
      baseTime += 10; // PDFs take longer to process
    } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
      baseTime += 15; // Spreadsheets are complex
    } else if (mimeType.includes('image')) {
      baseTime += 20; // Images require OCR
    }

    return baseTime;
  }

  // Get next document to process for a persona
  static async getNextDocument(dealId: string, personaType: PersonaType): Promise<Document | null> {
    loggers.queue.debug({ dealId, personaType }, 'Getting next document for persona');

    // Get the AI review record
    const aiReview = await prisma.aIReview.findUnique({
      where: {
        dealId_personaId: { dealId, personaId: await this.getPersonaId(personaType) }
      },
      include: {
        documentAnalyses: true
      }
    });

    if (!aiReview) {
      loggers.queue.warn({ dealId, personaType }, 'No AI review found for persona on deal');
      return null;
    }

    // Get processed document IDs (only those that are actually completed)
    const processedDocumentIds = aiReview.documentAnalyses
      .filter(da => da.analysisStatus === 'COMPLETED')
      .map(da => da.documentId);

    // Get all documents for the deal (including fileData for processing)
    const allDocuments = await prisma.document.findMany({
      where: { dealId },
      include: {
        // Only include fileData when we need it for processing
        // This is a large field so we're selective about when to include it
      }
    });

    loggers.queue.info({
      dealId,
      personaType,
      totalDocuments: allDocuments.length,
      processedDocuments: processedDocumentIds.length,
      documentSummary: allDocuments.map(doc => ({
        id: doc.id.substring(0, 8),
        name: doc.originalName,
        size: doc.fileSize,
        type: doc.mimeType
      }))
    }, 'Document queue status');

    // Filter out already processed documents
    const unprocessedDocuments = allDocuments.filter(doc =>
      !processedDocumentIds.includes(doc.id)
    );

    if (unprocessedDocuments.length === 0) {
      loggers.queue.info({ dealId, personaType }, 'All documents processed for persona');
      return null;
    }

    // Create queue and get highest priority document
    const queue = await this.createDocumentQueue(dealId, personaType);
    const nextDocumentId = queue.find(item =>
      unprocessedDocuments.some(doc => doc.id === item.documentId)
    )?.documentId;

    if (!nextDocumentId) {
      loggers.queue.warn({ dealId, personaType }, 'No next document found in queue');
      return null;
    }

    const nextDocument = unprocessedDocuments.find(doc => doc.id === nextDocumentId);

    loggers.queue.info({
      dealId,
      personaType,
      documentId: nextDocument?.id.substring(0, 8),
      documentName: nextDocument?.originalName
    }, 'Selected next document for processing');

    return nextDocument || null;
  }

  // Get queue status for a persona
  static async getQueueStatus(dealId: string, personaType: PersonaType): Promise<QueueStatus | null> {
    const aiReview = await prisma.aIReview.findUnique({
      where: {
        dealId_personaId: { dealId, personaId: await this.getPersonaId(personaType) }
      },
      include: {
        documentAnalyses: true
      }
    });

    if (!aiReview) {
      return null;
    }

    const totalDocuments = await prisma.document.count({
      where: { dealId }
    });

    const processedDocuments = aiReview.documentAnalyses.filter(
      da => da.analysisStatus === 'COMPLETED'
    ).length;

    const currentDocument = aiReview.currentDocument;
    const queuePosition = Math.max(0, totalDocuments - processedDocuments - 1);

    // Estimate remaining time
    const remainingDocuments = totalDocuments - processedDocuments;
    const avgProcessingTime = 45; // seconds per document
    const estimatedTimeRemaining = remainingDocuments * avgProcessingTime;

    return {
      dealId,
      personaType,
      totalDocuments,
      processedDocuments,
      currentDocument: currentDocument || undefined,
      queuePosition,
      estimatedTimeRemaining,
      status: aiReview.status as any
    };
  }

  // Helper to get persona ID by type
  private static async getPersonaId(personaType: PersonaType): Promise<string> {
    const persona = await prisma.aIPersona.findFirst({
      where: { type: personaType, isActive: true }
    });

    if (!persona) {
      throw new Error(`Persona not found: ${personaType}`);
    }

    return persona.id;
  }

  // Get overall queue status for all personas
  static async getAllQueueStatuses(dealId: string): Promise<QueueStatus[]> {
    const personas = await prisma.aIPersona.findMany({
      where: { isActive: true }
    });

    const statuses = await Promise.all(
      personas.map(persona => this.getQueueStatus(dealId, persona.type))
    );

    return statuses.filter(status => status !== null) as QueueStatus[];
  }
}
