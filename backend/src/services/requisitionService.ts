import { DealType, Prisma } from '@prisma/client';
import prisma from '../config/database';
import { ERROR_MESSAGES } from '../constants';
import {
  RequisitionTemplate,
  DealRequisition,
  CreateRequisitionTemplateRequest,
  UpdateDealRequisitionRequest,
  CreateDealRequisitionRequest,
  RequisitionTemplateListQuery,
  RequisitionTemplateListResponse,
  RequisitionCustomData,
  RequisitionValidationResult,
  RequisitionStats,
  RequisitionProgress,
} from '../types/requisition';
import { DEFAULT_REQUISITION_TEMPLATES } from '../data/defaultRequisitionTemplates';

export class RequisitionService {
  // Get all requisition templates with filtering
  static async getRequisitionTemplates(
    query: RequisitionTemplateListQuery,
    organizationId: string
  ): Promise<RequisitionTemplateListResponse> {
    const {
      page = 1,
      limit = 20,
      dealType,
      industry,
      search,
      isDefault,
    } = query;

    const skip = (page - 1) * limit;
    const take = Math.min(limit, 100);

    const where: Prisma.RequisitionTemplateWhereInput = {
      OR: [
        { createdBy: { organizationId } }, // User's organization templates
        { isDefault: true }, // Default templates
      ],
      ...(dealType && { dealType }),
      ...(industry && { industry: { contains: industry, mode: 'insensitive' } }),
      // Note: isDefault field doesn't exist in current schema, using isActive instead
      ...(isDefault !== undefined && { isActive: isDefault }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ],
      }),
    };

    const total = await prisma.requisitionTemplate.count({ where });

    const templates = await prisma.requisitionTemplate.findMany({
      where,
      skip,
      take,
      orderBy: [
        { isActive: 'desc' }, // Active templates first
        { createdAt: 'desc' },
      ],
      include: {
        categories: {
          include: {
            items: true,
          },
          orderBy: { orderIndex: 'asc' },
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(total / take);

    return {
      templates: templates.map(template => this.formatRequisitionTemplate(template)),
      pagination: {
        page,
        limit: take,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  // Get requisition template by ID
  static async getRequisitionTemplateById(
    templateId: string,
    organizationId: string
  ): Promise<RequisitionTemplate> {
    const template = await prisma.requisitionTemplate.findFirst({
      where: {
        id: templateId,
        OR: [
          { createdBy: { organizationId } },
          { isActive: true }, // Using isActive instead of isDefault
        ],
      },
      include: {
        categories: {
          include: {
            items: true,
          },
          orderBy: { orderIndex: 'asc' },
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!template) {
      throw new Error(ERROR_MESSAGES.NOT_FOUND);
    }

    return this.formatRequisitionTemplate(template);
  }

  // Create custom requisition template
  static async createRequisitionTemplate(
    data: CreateRequisitionTemplateRequest,
    createdById: string
  ): Promise<RequisitionTemplate> {
    const template = await prisma.requisitionTemplate.create({
      data: {
        name: data.name,
        dealType: data.dealType,
        industry: data.industry,
        description: data.description,
        isActive: data.isDefault || false, // Using isActive instead of isDefault
        createdById,
        categories: {
          create: data.categories.map(category => ({
            name: category.name,
            description: category.description,
            orderIndex: category.orderIndex,
            isRequired: category.isRequired,
            items: {
              create: category.items.map(item => ({
                name: item.name,
                description: item.description,
                isRequired: item.isRequired,
                orderIndex: item.orderIndex,
                acceptedFormats: item.acceptedFormats,
                maxFileSize: item.maxFileSize,
                examples: item.examples,
              })),
            },
          })),
        },
      },
      include: {
        categories: {
          include: {
            items: true,
          },
          orderBy: { orderIndex: 'asc' },
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    return this.formatRequisitionTemplate(template);
  }

  // Get deal requisition
  static async getDealRequisition(
    dealId: string,
    userId: string
  ): Promise<DealRequisition | null> {
    const dealRequisition = await prisma.dealRequisition.findFirst({
      where: {
        dealId,
        deal: {
          OR: [
            { createdById: userId },
            { assignedToId: userId },
            {
              organization: {
                users: {
                  some: { id: userId },
                },
              },
            },
          ],
        },
      },
      include: {
        template: {
          include: {
            categories: {
              include: {
                items: true,
              },
              orderBy: { orderIndex: 'asc' },
            },
          },
        },
        deal: {
          select: {
            id: true,
            title: true,
            type: true,
          },
        },
      },
    });

    if (!dealRequisition) {
      return null;
    }

    return this.formatDealRequisition(dealRequisition);
  }

  // Create deal requisition
  static async createDealRequisition(
    data: CreateDealRequisitionRequest,
    userId: string
  ): Promise<DealRequisition> {
    // Check if deal exists and user has access
    const deal = await prisma.deal.findFirst({
      where: {
        id: data.dealId,
        OR: [
          { createdById: userId },
          { assignedToId: userId },
          {
            organization: {
              users: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    if (!deal) {
      throw new Error(ERROR_MESSAGES.DEAL_NOT_FOUND);
    }

    // Check if requisition already exists
    const existingRequisition = await prisma.dealRequisition.findFirst({
      where: { dealId: data.dealId },
    });

    if (existingRequisition) {
      throw new Error('Deal requisition already exists');
    }

    // Get template
    const template = await prisma.requisitionTemplate.findFirst({
      where: {
        id: data.templateId,
        OR: [
          { createdBy: { organizationId: deal.organizationId } },
          { isActive: true },
        ],
      },
      include: {
        categories: {
          include: {
            items: true,
          },
          orderBy: { orderIndex: 'asc' },
        },
      },
    });

    if (!template) {
      throw new Error('Template not found');
    }

    // Create deal-specific template and items for document linking
    const dealSpecificTemplate = await prisma.requisitionTemplate.create({
      data: {
        name: `${template.name} - Deal ${deal.title}`,
        dealType: template.dealType,
        industry: template.industry,
        description: `Deal-specific template for ${deal.title}`,
        isDefault: false,
        isActive: true,
        createdById: userId,
        categories: {
          create: template.categories.map((category: any) => ({
            name: category.name,
            description: category.description,
            orderIndex: category.orderIndex,
            isRequired: category.isRequired,
            items: {
              create: category.items.map((item: any) => ({
                name: item.name,
                description: item.description,
                isRequired: item.isRequired,
                orderIndex: item.orderIndex,
                acceptedFormats: item.acceptedFormats,
                maxFileSize: item.maxFileSize,
                examples: item.examples,
              })),
            },
          })),
        },
      },
      include: {
        categories: {
          include: {
            items: true,
          },
          orderBy: { orderIndex: 'asc' },
        },
      },
    });

    // Create default custom data from the deal-specific template
    const defaultCustomData: RequisitionCustomData = {
      categories: dealSpecificTemplate.categories.map((category: any) => ({
        id: category.id,
        name: category.name,
        description: category.description || undefined,
        orderIndex: category.orderIndex,
        isRequired: category.isRequired,
        isCustom: false,
        items: category.items.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || undefined,
          isRequired: item.isRequired,
          orderIndex: item.orderIndex,
          isCustom: false,
          acceptedFormats: (item.acceptedFormats as string[]) || [],
          maxFileSize: item.maxFileSize || undefined,
          examples: (item.examples as string[]) || [],
          status: 'pending',
          uploadedDocuments: [],
        })),
      })),
      metadata: {
        lastModified: new Date().toISOString(),
        modifiedBy: userId,
        version: 1,
      },
    };

    const customData = data.customData
      ? { ...defaultCustomData, ...data.customData }
      : defaultCustomData;

    const dealRequisition = await prisma.dealRequisition.create({
      data: {
        dealId: data.dealId,
        templateId: dealSpecificTemplate.id, // Use the deal-specific template
        userId,
        customData: customData as any,
        isFinalized: false,
      },
      include: {
        template: {
          include: {
            categories: {
              include: {
                items: true,
              },
              orderBy: { orderIndex: 'asc' },
            },
          },
        },
        deal: {
          select: {
            id: true,
            title: true,
            type: true,
          },
        },
      },
    });

    return this.formatDealRequisition(dealRequisition);
  }

  // Update deal requisition
  static async updateDealRequisition(
    dealId: string,
    data: UpdateDealRequisitionRequest,
    userId: string
  ): Promise<DealRequisition> {
    const existingRequisition = await prisma.dealRequisition.findFirst({
      where: {
        dealId,
        deal: {
          OR: [
            { createdById: userId },
            { assignedToId: userId },
            {
              organization: {
                users: {
                  some: { id: userId },
                },
              },
            },
          ],
        },
      },
      include: {
        template: {
          include: {
            categories: {
              include: {
                items: true,
              },
            },
          },
        },
      },
    });

    if (!existingRequisition) {
      throw new Error('Deal requisition not found');
    }

    // Handle custom items - create new database records for custom items
    await this.syncCustomItemsWithDatabase(existingRequisition, data.customData);

    // Update metadata
    const updatedCustomData = {
      ...data.customData,
      metadata: {
        ...data.customData.metadata,
        lastModified: new Date().toISOString(),
        modifiedBy: userId,
        version: (data.customData.metadata.version || 1) + 1,
      },
    };

    const updatedRequisition = await prisma.dealRequisition.update({
      where: { id: existingRequisition.id },
      data: {
        customData: updatedCustomData as any,
        isFinalized: data.isFinalized,
      },
      include: {
        template: {
          include: {
            categories: {
              include: {
                items: true,
              },
              orderBy: { orderIndex: 'asc' },
            },
          },
        },
        deal: {
          select: {
            id: true,
            title: true,
            type: true,
          },
        },
      },
    });

    return this.formatDealRequisition(updatedRequisition);
  }

  // Sync custom items with database
  private static async syncCustomItemsWithDatabase(
    existingRequisition: any,
    newCustomData: RequisitionCustomData
  ): Promise<void> {
    for (const category of newCustomData.categories) {
      // Find corresponding database category
      const dbCategory = existingRequisition.template.categories.find(
        (c: any) => c.id === category.id
      );

      if (!dbCategory) continue;

      for (const item of category.items) {
        // Check if this is a new custom item (has temporary ID or isCustom flag)
        if (item.isCustom && (item.id.startsWith('custom_') || !item.id)) {
          // Create new database record for custom item
          const newItem = await prisma.requisitionItem.create({
            data: {
              name: item.name,
              description: item.description,
              isRequired: item.isRequired,
              orderIndex: item.orderIndex,
              acceptedFormats: item.acceptedFormats,
              maxFileSize: item.maxFileSize,
              examples: item.examples,
              categoryId: dbCategory.id,
            },
          });

          // Update the item ID in the custom data to reference the new database record
          item.id = newItem.id;
        }
      }
    }
  }

  // Format requisition template response
  private static formatRequisitionTemplate(template: any): RequisitionTemplate {
    return {
      id: template.id,
      name: template.name,
      dealType: template.dealType,
      industry: template.industry || undefined,
      description: template.description || undefined,
      isDefault: template.isActive || false, // Map isActive to isDefault for API
      createdAt: template.createdAt.toISOString(),
      updatedAt: template.updatedAt.toISOString(),
      createdById: template.createdById,
      categories: template.categories?.map((category: any) => ({
        id: category.id,
        name: category.name,
        description: category.description || undefined,
        orderIndex: category.orderIndex,
        isRequired: category.isRequired,
        templateId: category.templateId,
        items: category.items?.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || undefined,
          isRequired: item.isRequired,
          orderIndex: item.orderIndex,
          categoryId: item.categoryId,
          acceptedFormats: item.acceptedFormats || [],
          maxFileSize: item.maxFileSize || undefined,
          examples: item.examples || [],
        })) || [],
      })) || [],
    };
  }

  // Format deal requisition response
  private static formatDealRequisition(dealRequisition: any): DealRequisition {
    return {
      id: dealRequisition.id,
      dealId: dealRequisition.dealId,
      templateId: dealRequisition.templateId,
      userId: dealRequisition.userId,
      customData: dealRequisition.customData,
      isFinalized: dealRequisition.isFinalized,
      createdAt: dealRequisition.createdAt.toISOString(),
      updatedAt: dealRequisition.updatedAt.toISOString(),
      template: this.formatRequisitionTemplate(dealRequisition.template),
      deal: dealRequisition.deal,
    };
  }
}
