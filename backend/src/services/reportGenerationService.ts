import prisma from '../config/database';
import { GeminiService } from './geminiService';
import { loggers } from '../config/logger';
import { ReportType } from '@prisma/client';

interface AIAnalysisData {
  persona: {
    id: string;
    name: string;
    type: string;
  };
  analysis: {
    summary: string;
    confidence_score: number;
    risks: Array<{
      title: string;
      description: string;
      severity: string;
      category: string;
      recommendation: string;
    }>;
    recommendations: Array<{
      title: string;
      description: string;
      priority: string;
      action_required: boolean;
    }>;
    extracted_data: Array<{
      field: string;
      value: string;
      confidence: number;
      source_document: string;
    }>;
  };
}

interface DealData {
  id: string;
  title: string;
  type: string;
  value?: number;
  buyer?: string;
  seller?: string;
  industry?: string;
  jurisdiction?: string;
  description?: string;
}

export class ReportGenerationService {
  // Generate a comprehensive due diligence report
  static async generateReport(dealId: string, reportType: ReportType, userId: string) {
    loggers.report.info({ dealId, reportType, userId }, 'Starting report generation');

    // Check if deal exists
    const deal = await prisma.deal.findUnique({
      where: { id: dealId },
      include: {
        organization: true,
        createdBy: true,
        assignedTo: true
      }
    });

    if (!deal) {
      throw new Error('Deal not found');
    }

    // Get AI analysis results
    const aiReviews = await prisma.aIReview.findMany({
      where: { dealId },
      include: {
        persona: true
      }
    });

    if (aiReviews.length === 0) {
      throw new Error('No AI analysis found for this deal. Please complete AI review first.');
    }

    // Check if all reviews are completed
    const completedReviews = aiReviews.filter((review: any) => review.status === 'COMPLETED');
    if (completedReviews.length === 0) {
      throw new Error('No completed AI analysis found. Please wait for AI review to complete.');
    }

    // Create report generation record
    const reportGeneration = await prisma.reportGeneration.create({
      data: {
        dealId,
        type: reportType,
        status: 'GENERATING',
        generatedById: userId,
        progress: 0
      }
    });

    // Start background report generation
    this.generateReportInBackground(reportGeneration.id, deal, completedReviews);

    return {
      id: reportGeneration.id,
      status: 'GENERATING',
      progress: 0,
      message: 'Report generation started'
    };
  }

  // Background report generation
  private static async generateReportInBackground(reportId: string, deal: any, aiReviews: any[]) {
    try {
      loggers.report.info({ reportId }, 'Starting background report generation');

      // Update progress to 10%
      await this.updateReportProgress(reportId, 10);

      // Prepare AI analysis data
      const analysisData = this.prepareAnalysisData(aiReviews);

      // Update progress to 30%
      await this.updateReportProgress(reportId, 30);

      // Generate report content using Gemini
      const reportContent = await this.generateReportContent(deal, analysisData);

      // Update progress to 80%
      await this.updateReportProgress(reportId, 80);

      // Save report content
      await prisma.reportGeneration.update({
        where: { id: reportId },
        data: {
          status: 'COMPLETED',
          progress: 100,
          generatedAt: new Date(),
          // Store the report content in the filePath field (used as content storage)
          filePath: JSON.stringify(reportContent)
        }
      });

      loggers.report.info({ reportId }, 'Report generation completed successfully');

    } catch (error) {
      loggers.report.error({ reportId, error: error instanceof Error ? error.message : error }, 'Report generation failed');

      await prisma.reportGeneration.update({
        where: { id: reportId },
        data: {
          status: 'FAILED',
          errorMessage: error instanceof Error ? error.message : 'Unknown error occurred'
        }
      });
    }
  }

  // Prepare analysis data from AI reviews
  private static prepareAnalysisData(aiReviews: any[]): AIAnalysisData[] {
    return aiReviews.map(review => ({
      persona: {
        id: review.persona.id,
        name: review.persona.name,
        type: review.persona.type
      },
      analysis: review.analysisResult || {
        summary: 'Analysis completed',
        confidence_score: 75,
        risks: [],
        recommendations: [],
        extracted_data: []
      }
    }));
  }

  // Clean and format the AI response
  private static cleanReportContent(content: string): string {
    if (!content || typeof content !== 'string') {
      return 'No content generated';
    }

    return content
      // Remove any JSON formatting if AI accidentally includes it
      .replace(/^```json\s*/, '')
      .replace(/\s*```$/, '')
      .replace(/^```\s*/, '')

      // Clean up escaped characters
      .replace(/\\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'")
      .replace(/\\\\/g, '\\')

      // Remove excessive whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .replace(/^\s+|\s+$/g, '')

      // Ensure proper markdown formatting
      .replace(/^#\s*([^#])/gm, '# $1')
      .replace(/^##\s*([^#])/gm, '## $1')
      .replace(/^###\s*([^#])/gm, '### $1')

      .trim();
  }

  // Generate report content using Gemini AI
  private static async generateReportContent(deal: DealData, analysisData: AIAnalysisData[]) {
    const prompt = this.buildReportPrompt(deal, analysisData);

    loggers.report.info({ dealId: deal.id }, 'Sending report generation request to Gemini');

    const aiResponse = await GeminiService.generateReport(prompt);

    // Clean the AI response
    const cleanedContent = this.cleanReportContent(aiResponse);

    // Structure the report content properly
    const reportContent = {
      format: 'markdown',
      content: cleanedContent,
      generated_at: new Date().toISOString(),
      deal_info: {
        title: deal.title,
        type: deal.type,
        value: deal.value,
        buyer: deal.buyer,
        seller: deal.seller
      }
    };

    return reportContent;
  }

  // Build comprehensive prompt for report generation
  private static buildReportPrompt(deal: DealData, analysisData: AIAnalysisData[]): string {
    const dealInfo = `
Deal Information:
- Title: ${deal.title}
- Type: ${deal.type}
- Value: ${deal.value ? `$${deal.value.toLocaleString()}` : 'Not specified'}
- Buyer: ${deal.buyer || 'Not specified'}
- Seller: ${deal.seller || 'Not specified'}
- Industry: ${deal.industry || 'Not specified'}
- Jurisdiction: ${deal.jurisdiction || 'Not specified'}
- Description: ${deal.description || 'Not specified'}
`;

    const analysisSection = analysisData.map(data => `
${data.persona.name} (${data.persona.type}) Analysis:
Summary: ${data.analysis.summary}
Confidence Score: ${data.analysis.confidence_score}%

Key Risks:
${data.analysis.risks.map(risk => `- ${risk.title}: ${risk.description} (${risk.severity})`).join('\n')}

Recommendations:
${data.analysis.recommendations.map(rec => `- ${rec.title}: ${rec.description} (${rec.priority})`).join('\n')}

Key Data Points:
${data.analysis.extracted_data.map(data => `- ${data.field}: ${data.value} (${data.confidence}% confidence)`).join('\n')}
`).join('\n---\n');

    return `
You are an expert due diligence analyst. Generate a comprehensive, professional due diligence report based on the following deal information and AI analysis results.

${dealInfo}

AI Analysis Results:
${analysisSection}

Please generate a detailed due diligence report in a professional, readable format. Structure the report as follows:

# DUE DILIGENCE REPORT

## EXECUTIVE SUMMARY

### Transaction Overview
- Provide a clear overview of the transaction
- Include key deal metrics and structure
- Summarize the strategic rationale

### Key Findings
- Highlight the most important findings from the analysis
- Include both positive and negative aspects
- Focus on material issues that could impact the deal

### Investment Recommendation
- Provide a clear recommendation (Proceed, Proceed with Conditions, or Do Not Proceed)
- Justify the recommendation based on the analysis
- Include any critical conditions or requirements

## FINANCIAL ANALYSIS

### Financial Performance
- Analyze revenue trends and profitability
- Review key financial metrics and ratios
- Assess financial stability and cash flow

### Valuation Assessment
- Comment on the deal valuation and multiples
- Compare to industry benchmarks where possible
- Identify any valuation concerns or opportunities

### Financial Risks
- Highlight key financial risks identified
- Assess the impact and likelihood of each risk
- Suggest mitigation strategies

## OPERATIONAL ANALYSIS

### Management and Organization
- Assess the quality and experience of management team
- Review organizational structure and capabilities
- Identify key person dependencies

### Business Operations
- Analyze operational efficiency and scalability
- Review technology infrastructure and systems
- Assess operational risks and opportunities

## LEGAL AND COMPLIANCE

### Corporate Structure and Governance
- Review corporate structure and ownership
- Assess governance practices and board composition
- Identify any structural issues

### Legal and Regulatory Issues
- Highlight any material legal issues or litigation
- Review compliance with applicable regulations
- Assess regulatory risks and requirements

## RISK ASSESSMENT

### Critical Risks
- List and analyze the most significant risks
- Categorize risks by type (financial, operational, legal, market)
- Assess probability and potential impact

### Risk Mitigation
- Suggest specific mitigation strategies for each major risk
- Recommend deal structure modifications if needed
- Identify areas requiring ongoing monitoring

## RECOMMENDATIONS AND NEXT STEPS

### Strategic Recommendations
- Provide specific recommendations for proceeding
- Suggest deal terms or structure modifications
- Recommend areas for further due diligence

### Integration Considerations
- Highlight key integration challenges and opportunities
- Suggest post-closing priorities and actions
- Recommend retention strategies for key personnel

---

Please write this report in clear, professional language suitable for senior executives and board members. Use bullet points and structured formatting for readability. Base all conclusions on the AI analysis data provided, and be specific about findings and recommendations.

IMPORTANT FORMATTING REQUIREMENTS:
- Do NOT format this as JSON
- Use clean markdown formatting with proper headers (#, ##, ###)
- Use bullet points (-) for lists
- Use **bold** for emphasis and *italic* for secondary emphasis
- Write in clear, professional language
- Ensure proper spacing between sections
- Do not include any escape characters or raw formatting codes
- Make the content ready for direct display to executives

Provide a clean, readable report in markdown format that can be displayed directly to users.
`;
  }

  // Update report generation progress
  private static async updateReportProgress(reportId: string, progress: number) {
    await prisma.reportGeneration.update({
      where: { id: reportId },
      data: { progress }
    });
  }

  // Get reports by deal ID
  static async getReportsByDeal(dealId: string) {
    return await prisma.reportGeneration.findMany({
      where: { dealId },
      include: {
        generatedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  // Get report by ID
  static async getReportById(reportId: string) {
    return await prisma.reportGeneration.findUnique({
      where: { id: reportId },
      include: {
        deal: {
          select: {
            id: true,
            title: true,
            type: true,
            value: true,
            buyer: true,
            seller: true
          }
        },
        generatedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
  }

  // Get report status
  static async getReportStatus(reportId: string) {
    const report = await prisma.reportGeneration.findUnique({
      where: { id: reportId },
      select: {
        id: true,
        status: true,
        progress: true,
        errorMessage: true,
        generatedAt: true
      }
    });

    return report;
  }

  // Download report (placeholder for PDF generation)
  static async downloadReport(reportId: string) {
    const report = await prisma.reportGeneration.findUnique({
      where: { id: reportId }
    });

    if (!report || report.status !== 'COMPLETED') {
      return null;
    }

    // For now, return the JSON content
    // In a real implementation, you would generate a PDF here
    return {
      filename: `due-diligence-report-${reportId}.json`,
      data: Buffer.from(report.filePath || '{}'),
      size: Buffer.byteLength(report.filePath || '{}')
    };
  }
}
