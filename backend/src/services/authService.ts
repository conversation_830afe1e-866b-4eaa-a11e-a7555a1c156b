// Prisma client imports - using direct import to resolve IDE issues
import type { User, UserRole } from '.prisma/client';
import { UserRole as UserRoleValues } from '.prisma/client';
import prisma from '../config/database';
import {
  hashPassword,
  comparePassword,
  generateToken,
  generateRefreshToken,
  generateRandomString,
  addDays,
  isValidEmail,
  isValidPassword
} from '../utils';
import { ERROR_MESSAGES } from '../constants';
import config from '../config';
import { EmailService } from './emailService';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationId?: string;
  role?: UserRole;
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    organizationId: string;
    emailVerified: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: string;
  };
}

export class AuthService {
  // Login user
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const { email, password } = credentials;

    // Validate input
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    if (!isValidEmail(email)) {
      throw new Error('Invalid email format');
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      include: { organization: true },
    });

    if (!user) {
      throw new Error(ERROR_MESSAGES.INVALID_CREDENTIALS);
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new Error(ERROR_MESSAGES.INVALID_CREDENTIALS);
    }

    // Generate tokens
    const tokenPayload = { userId: user.id, email: user.email, role: user.role };
    const accessToken = generateToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // Create session
    const expiresAt = addDays(new Date(), 7); // 7 days for refresh token
    await prisma.userSession.create({
      data: {
        userId: user.id,
        token: accessToken,
        expiresAt,
      },
    });

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        organizationId: user.organizationId,
        emailVerified: user.emailVerified,
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn,
      },
    };
  }

  // Register new user
  static async register(userData: RegisterData): Promise<AuthResponse> {
    const { email, password, firstName, lastName, organizationId, role } = userData;

    // Validate input
    if (!email || !password || !firstName || !lastName) {
      throw new Error('All required fields must be provided');
    }

    if (!isValidEmail(email)) {
      throw new Error('Invalid email format');
    }

    if (!isValidPassword(password)) {
      throw new Error('Password must be at least 8 characters with uppercase, lowercase, and number');
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      throw new Error(ERROR_MESSAGES.EMAIL_ALREADY_REGISTERED);
    }

    // Get or create organization
    let targetOrganizationId = organizationId;
    if (!targetOrganizationId) {
      // Create default organization for new user
      const organization = await prisma.organization.create({
        data: {
          name: `${firstName} ${lastName}'s Organization`,
          domain: email.split('@')[1],
        },
      });
      targetOrganizationId = organization.id;
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        passwordHash,
        firstName,
        lastName,
        role: role || UserRoleValues.ANALYST,
        organizationId: targetOrganizationId,
        emailVerified: false, // Will be verified via email
      },
      include: { organization: true },
    });

    // Generate tokens
    const tokenPayload = { userId: user.id, email: user.email, role: user.role };
    const accessToken = generateToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // Create session
    const expiresAt = addDays(new Date(), 7);
    await prisma.userSession.create({
      data: {
        userId: user.id,
        token: accessToken,
        expiresAt,
      },
    });

    // Send welcome email (don't wait for it to complete)
    EmailService.sendWelcomeEmail(email, {
      firstName: user.firstName,
      loginLink: config.app.frontendUrl,
    }).catch(error => {
      console.error('❌ Failed to send welcome email:', error);
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        organizationId: user.organizationId,
        emailVerified: user.emailVerified,
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn,
      },
    };
  }

  // Logout user
  static async logout(userId: string, token: string): Promise<void> {
    // Remove session
    await prisma.userSession.deleteMany({
      where: {
        userId,
        token,
      },
    });
  }

  // Logout from all devices
  static async logoutAll(userId: string): Promise<void> {
    await prisma.userSession.deleteMany({
      where: { userId },
    });
  }

  // Refresh access token
  static async refreshToken(refreshToken: string): Promise<{ accessToken: string; expiresIn: string }> {
    try {
      const decoded = JSON.parse(Buffer.from(refreshToken.split('.')[1], 'base64').toString()) as any;

      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
      });

      if (!user) {
        throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
      }

      // Generate new access token
      const tokenPayload = { userId: user.id, email: user.email, role: user.role };
      const accessToken = generateToken(tokenPayload);

      // Update session with new token
      await prisma.userSession.updateMany({
        where: { userId: user.id },
        data: { token: accessToken },
      });

      return {
        accessToken,
        expiresIn: config.jwt.expiresIn,
      };
    } catch (error) {
      throw new Error(ERROR_MESSAGES.TOKEN_INVALID);
    }
  }

  // Request password reset
  static async requestPasswordReset(email: string): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (!user) {
      // Don't reveal if email exists
      return;
    }

    // Generate reset token
    const resetToken = generateRandomString(32);
    const expiresAt = addDays(new Date(), 1); // 24 hours

    // Save reset token
    await prisma.passwordReset.create({
      data: {
        userId: user.id,
        token: resetToken,
        expiresAt,
      },
    });

    // Send password reset email
    const resetLink = `${config.app.frontendUrl}/reset-password?token=${resetToken}`;

    try {
      await EmailService.sendPasswordResetEmail(email, {
        firstName: user.firstName,
        resetLink,
      });
      console.log(`✅ Password reset email sent to ${email}`);
    } catch (error) {
      console.error('❌ Failed to send password reset email:', error);
      // Don't throw error to avoid revealing if email exists
    }
  }

  // Reset password
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    if (!isValidPassword(newPassword)) {
      throw new Error('Password must be at least 8 characters with uppercase, lowercase, and number');
    }

    const resetRecord = await prisma.passwordReset.findUnique({
      where: { token },
      include: { user: true },
    });

    if (!resetRecord || resetRecord.used || resetRecord.expiresAt < new Date()) {
      throw new Error('Invalid or expired reset token');
    }

    // Hash new password
    const passwordHash = await hashPassword(newPassword);

    // Update password and mark reset token as used
    await prisma.$transaction([
      prisma.user.update({
        where: { id: resetRecord.userId },
        data: { passwordHash },
      }),
      prisma.passwordReset.update({
        where: { id: resetRecord.id },
        data: { used: true },
      }),
      // Logout from all devices
      prisma.userSession.deleteMany({
        where: { userId: resetRecord.userId },
      }),
    ]);
  }

  // Get user profile
  static async getProfile(userId: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true },
    });
  }

  // Update user profile
  static async updateProfile(userId: string, updateData: Partial<User>): Promise<User> {
    // Remove sensitive fields that shouldn't be updated via this method
    const { passwordHash, emailVerified, ...safeUpdateData } = updateData as any;

    return prisma.user.update({
      where: { id: userId },
      data: safeUpdateData,
      include: { organization: true },
    });
  }

  // Change password
  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error(ERROR_MESSAGES.USER_NOT_FOUND);
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    if (!isValidPassword(newPassword)) {
      throw new Error('New password must be at least 8 characters with uppercase, lowercase, and number');
    }

    // Hash new password
    const passwordHash = await hashPassword(newPassword);

    // Update password and logout from all other devices
    await prisma.$transaction([
      prisma.user.update({
        where: { id: userId },
        data: { passwordHash },
      }),
      prisma.userSession.deleteMany({
        where: { userId },
      }),
    ]);
  }
}
