import { DealType } from '@prisma/client';
import { DefaultTemplateData } from '../types/requisition';

export const DEFAULT_REQUISITION_TEMPLATES: DefaultTemplateData = {
  MA: {
    name: 'M&A Due Diligence Template',
    description: 'Comprehensive template for mergers and acquisitions due diligence',
    categories: [
      {
        name: 'Corporate Documents',
        description: 'Essential corporate governance and legal documents',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'Certificate of Incorporation',
            description: 'Current certificate of incorporation and all amendments',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760, // 10MB
            examples: ['Certificate of Incorporation.pdf', 'Articles of Incorporation.pdf']
          },
          {
            name: 'Bylaws',
            description: 'Current bylaws and all amendments',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['Corporate Bylaws.pdf', 'Amended Bylaws.pdf']
          },
          {
            name: 'Board Resolutions',
            description: 'Board resolutions for the past 3 years',
            isRequired: true,
            orderIndex: 3,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: ********, // 20MB
            examples: ['Board Resolutions 2024.pdf', 'Board Minutes.pdf']
          },
          {
            name: 'Shareholder Agreements',
            description: 'All shareholder agreements and voting trusts',
            isRequired: true,
            orderIndex: 4,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['Shareholder Agreement.pdf', 'Voting Trust Agreement.pdf']
          },
          {
            name: 'Stock Ledger',
            description: 'Current capitalization table and stock ledger',
            isRequired: true,
            orderIndex: 5,
            acceptedFormats: ['pdf', 'xlsx', 'xls'],
            maxFileSize: 5242880, // 5MB
            examples: ['Cap Table.xlsx', 'Stock Ledger.pdf']
          }
        ]
      },
      {
        name: 'Financial Documents',
        description: 'Financial statements, tax returns, and accounting records',
        orderIndex: 2,
        isRequired: true,
        items: [
          {
            name: 'Audited Financial Statements',
            description: 'Audited financial statements for the past 3 years',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['Audited Financials 2024.pdf', '2023 Annual Report.pdf']
          },
          {
            name: 'Management Financial Statements',
            description: 'Monthly/quarterly management reports for the past 2 years',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'xlsx', 'xls'],
            maxFileSize: ********,
            examples: ['Monthly Financials Q1 2024.xlsx', 'Management Reports.pdf']
          },
          {
            name: 'Tax Returns',
            description: 'Corporate tax returns for the past 3 years',
            isRequired: true,
            orderIndex: 3,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['Tax Return 2023.pdf', 'Corporate Tax Filing.pdf']
          },
          {
            name: 'Budget and Projections',
            description: 'Current year budget and 3-year financial projections',
            isRequired: false,
            orderIndex: 4,
            acceptedFormats: ['pdf', 'xlsx', 'xls'],
            maxFileSize: 10485760,
            examples: ['2024 Budget.xlsx', 'Financial Projections.pdf']
          }
        ]
      },
      {
        name: 'Legal and Compliance',
        description: 'Legal agreements, litigation, and regulatory compliance',
        orderIndex: 3,
        isRequired: true,
        items: [
          {
            name: 'Material Contracts',
            description: 'All material contracts and agreements',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 52428800, // 50MB
            examples: ['Customer Contracts.pdf', 'Supplier Agreements.pdf']
          },
          {
            name: 'Litigation Summary',
            description: 'Summary of all pending, threatened, or settled litigation',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['Litigation Summary.pdf', 'Legal Proceedings.doc']
          },
          {
            name: 'Regulatory Licenses',
            description: 'All business licenses and regulatory permits',
            isRequired: true,
            orderIndex: 3,
            acceptedFormats: ['pdf', 'jpg', 'png'],
            maxFileSize: 10485760,
            examples: ['Business License.pdf', 'Operating Permits.pdf']
          },
          {
            name: 'Insurance Policies',
            description: 'Current insurance policies and coverage summaries',
            isRequired: false,
            orderIndex: 4,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['General Liability Policy.pdf', 'D&O Insurance.pdf']
          }
        ]
      },
      {
        name: 'Human Resources',
        description: 'Employee records, benefits, and HR policies',
        orderIndex: 4,
        isRequired: false,
        items: [
          {
            name: 'Employee Handbook',
            description: 'Current employee handbook and HR policies',
            isRequired: false,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['Employee Handbook.pdf', 'HR Policies.doc']
          },
          {
            name: 'Organization Chart',
            description: 'Current organizational structure and reporting lines',
            isRequired: false,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'png', 'jpg', 'xlsx'],
            maxFileSize: 5242880,
            examples: ['Org Chart.pdf', 'Organization Structure.png']
          },
          {
            name: 'Key Employee Contracts',
            description: 'Employment agreements for key personnel',
            isRequired: false,
            orderIndex: 3,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: ********,
            examples: ['CEO Employment Agreement.pdf', 'Key Employee Contracts.pdf']
          }
        ]
      },
      {
        name: 'Intellectual Property',
        description: 'Patents, trademarks, copyrights, and IP agreements',
        orderIndex: 5,
        isRequired: false,
        items: [
          {
            name: 'Patent Portfolio',
            description: 'All patents, patent applications, and IP registrations',
            isRequired: false,
            orderIndex: 1,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['Patent Portfolio.pdf', 'IP Registrations.pdf']
          },
          {
            name: 'Trademark Registrations',
            description: 'All trademark registrations and applications',
            isRequired: false,
            orderIndex: 2,
            acceptedFormats: ['pdf'],
            maxFileSize: 10485760,
            examples: ['Trademark Registrations.pdf', 'Brand Portfolio.pdf']
          },
          {
            name: 'IP Agreements',
            description: 'License agreements, IP assignments, and NDAs',
            isRequired: false,
            orderIndex: 3,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: ********,
            examples: ['IP License Agreements.pdf', 'Technology Assignments.pdf']
          }
        ]
      }
    ]
  },
  INVESTMENT: {
    name: 'Investment Due Diligence Template',
    description: 'Template for investment and funding due diligence',
    categories: [
      {
        name: 'Business Overview',
        description: 'Company overview and business model documentation',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'Business Plan',
            description: 'Current business plan and strategy document',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],
            maxFileSize: ********,
            examples: ['Business Plan 2024.pdf', 'Strategic Plan.pptx']
          },
          {
            name: 'Pitch Deck',
            description: 'Latest investor presentation and pitch deck',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'ppt', 'pptx'],
            maxFileSize: ********,
            examples: ['Investor Pitch Deck.pdf', 'Series A Presentation.pptx']
          },
          {
            name: 'Market Analysis',
            description: 'Market research and competitive analysis',
            isRequired: false,
            orderIndex: 3,
            acceptedFormats: ['pdf', 'doc', 'docx', 'xlsx'],
            maxFileSize: ********,
            examples: ['Market Research.pdf', 'Competitive Analysis.xlsx']
          }
        ]
      },
      {
        name: 'Financial Information',
        description: 'Financial statements and projections',
        orderIndex: 2,
        isRequired: true,
        items: [
          {
            name: 'Financial Statements',
            description: 'Historical financial statements (3 years)',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'xlsx'],
            maxFileSize: ********,
            examples: ['Financial Statements.pdf', 'P&L Statements.xlsx']
          },
          {
            name: 'Financial Projections',
            description: '5-year financial projections and assumptions',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'xlsx'],
            maxFileSize: 10485760,
            examples: ['Financial Projections.xlsx', '5-Year Model.pdf']
          },
          {
            name: 'Cap Table',
            description: 'Current capitalization table and ownership structure',
            isRequired: true,
            orderIndex: 3,
            acceptedFormats: ['pdf', 'xlsx'],
            maxFileSize: 5242880,
            examples: ['Cap Table.xlsx', 'Ownership Structure.pdf']
          }
        ]
      }
    ]
  },
  PARTNERSHIP: {
    name: 'Partnership Due Diligence Template',
    description: 'Template for strategic partnership due diligence',
    categories: [
      {
        name: 'Partnership Structure',
        description: 'Partnership agreements and structure documentation',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'Partnership Agreement',
            description: 'Proposed partnership agreement and terms',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['Partnership Agreement.pdf', 'Strategic Alliance Terms.doc']
          },
          {
            name: 'Business Case',
            description: 'Business case and rationale for partnership',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],
            maxFileSize: ********,
            examples: ['Partnership Business Case.pdf', 'Strategic Rationale.pptx']
          }
        ]
      }
    ]
  },
  JOINT_VENTURE: {
    name: 'Joint Venture Due Diligence Template',
    description: 'Template for joint venture due diligence',
    categories: [
      {
        name: 'JV Structure',
        description: 'Joint venture structure and governance',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'JV Agreement',
            description: 'Joint venture agreement and operating terms',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['JV Agreement.pdf', 'Operating Agreement.doc']
          },
          {
            name: 'Governance Structure',
            description: 'Governance and decision-making framework',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],
            maxFileSize: 10485760,
            examples: ['Governance Framework.pdf', 'Decision Matrix.pptx']
          }
        ]
      }
    ]
  },
  ASSET_PURCHASE: {
    name: 'Asset Purchase Due Diligence Template',
    description: 'Template for asset purchase due diligence',
    categories: [
      {
        name: 'Asset Documentation',
        description: 'Documentation of assets being acquired',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'Asset List',
            description: 'Detailed list of assets included in the transaction',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'xlsx', 'xls'],
            maxFileSize: 10485760,
            examples: ['Asset Inventory.xlsx', 'Asset List.pdf']
          },
          {
            name: 'Asset Valuations',
            description: 'Independent valuations of key assets',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['Asset Valuation Report.pdf', 'Appraisal Reports.pdf']
          }
        ]
      }
    ]
  },
  IPO: {
    name: 'IPO Due Diligence Template',
    description: 'Template for initial public offering due diligence',
    categories: [
      {
        name: 'SEC Filings',
        description: 'SEC registration and filing documents',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'S-1 Registration Statement',
            description: 'Draft S-1 registration statement',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['S-1 Registration.pdf', 'IPO Filing.pdf']
          },
          {
            name: 'Prospectus',
            description: 'Preliminary and final prospectus',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf'],
            maxFileSize: ********,
            examples: ['Preliminary Prospectus.pdf', 'Final Prospectus.pdf']
          }
        ]
      }
    ]
  },
  DEBT_FINANCING: {
    name: 'Debt Financing Due Diligence Template',
    description: 'Template for debt financing due diligence',
    categories: [
      {
        name: 'Credit Documentation',
        description: 'Credit agreements and financing documentation',
        orderIndex: 1,
        isRequired: true,
        items: [
          {
            name: 'Credit Agreement',
            description: 'Proposed credit agreement and terms',
            isRequired: true,
            orderIndex: 1,
            acceptedFormats: ['pdf', 'doc', 'docx'],
            maxFileSize: 10485760,
            examples: ['Credit Agreement.pdf', 'Loan Terms.doc']
          },
          {
            name: 'Financial Covenants',
            description: 'Financial covenants and compliance requirements',
            isRequired: true,
            orderIndex: 2,
            acceptedFormats: ['pdf', 'xlsx'],
            maxFileSize: 5242880,
            examples: ['Financial Covenants.pdf', 'Compliance Matrix.xlsx']
          }
        ]
      }
    ]
  }
};
