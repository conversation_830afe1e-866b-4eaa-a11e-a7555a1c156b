# Deal Management API Documentation

## Overview

The Deal Management API provides comprehensive CRUD operations for managing due diligence deals, including creation, updates, assignment, status management, and note tracking.

## Base URL

```
/api/deals
```

## Authentication

All endpoints require authentication via <PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## Endpoints

### 1. Create Deal

**POST** `/api/deals`

Creates a new deal in the system.

**Request Body:**
```json
{
  "title": "TechCorp Acquisition",
  "type": "MA",
  "value": 50000000,
  "jurisdiction": "Delaware",
  "buyer": "Global Holdings Inc",
  "seller": "TechCorp Ltd",
  "startDate": "2024-01-01T00:00:00.000Z",
  "expectedSigningDate": "2024-03-01T00:00:00.000Z",
  "expectedClosingDate": "2024-06-01T00:00:00.000Z",
  "assignedToId": "user-id-here",
  "parties": [
    {
      "partyType": "BUYER",
      "name": "Global Holdings Inc",
      "role": "Acquirer",
      "contactInfo": {
        "email": "<EMAIL>",
        "phone": "******-0123"
      }
    }
  ],
  "dates": [
    {
      "dateType": "SIGNING_DATE",
      "dateValue": "2024-03-01T00:00:00.000Z",
      "description": "Expected signing date"
    }
  ],
  "notes": "Initial deal setup notes"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Deal created successfully",
  "data": {
    "id": "deal-id-here",
    "title": "TechCorp Acquisition",
    "type": "MA",
    "status": "DRAFT",
    "progress": 0,
    "createdAt": "2024-01-01T00:00:00.000Z",
    // ... full deal object
  }
}
```

### 2. Get Deals List

**GET** `/api/deals`

Retrieves a paginated list of deals with optional filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `search` (string): Search in title, buyer, seller, jurisdiction
- `status` (string): Filter by deal status
- `type` (string): Filter by deal type
- `assignedToId` (string): Filter by assigned user
- `createdById` (string): Filter by creator
- `sortBy` (string): Sort field (createdAt, updatedAt, title, value, progress)
- `sortOrder` (string): Sort order (asc, desc)
- `startDate` (string): Filter deals created after this date
- `endDate` (string): Filter deals created before this date

**Example:**
```
GET /api/deals?page=1&limit=10&status=ACTIVE&type=MA&search=TechCorp
```

**Response:**
```json
{
  "success": true,
  "data": {
    "deals": [
      {
        "id": "deal-id-here",
        "title": "TechCorp Acquisition",
        // ... deal object
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "filters": {
      "status": "ACTIVE",
      "type": "MA"
    }
  }
}
```

### 3. Get Deal by ID

**GET** `/api/deals/:id`

Retrieves a specific deal by its ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "deal-id-here",
    "title": "TechCorp Acquisition",
    "type": "MA",
    "value": 50000000,
    "status": "ACTIVE",
    "progress": 65,
    "createdBy": {
      "id": "user-id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    "assignedTo": {
      "id": "user-id",
      "firstName": "Jane",
      "lastName": "Smith",
      "email": "<EMAIL>"
    },
    "parties": [...],
    "dates": [...],
    "notes": [...],
    "_count": {
      "documents": 24,
      "reviews": 3,
      "comments": 8
    }
  }
}
```

### 4. Update Deal

**PUT** `/api/deals/:id`

Updates an existing deal.

**Request Body:**
```json
{
  "title": "Updated Deal Title",
  "progress": 75,
  "status": "UNDER_REVIEW",
  "expectedClosingDate": "2024-07-01T00:00:00.000Z"
}
```

### 5. Delete Deal

**DELETE** `/api/deals/:id`

Deletes a deal (only creator can delete).

**Response:**
```json
{
  "success": true,
  "message": "Deal deleted successfully"
}
```

### 6. Assign Deal

**POST** `/api/deals/:id/assign`

Assigns a deal to a user.

**Request Body:**
```json
{
  "assignedToId": "user-id-here",
  "note": "Assigning for legal review"
}
```

### 7. Update Deal Status

**PUT** `/api/deals/:id/status`

Updates the status of a deal.

**Request Body:**
```json
{
  "status": "UNDER_REVIEW",
  "note": "Moving to review phase"
}
```

### 8. Add Deal Note

**POST** `/api/deals/:id/notes`

Adds a note to a deal.

**Request Body:**
```json
{
  "note": "Important update regarding the deal timeline"
}
```

### 9. Get Deal Statistics

**GET** `/api/deals/stats`

Retrieves deal statistics for the organization.

**Response:**
```json
{
  "success": true,
  "data": {
    "total": 45,
    "byStatus": {
      "DRAFT": 5,
      "ACTIVE": 15,
      "UNDER_REVIEW": 10,
      "COMPLETED": 12,
      "CANCELLED": 3
    },
    "byType": {
      "MA": 20,
      "INVESTMENT": 15,
      "PARTNERSHIP": 10
    },
    "totalValue": 2500000000,
    "averageProgress": 67,
    "recentActivity": 8
  }
}
```

## Data Types

### Deal Types
- `MA` - M&A
- `INVESTMENT` - Investment
- `PARTNERSHIP` - Partnership
- `JOINT_VENTURE` - Joint Venture
- `ASSET_PURCHASE` - Asset Purchase
- `IPO` - IPO
- `DEBT_FINANCING` - Debt Financing

### Deal Status
- `DRAFT` - Draft
- `ACTIVE` - Active
- `UNDER_REVIEW` - Under Review
- `PENDING_APPROVAL` - Pending Approval
- `COMPLETED` - Completed
- `CANCELLED` - Cancelled
- `ON_HOLD` - On Hold

### Party Types
- `BUYER` - Buyer
- `SELLER` - Seller
- `ADVISOR` - Advisor
- `BANK` - Bank
- `LEGAL_COUNSEL` - Legal Counsel
- `AUDITOR` - Auditor
- `OTHER` - Other

### Date Types
- `SIGNING_DATE` - Signing Date
- `CLOSING_DATE` - Closing Date
- `DUE_DILIGENCE_START` - Due Diligence Start
- `DUE_DILIGENCE_END` - Due Diligence End
- `BOARD_APPROVAL` - Board Approval
- `SHAREHOLDER_APPROVAL` - Shareholder Approval
- `REGULATORY_APPROVAL` - Regulatory Approval
- `FINANCING_COMMITMENT` - Financing Commitment
- `OTHER` - Other

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "message": "Human-readable error description",
  "details": [] // Validation errors if applicable
}
```

Common HTTP status codes:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (deal not found)
- `409` - Conflict (duplicate deal title)
- `500` - Internal Server Error

## Access Control

- Users can only access deals within their organization
- Deal creators can edit and delete their deals
- Assigned users can edit deals assigned to them
- Senior analysts and admins have broader access
- Only deal creators can delete deals
