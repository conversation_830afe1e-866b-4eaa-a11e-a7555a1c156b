# Backend Dockerfile
FROM node:18-alpine
WORKDIR /app

# Set environment variables
ENV NODE_ENV=development

# Copy package files first for better caching
COPY package.json ./
RUN npm install

# Copy the rest of the application code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Expose the port the app will run on
EXPOSE 8000

# Start the application in development mode
CMD ["npm", "run", "dev"]
