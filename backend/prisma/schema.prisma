// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// USER MANAGEMENT & AUTHENTICATION
// ================================

model Organization {
  id        String   @id @default(cuid())
  name      String
  domain    String?  @unique
  settings  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users User[]
  deals Deal[]

  @@map("organizations")
}

enum UserRole {
  ADMIN
  SENIOR_ANALYST
  ANALYST
  REVIEWER
  VIEWER
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  passwordHash  String
  firstName     String
  lastName      String
  role          UserRole  @default(ANALYST)
  emailVerified Boolean   @default(false)
  lastLogin     DateTime?
  selectedPersonas PersonaType[] @default([])
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Relations
  sessions       UserSession[]
  passwordResets PasswordReset[]
  createdDeals   Deal[]        @relation("DealCreator")
  assignedDeals  Deal[]        @relation("DealAssignee")
  comments       Comment[]
  notifications  Notification[]
  emailLogs      EmailLog[]
  documents      Document[]    @relation("DocumentUploader")
  documentVersions DocumentVersion[]
  reviews        Review[]
  reportGenerations ReportGeneration[]
  reportTemplates ReportTemplate[]
  requisitionTemplates RequisitionTemplate[]
  dealNotes      DealNote[]
  reviewComments ReviewComment[]
  auditLogs      AuditLog[]
  dealRequisitions DealRequisition[]

  @@map("users")
}

model UserSession {
  id        String   @id @default(cuid())
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  // Foreign Keys
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model PasswordReset {
  id        String   @id @default(cuid())
  token     String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  // Foreign Keys
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_resets")
}

// ================================
// DEAL MANAGEMENT
// ================================

enum DealType {
  MA              // M&A
  INVESTMENT
  PARTNERSHIP
  JOINT_VENTURE
  ASSET_PURCHASE
  IPO
  DEBT_FINANCING
}

enum DealStatus {
  DRAFT
  ACTIVE
  DOCUMENTS_SUBMITTED  // When documents are submitted for AI review
  UNDER_REVIEW
  PENDING_APPROVAL
  COMPLETED
  CANCELLED
  ON_HOLD
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ConfidentialityLevel {
  PUBLIC
  INTERNAL
  CONFIDENTIAL
  RESTRICTED
}

model Deal {
  id               String     @id @default(cuid())
  title            String
  type             DealType
  currency         String?
  value            Decimal?
  jurisdiction     String?
  buyer            String?
  seller           String?
  status           DealStatus @default(DRAFT)
  startDate        DateTime?
  expectedSigningDate DateTime?
  expectedClosingDate DateTime?
  actualClosingDate   DateTime?
  progress         Int        @default(0) // 0-100
  industry         String?
  description      String?
  riskLevel        RiskLevel? @default(MEDIUM)
  confidentialityLevel ConfidentialityLevel? @default(INTERNAL)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("DealCreator", fields: [createdById], references: [id])
  assignedToId   String?
  assignedTo     User?        @relation("DealAssignee", fields: [assignedToId], references: [id])

  // Relations
  parties       DealParty[]
  suppliers     DealSupplier[]
  dates         DealDate[]
  notes         DealNote[]
  documents     Document[]
  reviews       Review[]
  reports       ReportGeneration[]
  comments      Comment[]
  notifications Notification[]
  analytics     AnalyticsMetric[]
  requisition   DealRequisition?
  aiReviews     AIReview[]

  @@map("deals")
}

enum PartyType {
  BUYER
  SELLER
  ADVISOR
  BANK
  LEGAL_COUNSEL
  AUDITOR
  OTHER
}

model DealParty {
  id          String    @id @default(cuid())
  partyType   PartyType
  name        String
  role        String?
  contactInfo Json?
  createdAt   DateTime  @default(now())

  // Foreign Keys
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("deal_parties")
}

model DealSupplier {
  id           String   @id @default(cuid())
  name         String
  jurisdiction String
  contactInfo  Json?
  description  String?
  createdAt    DateTime @default(now())

  // Foreign Keys
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("deal_suppliers")
}

enum DateType {
  SIGNING
  CLOSING
  DUE_DILIGENCE_START
  DUE_DILIGENCE_END
  BOARD_APPROVAL
  REGULATORY_APPROVAL
  FINANCING_COMMITMENT
  OTHER
}

model DealDate {
  id          String   @id @default(cuid())
  dateType    DateType
  dateValue   DateTime
  description String?
  createdAt   DateTime @default(now())

  // Foreign Keys
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("deal_dates")
}

model DealNote {
  id        String   @id @default(cuid())
  note      String
  createdAt DateTime @default(now())

  // Foreign Keys
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)
  userId String
  user   User   @relation(fields: [userId], references: [id])

  @@map("deal_notes")
}

// ================================
// REQUISITION & DOCUMENT MANAGEMENT
// ================================

model RequisitionTemplate {
  id           String   @id @default(cuid())
  name         String
  dealType     DealType
  industry     String?
  description  String?
  templateData Json?
  isDefault    Boolean  @default(false)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Foreign Keys
  createdById String
  createdBy   User   @relation(fields: [createdById], references: [id])

  // Relations
  categories RequisitionCategory[]
  dealRequisitions DealRequisition[]

  @@map("requisition_templates")
}

model RequisitionCategory {
  id          String @id @default(cuid())
  name        String
  description String?
  orderIndex  Int
  isRequired  Boolean @default(false)

  // Foreign Keys
  templateId String
  template   RequisitionTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  // Relations
  items RequisitionItem[]

  @@map("requisition_categories")
}

model RequisitionItem {
  id          String  @id @default(cuid())
  name        String
  description String?
  isRequired  Boolean @default(false)
  orderIndex  Int
  acceptedFormats Json?
  maxFileSize Int?
  examples    Json?

  // Foreign Keys
  categoryId String
  category   RequisitionCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  // Relations
  documents Document[]

  @@map("requisition_items")
}

model DealRequisition {
  id          String   @id @default(cuid())
  customData  Json     // Stores the customized requisition structure
  isFinalized Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign Keys
  dealId     String
  deal       Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)
  templateId String
  template   RequisitionTemplate @relation(fields: [templateId], references: [id])
  userId     String
  user       User   @relation(fields: [userId], references: [id])

  @@unique([dealId]) // One requisition per deal
  @@map("deal_requisitions")
}

enum DocumentStatus {
  PENDING
  UPLOADED
  UNDER_REVIEW
  APPROVED
  REJECTED
  MISSING
}

model Document {
  id          String         @id @default(cuid())
  filename    String
  originalName String
  fileData    Bytes          // Binary storage for file content
  filePath    String?        // Optional: for backward compatibility or external storage
  fileSize    Int
  mimeType    String
  fileHash    String?        // For deduplication and integrity checking
  status      DocumentStatus @default(PENDING)
  version     Int            @default(1)
  remarks     String?
  templateItemId String?     // Store the frontend template item ID for matching
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Foreign Keys
  dealId            String
  deal              Deal             @relation(fields: [dealId], references: [id], onDelete: Cascade)
  requisitionItemId String?
  requisitionItem   RequisitionItem? @relation(fields: [requisitionItemId], references: [id])
  uploadedById      String
  uploadedBy        User             @relation("DocumentUploader", fields: [uploadedById], references: [id])

  // Relations
  versions      DocumentVersion[]
  reviewFindings ReviewFinding[]
  documentAnalyses DocumentAnalysis[]

  @@map("documents")
}

model DocumentVersion {
  id        String   @id @default(cuid())
  version   Int
  fileData  Bytes    // Binary storage for version content
  filePath  String?  // Optional: for backward compatibility
  fileSize  Int
  fileHash  String?  // For integrity checking
  createdAt DateTime @default(now())

  // Foreign Keys
  documentId   String
  document     Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  uploadedById String
  uploadedBy   User     @relation(fields: [uploadedById], references: [id])

  @@map("document_versions")
}

// ================================
// AI REVIEW SYSTEM
// ================================

enum PersonaType {
  FINANCIAL
  LEGAL
  OPERATIONAL
  HR
  COMMERCIAL_MARKET
  IT_TECHNOLOGY
  ESG
  TAX
  STRATEGIC_FIT
  REGULATORY
  INSURANCE
  INTELLECTUAL_PROPERTY
  REAL_ESTATE_ASSET
  CUSTOMER_SUPPLIER
  COMPLIANCE_RISK
}

model AIPersona {
  id             String      @id @default(cuid())
  name           String
  type           PersonaType
  description    String?
  promptTemplate String      @db.Text // Base prompt template for this persona
  isActive       Boolean     @default(true)
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  reviews Review[]
  aiReviews AIReview[]

  @@map("ai_personas")
}

enum ReviewStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  APPROVED
  REJECTED
  REQUIRES_ATTENTION
}

model Review {
  id              String       @id @default(cuid())
  status          ReviewStatus @default(PENDING)
  confidenceScore Int?         // 0-100
  summary         String?
  startedAt       DateTime?
  completedAt     DateTime?
  approvedAt      DateTime?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  // Foreign Keys
  dealId    String
  deal      Deal      @relation(fields: [dealId], references: [id], onDelete: Cascade)
  personaId String
  persona   AIPersona @relation(fields: [personaId], references: [id])
  reviewerId String?
  reviewer  User?     @relation(fields: [reviewerId], references: [id])

  // Relations
  findings ReviewFinding[]
  comments ReviewComment[]

  @@unique([dealId, personaId])
  @@map("reviews")
}

enum FindingType {
  RISK
  RECOMMENDATION
  OBSERVATION
  RED_FLAG
  COMPLIANCE_ISSUE
  FINANCIAL_CONCERN
  LEGAL_ISSUE
  TECHNICAL_DEBT
}

model ReviewFinding {
  id           String      @id @default(cuid())
  findingType  FindingType
  title        String
  description  String
  riskLevel    RiskLevel?
  recommendation String?
  createdAt    DateTime    @default(now())

  // Foreign Keys
  reviewId   String
  review     Review    @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  documentId String?
  document   Document? @relation(fields: [documentId], references: [id])

  @@map("review_findings")
}

model ReviewComment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())

  // Foreign Keys
  reviewId String
  review   Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  userId   String
  user     User   @relation(fields: [userId], references: [id])

  @@map("review_comments")
}

// ================================
// REPORTING & ANALYTICS
// ================================

enum ReportType {
  EXECUTIVE_SUMMARY
  DETAILED_ANALYSIS
  RISK_ASSESSMENT
  COMPLIANCE_REPORT
  FINANCIAL_ANALYSIS
  LEGAL_REVIEW
  TECHNICAL_ASSESSMENT
  CUSTOM
}

enum ReportStatus {
  GENERATING
  COMPLETED
  FAILED
  CANCELLED
}

model ReportGeneration {
  id          String       @id @default(cuid())
  type        ReportType
  status      ReportStatus @default(GENERATING)
  templateId  String?
  filePath    String?
  fileSize    Int?
  progress    Int          @default(0) // 0-100
  errorMessage String?
  generatedAt DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Foreign Keys
  dealId      String
  deal        Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)
  generatedById String
  generatedBy User @relation(fields: [generatedById], references: [id])

  @@map("report_generations")
}

model ReportTemplate {
  id           String     @id @default(cuid())
  name         String
  type         ReportType
  templateData Json
  isActive     Boolean    @default(true)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // Foreign Keys
  createdById String
  createdBy   User   @relation(fields: [createdById], references: [id])

  @@map("report_templates")
}

enum MetricType {
  DEAL_PROGRESS
  REVIEW_COMPLETION
  RISK_SCORE
  DOCUMENT_COUNT
  TIME_TO_COMPLETION
  USER_ACTIVITY
  SYSTEM_PERFORMANCE
}

model AnalyticsMetric {
  id           String     @id @default(cuid())
  metricType   MetricType
  value        Decimal
  metadata     Json?
  calculatedAt DateTime   @default(now())

  // Foreign Keys
  dealId String?
  deal   Deal?  @relation(fields: [dealId], references: [id])

  @@map("analytics_metrics")
}

// ================================
// COMMUNICATION & NOTIFICATIONS
// ================================

enum NotificationType {
  DEAL_CREATED
  DEAL_UPDATED
  DOCUMENT_UPLOADED
  REVIEW_COMPLETED
  REPORT_GENERATED
  COMMENT_ADDED
  DEADLINE_APPROACHING
  SYSTEM_ALERT
}

enum NotificationStatus {
  UNREAD
  READ
  ARCHIVED
}

model Notification {
  id        String             @id @default(cuid())
  type      NotificationType
  title     String
  message   String
  status    NotificationStatus @default(UNREAD)
  metadata  Json?
  createdAt DateTime           @default(now())
  readAt    DateTime?

  // Foreign Keys
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  dealId String?
  deal   Deal?  @relation(fields: [dealId], references: [id])

  @@map("notifications")
}

enum EmailStatus {
  PENDING
  SENT
  FAILED
  BOUNCED
}

model EmailLog {
  id        String      @id @default(cuid())
  template  String
  subject   String
  recipient String
  status    EmailStatus @default(PENDING)
  sentAt    DateTime?
  errorMessage String?
  metadata  Json?
  createdAt DateTime    @default(now())

  // Foreign Keys
  userId String?
  user   User?  @relation(fields: [userId], references: [id])

  @@map("email_logs")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  dealId   String
  deal     Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  parentId String?
  parent   Comment? @relation("CommentReplies", fields: [parentId], references: [id])

  // Relations
  replies Comment[] @relation("CommentReplies")

  @@map("comments")
}

// ================================
// SYSTEM & AUDIT
// ================================

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  UPLOAD
  DOWNLOAD
  APPROVE
  REJECT
  EXPORT
}

model AuditLog {
  id          String      @id @default(cuid())
  action      AuditAction
  entityType  String
  entityId    String?
  oldValues   Json?
  newValues   Json?
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime    @default(now())

  // Foreign Keys
  userId String?
  user   User?  @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// ================================
// AI REVIEW SYSTEM EXTENSIONS
// ================================

enum RiskSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum RecommendationPriority {
  LOW
  MEDIUM
  HIGH
}

enum VerificationStatus {
  VERIFIED
  FLAGGED
  PENDING
}

model AIReview {
  id              String       @id @default(cuid())
  dealId          String
  personaId       String
  status          ReviewStatus @default(PENDING)

  // Analysis results stored as JSON
  analysisResult  Json?        // Cumulative analysis that grows with each document

  // Progress tracking
  totalDocuments     Int       @default(0)
  processedDocuments Int       @default(0)
  currentDocument    String?   // Currently processing document ID
  completionPercentage Int     @default(0)

  // Timestamps
  startedAt    DateTime?
  completedAt  DateTime?
  lastUpdated  DateTime     @default(now())

  // Error handling
  errorMessage String?
  retryCount   Int         @default(0)
  maxRetries   Int         @default(3)

  // Cost tracking
  tokenUsage   Json?       // Track API usage and costs

  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Foreign Keys
  deal         Deal        @relation(fields: [dealId], references: [id], onDelete: Cascade)
  persona      AIPersona   @relation(fields: [personaId], references: [id])

  // Relations
  documentAnalyses DocumentAnalysis[]

  @@unique([dealId, personaId]) // One review per persona per deal
  @@map("ai_reviews")
}

model DocumentAnalysis {
  id              String    @id @default(cuid())
  reviewId        String
  documentId      String
  analysisStatus  ReviewStatus @default(PENDING)

  // Document processing
  extractedText   String?   @db.Text // OCR/parsed content
  analysisNotes   String?   @db.Text // Persona-specific analysis notes
  processingOrder Int       // Order in which document was processed

  // Results from this specific document
  documentInsights Json?    // Insights extracted from this document

  // Timestamps
  startedAt       DateTime?
  completedAt     DateTime?
  processedAt     DateTime  @default(now())

  // Error handling
  errorMessage    String?
  retryCount      Int       @default(0)

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Foreign Keys
  review          AIReview  @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  document        Document  @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@unique([reviewId, documentId]) // One analysis per document per review
  @@map("document_analyses")
}

// ================================
// BACKGROUND JOBS
// ================================

enum JobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

model BackgroundJob {
  id          String    @id @default(cuid())
  type        String
  status      JobStatus @default(PENDING)
  payload     Json
  result      Json?
  errorMessage String?
  attempts    Int       @default(0)
  maxAttempts Int       @default(3)
  scheduledAt DateTime?
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("background_jobs")
}
