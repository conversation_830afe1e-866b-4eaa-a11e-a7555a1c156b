import { PrismaClient, DealType, UserRole } from '@prisma/client';
import { hashPassword } from '../src/utils';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create minimal organization for system user
  const organization = await prisma.organization.upsert({
    where: { domain: 'system.local' },
    update: {},
    create: {
      name: 'System',
      domain: 'system.local',
      settings: {},
    },
  });

  // Create minimal system user for template creation
  const systemUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: await hashPassword('system'),
      firstName: 'System',
      lastName: 'User',
      role: UserRole.ADMIN,
      emailVerified: true,
      organizationId: organization.id,
    },
  });

  // Create requisition template
  const requisitionTemplate = await prisma.requisitionTemplate.create({
    data: {
      name: 'Standard M&A Due Diligence',
      dealType: DealType.MA,
      industry: 'Technology',
      templateData: {
        description: 'Comprehensive due diligence checklist for M&A transactions',
        version: '1.0',
      },
      createdById: systemUser.id,
      categories: {
        create: [
          {
            name: 'Corporate Documents',
            description: 'Corporate governance and organizational documents',
            orderIndex: 1,
            items: {
              create: [
                {
                  name: 'Articles of Incorporation',
                  description: 'Current articles of incorporation and all amendments',
                  isRequired: true,
                  orderIndex: 1,
                },
                {
                  name: 'Corporate Bylaws',
                  description: 'Current corporate bylaws and all amendments',
                  isRequired: true,
                  orderIndex: 2,
                },
                {
                  name: 'Board Resolutions',
                  description: 'Board resolutions for the past 3 years',
                  isRequired: true,
                  orderIndex: 3,
                },
              ],
            },
          },
          {
            name: 'Financial Information',
            description: 'Financial statements and related documentation',
            orderIndex: 2,
            items: {
              create: [
                {
                  name: 'Audited Financial Statements',
                  description: 'Audited financial statements for the past 3 years',
                  isRequired: true,
                  orderIndex: 1,
                },
                {
                  name: 'Management Accounts',
                  description: 'Monthly management accounts for the current year',
                  isRequired: true,
                  orderIndex: 2,
                },
                {
                  name: 'Tax Returns',
                  description: 'Corporate tax returns for the past 3 years',
                  isRequired: true,
                  orderIndex: 3,
                },
              ],
            },
          },
        ],
      },
    },
  });

  console.log('✅ Created requisition template');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📊 Summary:');
  console.log(`- Requisition Template: ${requisitionTemplate.name}`);
  console.log('\n✨ The requisition template is ready for use in the application.');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
