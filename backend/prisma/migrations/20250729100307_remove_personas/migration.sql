/*
  Warnings:

  - The values [<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,T<PERSON><PERSON><PERSON><PERSON><PERSON>] on the enum `PersonaType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PersonaType_new" AS ENUM ('OPERATIONAL', 'COMPLIANC<PERSON>', 'STRATEGIC', 'RISK', 'ESG');
ALTER TABLE "ai_personas" ALTER COLUMN "type" TYPE "PersonaType_new" USING ("type"::text::"PersonaType_new");
ALTER TYPE "PersonaType" RENAME TO "PersonaType_old";
ALTER TYPE "PersonaType_new" RENAME TO "PersonaType";
DROP TYPE "PersonaType_old";
COMMIT;
