/*
  Warnings:

  - The values [CO<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>RA<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>] on the enum `PersonaType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PersonaType_new" AS ENUM ('FINANCIAL', '<PERSON>EG<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'HR', 'COMMERCIAL_MARKET', 'IT_TECHNOLOGY', 'ESG', 'TAX', 'STRATEGIC_FIT', 'REGULATORY', 'INSURANCE', 'INTELLECTUAL_PROPERTY', 'REAL_ESTATE_ASSET', 'CUSTOMER_SUPPLIER', 'COMPLIANCE_RISK');
ALTER TABLE "ai_personas" ALTER COLUMN "type" TYPE "PersonaType_new" USING ("type"::text::"PersonaType_new");
ALTER TYPE "PersonaType" RENAME TO "PersonaType_old";
ALTER TYPE "PersonaType_new" RENAME TO "PersonaType";
DROP TYPE "PersonaType_old";
COMMIT;
