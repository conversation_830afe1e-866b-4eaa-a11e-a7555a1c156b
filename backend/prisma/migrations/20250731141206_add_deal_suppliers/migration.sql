/*
  Warnings:

  - The values [ESG] on the enum `PersonaType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PersonaType_new" AS ENUM ('OPERATIONAL', 'COMPLIANCE', 'STRATEGIC', 'RISK');
ALTER TABLE "ai_personas" ALTER COLUMN "type" TYPE "PersonaType_new" USING ("type"::text::"PersonaType_new");
ALTER TYPE "PersonaType" RENAME TO "PersonaType_old";
ALTER TYPE "PersonaType_new" RENAME TO "PersonaType";
DROP TYPE "PersonaType_old";
COMMIT;

-- CreateTable
CREATE TABLE "deal_suppliers" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "jurisdiction" TEXT NOT NULL,
    "contactInfo" JSONB,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dealId" TEXT NOT NULL,

    CONSTRAINT "deal_suppliers_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "deal_suppliers" ADD CONSTRAINT "deal_suppliers_dealId_fkey" FOREIGN KEY ("dealId") REFERENCES "deals"("id") ON DELETE CASCADE ON UPDATE CASCADE;
