{"name": "due-diligence-nexus-platform", "version": "1.0.0", "description": "Fullstack Due Diligence Nexus Platform", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm run start", "start:frontend": "cd frontend && npm run start"}, "keywords": ["fullstack", "due-diligence", "nexus", "platform"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.2.0"}}