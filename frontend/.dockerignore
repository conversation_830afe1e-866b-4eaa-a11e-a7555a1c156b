# Node dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Logs and temporary data
logs
*.log
*.pid
*.seed
*.pid.lock
.npm
.cache
.vite
tmp/
temp/
.nyc_output
coverage
*.lcov
*.tsbuildinfo
.eslintcache

# System files
.DS_Store
*.swp
*.swo
.idea
.vscode/

# Git
.git
.gitignore

# Build outputs
dist
dist-ssr
*.tgz

# REPL & yarn
.node_repl_history
.yarn-integrity

# Misc dev tooling caches
.rpt2_cache/
.rts2_cache_*/
.parcel-cache

# Storybook output
storybook-static

# Ignore tests (optional, depends if you run tests in Docker)
**/*.test.*
**/*.spec.*

# Ignore documentation (optional)
docs/
README.md
