import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  FileText,
  Download,
  Printer,
  Loader2,
  Bot,
  Eye,
  User,
  Clock
} from "lucide-react";
import { Link, useSearchParams } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { reportService, type ReportGeneration as ReportGenerationType, ReportContent } from "../services/reportService";
import { aiReviewService, AIReviewResult } from "../services/aiReviewService";
import { dealService } from "../services/dealService";
import { Deal } from "../types/deal";

// Helper function to extract and format report content
const extractReportContent = (reportContent: any): string => {
  if (!reportContent) {
    return 'No content available';
  }

  let content = '';

  // Handle different content structures
  if (typeof reportContent.content === 'string') {
    content = reportContent.content;
  } else if (typeof reportContent.content === 'object') {
    // Handle nested content object
    if (reportContent.content.content) {
      content = reportContent.content.content;
    } else {
      // Try to find content in the object
      const contentKeys = ['content', 'text', 'markdown', 'body'];
      for (const key of contentKeys) {
        if (reportContent.content[key] && typeof reportContent.content[key] === 'string') {
          content = reportContent.content[key];
          break;
        }
      }
      if (!content) {
        content = JSON.stringify(reportContent.content, null, 2);
      }
    }
  } else if (typeof reportContent === 'string') {
    content = reportContent;
  } else {
    // Try to extract content from object structure
    content = JSON.stringify(reportContent, null, 2);
  }

  if (!content || typeof content !== 'string') {
    return 'No content available';
  }

  return content
    // Remove any JSON wrapper if present
    .replace(/^```json\s*/, '')
    .replace(/\s*```$/, '')
    .replace(/^```\s*/, '')
    .replace(/\s*```$/, '')

    // Remove JSON object wrapper if content is wrapped
    .replace(/^\s*{\s*"content"\s*:\s*"/, '')
    .replace(/"\s*}\s*$/, '')

    // Handle escaped characters
    .replace(/\\n/g, '\n')
    .replace(/\\"/g, '"')
    .replace(/\\'/g, "'")
    .replace(/\\\\/g, '\\')
    .replace(/\\t/g, '\t')

    // Clean up extra whitespace but preserve intentional formatting
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Multiple newlines to double newlines
    .replace(/^\s+|\s+$/g, '') // Trim leading/trailing whitespace

    // Ensure proper markdown header formatting
    .replace(/^#+\s*/gm, (match) => match.replace(/\s+$/, ' '))

    .trim();
};

// Helper function to convert markdown to HTML
const markdownToHtml = (reportContent: any): string => {
  const formatted = extractReportContent(reportContent);

  return formatted
    // Headers
    .replace(/^# (.+)$/gm, '<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900 border-b-2 border-gray-200 pb-2">$1</h1>')
    .replace(/^## (.+)$/gm, '<h2 class="text-2xl font-semibold mt-6 mb-4 text-gray-800">$1</h2>')
    .replace(/^### (.+)$/gm, '<h3 class="text-xl font-medium mt-5 mb-3 text-gray-700">$1</h3>')
    .replace(/^#### (.+)$/gm, '<h4 class="text-lg font-medium mt-4 mb-2 text-gray-600">$1</h4>')

    // Bold and italic
    .replace(/\*\*(.+?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
    .replace(/\*(.+?)\*/g, '<em class="italic text-gray-700">$1</em>')

    // Lists - handle bullet points
    .replace(/^[\s]*[-*+]\s+(.+)$/gm, '<li class="ml-6 mb-2 list-disc">$1</li>')

    // Numbered lists
    .replace(/^[\s]*\d+\.\s+(.+)$/gm, '<li class="ml-6 mb-2 list-decimal">$1</li>')

    // Wrap consecutive list items in ul tags
    .replace(/(<li class="ml-6 mb-2 list-disc">.*?<\/li>)(?:\s*<li class="ml-6 mb-2 list-disc">.*?<\/li>)*/gs, '<ul class="my-4">$&</ul>')
    .replace(/(<li class="ml-6 mb-2 list-decimal">.*?<\/li>)(?:\s*<li class="ml-6 mb-2 list-decimal">.*?<\/li>)*/gs, '<ol class="my-4">$&</ol>')

    // Paragraphs - convert double newlines to paragraph breaks
    .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 leading-relaxed">')

    // Single newlines to line breaks within paragraphs
    .replace(/\n/g, '<br>')

    // Wrap in initial paragraph tag
    .replace(/^/, '<p class="mb-4 text-gray-700 leading-relaxed">')
    .replace(/$/, '</p>')

    // Clean up empty paragraphs
    .replace(/<p class="mb-4 text-gray-700 leading-relaxed"><\/p>/g, '')
    .replace(/<p class="mb-4 text-gray-700 leading-relaxed">\s*<\/p>/g, '');
};

const ReportGeneration = () => {
  const [searchParams] = useSearchParams();
  const dealId = searchParams.get('dealId') || '';
  const { toast } = useToast();

  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);

  // Data states
  const [deal, setDeal] = useState<Deal | null>(null);
  const [aiReviewData, setAiReviewData] = useState<AIReviewResult[] | null>(null);
  const [currentReport, setCurrentReport] = useState<ReportGenerationType | null>(null);
  const [reportContent, setReportContent] = useState<ReportContent | null>(null);

  // UI states
  const [generationProgress, setGenerationProgress] = useState(0);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, [dealId]);

  const loadInitialData = async () => {
    if (!dealId) {
      console.log('No dealId provided');
      setIsLoading(false);
      return;
    }

    console.log('Loading data for dealId:', dealId);

    try {
      setIsLoading(true);

      // Load deal data
      console.log('Loading deal data...');
      const dealData = await dealService.getDealById(dealId);
      console.log('Deal data loaded:', dealData);
      setDeal(dealData);

      // Load AI review data
      console.log('Loading AI review data...');
      const aiData = await aiReviewService.getResults(dealId);
      console.log('AI review data loaded:', aiData);
      setAiReviewData(aiData.results);

      // Load existing reports
      console.log('Loading existing reports...');
      const reportsData = await reportService.getReportsByDeal(dealId);
      console.log('Reports data loaded:', reportsData);

      // If there's a completed report, show it
      const completedReport = reportsData.find(r => r.status === 'COMPLETED');
      if (completedReport) {
        setCurrentReport(completedReport);
        const content = reportService.parseReportContent(completedReport);
        setReportContent(content);
      }

    } catch (error) {
      console.error('Failed to load data:', error);
      toast({
        title: "Error Loading Data",
        description: error instanceof Error ? error.message : "Failed to load deal and AI review data.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    if (!dealId) return;

    try {
      setIsGenerating(true);
      setGenerationProgress(0);

      // Check if AI review is completed
      if (!aiReviewData || aiReviewData.filter(r => r.status === 'COMPLETED').length === 0) {
        toast({
          title: "AI Review Required",
          description: "Please complete the AI review before generating a report.",
          variant: "destructive"
        });
        setIsGenerating(false);
        return;
      }

      // Start report generation
      const report = await reportService.generateReport(dealId, {
        reportType: 'EXECUTIVE_SUMMARY'
      });

      setCurrentReport(report);

      // Poll for completion
      await reportService.pollReportStatus(report.id, (status) => {
        setGenerationProgress(status.progress);
      });

      // Reload reports and show the completed one
      await loadInitialData();

      toast({
        title: "Report Generated Successfully",
        description: "Your detailed due diligence report is now available.",
      });

    } catch (error: any) {
      console.error('Failed to generate report:', error);
      toast({
        title: "Report Generation Failed",
        description: error.message || "Failed to generate report. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadReport = async () => {
    if (!currentReport || !reportContent) return;

    try {
      // For now, create a text file with the formatted content
      // TODO: Implement proper PDF generation on the backend
      const cleanContent = extractReportContent(reportContent);

      const blob = new Blob([cleanContent], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `due-diligence-report-${deal?.title || 'report'}-${new Date().toISOString().split('T')[0]}.txt`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Report Downloaded",
        description: "Report has been downloaded as a text file.",
      });
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download the report. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link to="/dashboard">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
              <FileText className="w-8 h-8 text-primary" />
              Due Diligence Report
            </h1>
            <p className="text-muted-foreground">
              Deal ID: {dealId} - {deal?.title || 'Loading...'}
            </p>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin mr-2" />
              <span>Loading deal and AI review data...</span>
            </CardContent>
          </Card>
        )}

        {/* No AI Review Data */}
        {!isLoading && (!aiReviewData || aiReviewData.filter(r => r.status === 'COMPLETED').length === 0) && (
          <Card>
            <CardContent className="text-center py-8">
              <Bot className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">AI Review Required</h3>
              <p className="text-muted-foreground mb-4">
                Complete the AI review process before generating a due diligence report.
              </p>
              <Link to={`/ai-review?dealId=${dealId}`}>
                <Button>
                  Go to AI Review
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Report Content */}
        {!isLoading && aiReviewData && aiReviewData.filter(r => r.status === 'COMPLETED').length > 0 && (
          <>
            {!currentReport || currentReport.status !== 'COMPLETED' ? (
              <Card>
                <CardHeader>
                  <CardTitle>Executive Summary</CardTitle>
                  <CardDescription>Comprehensive due diligence analysis preview</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Deal Overview */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Deal Overview</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><strong>Target Company:</strong> {deal?.seller || 'Not specified'}</div>
                      <div><strong>Deal Type:</strong> {deal?.type || 'Not specified'}</div>
                      <div><strong>Deal Value:</strong> {deal?.value ? `$${deal.value.toLocaleString()}` : 'Not specified'}</div>
                      <div><strong>Jurisdiction:</strong> {deal?.jurisdiction || 'Not specified'}</div>
                    </div>
                  </div>

                  {/* AI Analysis Summary */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">AI Analysis Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {aiReviewData.filter(r => r.status === 'COMPLETED').map((result) => (
                        <div key={result.persona.id} className="p-3 border rounded-lg">
                          <div className="font-medium text-sm mb-1">{result.persona.name}</div>
                          <div className="text-xs text-muted-foreground mb-2">
                            Confidence: {result.analysis?.confidence_score || 0}%
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2">
                            {result.analysis?.summary || 'Analysis completed'}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Click "Generate Full Report" to see detailed analysis</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* Report Header */}
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-2xl">Due Diligence Report</CardTitle>
                        <CardDescription>
                          {deal?.title || 'Deal Analysis'} - {reportService.formatReportType(currentReport.type)}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-sm">
                          <Clock className="w-4 h-4 mr-1" />
                          Generated {currentReport.generatedAt ? new Date(currentReport.generatedAt).toLocaleDateString() : 'Recently'}
                        </Badge>
                        <Badge variant="outline" className="text-sm">
                          <User className="w-4 h-4 mr-1" />
                          {currentReport.generatedBy.firstName} {currentReport.generatedBy.lastName}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                </Card>

                {/* Report Content Display */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="w-5 h-5" />
                      Generated Report Content
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {reportContent ? (
                      <div className="space-y-6">
                        {reportContent.format === 'markdown' ? (
                          <div className="prose prose-lg max-w-none">
                            <div
                              className="text-base leading-relaxed"
                              style={{
                                fontFamily: 'system-ui, -apple-system, sans-serif',
                                lineHeight: '1.7'
                              }}
                              dangerouslySetInnerHTML={{
                                __html: markdownToHtml(reportContent)
                              }}
                            />
                          </div>
                        ) : reportContent.format === 'text' ? (
                          <div className="prose max-w-none">
                            <div className="whitespace-pre-wrap text-sm bg-gray-50 p-6 rounded-lg border leading-relaxed font-mono">
                              {extractReportContent(reportContent)}
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {reportContent.sections && Object.entries(reportContent.sections).map(([section, content]) => (
                              <div key={section} className="border rounded-lg p-4">
                                <h3 className="text-lg font-semibold mb-2 capitalize">
                                  {section.replace(/_/g, ' ')}
                                </h3>
                                <div className="text-sm text-gray-700">
                                  {typeof content === 'string' ? content : (
                                    <pre className="whitespace-pre-wrap bg-gray-50 p-2 rounded text-xs">
                                      {JSON.stringify(content, null, 2)}
                                    </pre>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>Loading report content...</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </>
        )}

        {/* Generation Progress */}
        {isGenerating && (
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Generating comprehensive report...</span>
                  <span>{generationProgress}%</span>
                </div>
                <Progress value={generationProgress} className="w-full" />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        {!isLoading && (
          <div className="flex justify-between">
            <Link to={`/ai-review?dealId=${dealId}`}>
              <Button variant="outline">
                Back to Review
              </Button>
            </Link>
            <div className="flex gap-2">
              {aiReviewData && aiReviewData.filter(r => r.status === 'COMPLETED').length > 0 && (
                <Button variant="outline" onClick={handleGenerateReport} disabled={isGenerating}>
                  <Printer className="w-4 h-4 mr-2" />
                  {isGenerating ? 'Generating...' : 'Generate Full Report'}
                </Button>
              )}
              {currentReport && currentReport.status === 'COMPLETED' && (
                <Button onClick={handleDownloadReport}>
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportGeneration;
