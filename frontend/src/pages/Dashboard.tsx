import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  DollarSign,
  Calendar,
  Users,
  TrendingUp,
  BarChart3
} from "lucide-react";
import { Link } from "react-router-dom";

const Dashboard = () => {
  const deals = [
    {
      id: 1,
      title: "TechCorp Acquisition",
      type: "M&A",
      value: "$50M",
      status: "In Progress",
      progress: 65,
      dueDate: "2024-08-15",
      riskLevel: "Medium"
    },
    {
      id: 2,
      title: "StartupXYZ Investment",
      type: "Investment",
      value: "$15M",
      status: "Draft",
      progress: 25,
      dueDate: "2024-08-30",
      riskLevel: "Low"
    },
    {
      id: 3,
      title: "Global Partners LLC",
      type: "Partnership",
      value: "$100M",
      status: "Completed",
      progress: 100,
      dueDate: "2024-07-01",
      riskLevel: "High"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-green-100 text-green-800";
      case "In Progress": return "bg-blue-100 text-blue-800";
      case "Draft": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "High": return "text-red-600";
      case "Medium": return "text-yellow-600";
      case "Low": return "text-green-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-light text-foreground">Dashboard</h1>
            <p className="text-muted-foreground font-light">Overview of your due diligence processes</p>
          </div>
          <Link to="/register-deal">
            <Button className="flex items-center gap-2 font-light">
              <Plus className="w-4 h-4" />
              New Deal
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Total Deals</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">24</div>
              <p className="text-xs text-muted-foreground font-light">+2 from last month</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">8</div>
              <p className="text-xs text-muted-foreground font-light">Active deals</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">12</div>
              <p className="text-xs text-muted-foreground font-light">This quarter</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Risk Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">7</div>
              <p className="text-xs text-muted-foreground font-light">High priority</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">$2.4B</div>
              <p className="text-xs text-muted-foreground font-light">Combined deal value</p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="font-light">Deal Status Distribution</CardTitle>
              <CardDescription className="font-light">Current status of all deals</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light">In Progress</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-muted rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{width: '35%'}}></div>
                    </div>
                    <span className="text-sm font-light">35%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light">Completed</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-muted rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{width: '50%'}}></div>
                    </div>
                    <span className="text-sm font-light">50%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light">Draft</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-muted rounded-full h-2">
                      <div className="bg-yellow-500 h-2 rounded-full" style={{width: '15%'}}></div>
                    </div>
                    <span className="text-sm font-light">15%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="font-light">Risk Overview</CardTitle>
              <CardDescription className="font-light">Risk distribution across deals</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-red-600">High Risk</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-muted rounded-full h-2">
                      <div className="bg-red-500 h-2 rounded-full" style={{width: '20%'}}></div>
                    </div>
                    <span className="text-sm font-light">20%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-yellow-600">Medium Risk</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-muted rounded-full h-2">
                      <div className="bg-yellow-500 h-2 rounded-full" style={{width: '45%'}}></div>
                    </div>
                    <span className="text-sm font-light">45%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-light text-green-600">Low Risk</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-muted rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{width: '35%'}}></div>
                    </div>
                    <span className="text-sm font-light">35%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Deals */}
        <Card>
          <CardHeader>
            <CardTitle className="font-light">Recent Deal Activity</CardTitle>
            <CardDescription className="font-light">Latest updates from your active deals</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input placeholder="Search deals..." className="pl-10 font-light" />
                </div>
              </div>
              <Button variant="outline" className="flex items-center gap-2 font-light">
                <Filter className="w-4 h-4" />
                Filter
              </Button>
            </div>

            {/* Deals Table */}
            <div className="space-y-4">
              {deals.map((deal) => (
                <div key={deal.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium text-lg">{deal.title}</h3>
                        <Badge className={getStatusColor(deal.status)}>{deal.status}</Badge>
                        <Badge variant="outline" className="font-light">{deal.type}</Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          <span className="font-light">{deal.value}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span className="font-light">Due: {deal.dueDate}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <AlertTriangle className={`w-4 h-4 ${getRiskColor(deal.riskLevel)}`} />
                          <span className="font-light">{deal.riskLevel} Risk</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <div className="w-full sm:w-32">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="font-light">Progress</span>
                          <span className="font-light">{deal.progress}%</span>
                        </div>
                        <Progress value={deal.progress} className="w-full" />
                      </div>
                      <div className="flex gap-2">
                        <Link to={`/document-upload?dealId=${deal.id}`}>
                          <Button variant="outline" size="sm" className="font-light">
                            Documents
                          </Button>
                        </Link>
                        <Link to={`/ai-review?dealId=${deal.id}`}>
                          <Button variant="outline" size="sm" className="font-light">
                            Review
                          </Button>
                        </Link>
                        <Link to={`/report-generation?dealId=${deal.id}`}>
                          <Button size="sm" className="font-light">
                            Report
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;