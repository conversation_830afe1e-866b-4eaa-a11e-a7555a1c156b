import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Search, 
  FileText, 
  AlertTriangle, 
  TrendingUp, 
  DollarSign,
  Calendar,
  MessageSquare,
  Lightbulb,
  BarChart3
} from "lucide-react";

const AIInsights = () => {
  const [query, setQuery] = useState("");
  const [insights] = useState([
    {
      id: 1,
      type: "Risk Alert",
      title: "High termination risk detected in TechCorp deal",
      description: "AI found 3 termination clauses with 30-day notice periods",
      confidence: 95,
      severity: "high",
      documents: ["contract.pdf", "terms.docx"],
      timestamp: "2 hours ago"
    },
    {
      id: 2,
      type: "Financial Insight",
      title: "Revenue trend analysis for StartupXYZ",
      description: "3-year growth pattern shows 45% CAGR with Q4 seasonality",
      confidence: 87,
      severity: "medium",
      documents: ["financials.xlsx", "audit.pdf"],
      timestamp: "4 hours ago"
    },
    {
      id: 3,
      type: "Compliance Check",
      title: "GDPR compliance gaps identified",
      description: "Missing data processing agreements in 2 subsidiary contracts",
      confidence: 92,
      severity: "high",
      documents: ["privacy_policy.pdf", "data_agreement.docx"],
      timestamp: "6 hours ago"
    }
  ]);

  const quickQuestions = [
    "Show me all high-risk clauses across deals",
    "What are the key financial metrics for Q3?",
    "Any compliance issues requiring immediate attention?",
    "Compare termination clauses in current deals",
    "Revenue trends in the tech sector deals"
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high": return "bg-red-100 text-red-800 border-red-200";
      case "medium": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low": return "bg-green-100 text-green-800 border-green-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Risk Alert": return <AlertTriangle className="w-4 h-4" />;
      case "Financial Insight": return <DollarSign className="w-4 h-4" />;
      case "Compliance Check": return <FileText className="w-4 h-4" />;
      default: return <Brain className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-3xl font-light text-foreground">AI Insights</h1>
            <p className="text-muted-foreground font-light">Ask questions and get AI-powered analysis of your deals and documents</p>
          </div>

          {/* AI Query Input */}
          <Card>
            <CardContent className="p-6">
              <div className="flex gap-4">
                <div className="flex-1 relative">
                  <Brain className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                  <Input 
                    placeholder="Ask me anything about your deals, documents, or compliance..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="pl-12 py-3 text-base font-light"
                  />
                </div>
                <Button className="px-6">
                  <Search className="w-4 h-4 mr-2" />
                  Ask AI
                </Button>
              </div>

              {/* Quick Questions */}
              <div className="mt-4">
                <p className="text-sm text-muted-foreground mb-3 font-light">Quick questions:</p>
                <div className="flex flex-wrap gap-2">
                  {quickQuestions.map((question, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => setQuery(question)}
                      className="text-xs font-light"
                    >
                      {question}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Active Insights</CardTitle>
              <Lightbulb className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">23</div>
              <p className="text-xs text-muted-foreground font-light">+5 from yesterday</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">High Priority</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">7</div>
              <p className="text-xs text-muted-foreground font-light">Require attention</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Documents Analyzed</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">156</div>
              <p className="text-xs text-muted-foreground font-light">Across all deals</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">AI Confidence</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">91%</div>
              <p className="text-xs text-muted-foreground font-light">Average accuracy</p>
            </CardContent>
          </Card>
        </div>

        {/* Insights List */}
        <Card>
          <CardHeader>
            <CardTitle className="font-light">Recent AI Insights</CardTitle>
            <CardDescription className="font-light">AI-generated findings and recommendations from your documents</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.map((insight) => (
                <div key={insight.id} className="border rounded-lg p-4 hover:bg-muted/30 transition-colors">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(insight.type)}
                          <span className="text-sm font-light text-muted-foreground">{insight.type}</span>
                        </div>
                        <Badge className={getSeverityColor(insight.severity)}>
                          {insight.severity} priority
                        </Badge>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <TrendingUp className="w-3 h-3" />
                          {insight.confidence}% confidence
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="font-medium text-foreground mb-1">{insight.title}</h3>
                        <p className="text-sm text-muted-foreground font-light">{insight.description}</p>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <FileText className="w-4 h-4" />
                          {insight.documents.length} documents
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {insight.timestamp}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2">
                      <Button variant="outline" size="sm">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Discuss
                      </Button>
                      <Button variant="outline" size="sm">
                        View Documents
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AIInsights;