import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "react-router-dom";
import { 
  ArrowLeft, 
  FileText, 
  Brain, 
  Shield, 
  Users, 
  TrendingUp, 
  CheckCircle,
  Search,
  Clock,
  Download,
  MessageSquare,
  Workflow
} from "lucide-react";

const Features = () => {
  const mainFeatures = [
    {
      icon: <FileText className="h-12 w-12 text-blue-600" />,
      title: "Document Management",
      description: "Centralized, secure document repository with advanced organization and search capabilities.",
      features: [
        "Drag-and-drop file uploads",
        "Version control and history",
        "Advanced search and filtering",
        "Bulk document operations",
        "Custom folder structures"
      ]
    },
    {
      icon: <Brain className="h-12 w-12 text-purple-600" />,
      title: "AI-Powered Analysis",
      description: "Intelligent document analysis and risk assessment using advanced machine learning.",
      features: [
        "Automated document classification",
        "Risk factor identification",
        "Key information extraction",
        "Anomaly detection",
        "Predictive insights"
      ]
    },
    {
      icon: <Users className="h-12 w-12 text-green-600" />,
      title: "Team Collaboration",
      description: "Seamless collaboration tools designed for distributed due diligence teams.",
      features: [
        "Real-time commenting",
        "Task assignment and tracking",
        "Team workspaces",
        "Permission management",
        "Activity notifications"
      ]
    },
    {
      icon: <TrendingUp className="h-12 w-12 text-orange-600" />,
      title: "Analytics & Reporting",
      description: "Comprehensive reporting and analytics to track progress and generate insights.",
      features: [
        "Progress dashboards",
        "Custom report generation",
        "Data visualization",
        "Export capabilities",
        "Performance metrics"
      ]
    }
  ];

  const additionalFeatures = [
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Enterprise Security",
      description: "Bank-grade security with encryption, audit trails, and compliance features."
    },
    {
      icon: <Search className="h-6 w-6" />,
      title: "Advanced Search",
      description: "Powerful search across all documents with filters, tags, and AI-powered suggestions."
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Workflow Automation",
      description: "Automated workflows and checklists to streamline repetitive tasks."
    },
    {
      icon: <Download className="h-6 w-6" />,
      title: "Export & Integration",
      description: "Export data in multiple formats and integrate with existing tools."
    },
    {
      icon: <MessageSquare className="h-6 w-6" />,
      title: "Communication Hub",
      description: "Centralized communication with threaded discussions and notifications."
    },
    {
      icon: <Workflow className="h-6 w-6" />,
      title: "Custom Workflows",
      description: "Create and customize workflows to match your organization's processes."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link to="/" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Link>
            </div>
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-primary">Due Diligence Nexus</h1>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/about" className="text-muted-foreground hover:text-primary transition-colors">
                About
              </Link>
              <Link to="/contact" className="text-muted-foreground hover:text-primary transition-colors">
                Contact
              </Link>
              <Link to="/login">
                <Button variant="outline" size="sm">
                  Sign In
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="sm">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto text-center">
          <Badge variant="secondary" className="mb-4">
            Comprehensive Feature Set
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Everything You Need for Due Diligence
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Our platform provides a complete suite of tools designed specifically for modern 
            due diligence processes, from document management to AI-powered insights.
          </p>
        </div>
      </section>

      {/* Main Features */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/50">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {mainFeatures.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardHeader>
                  <div className="mb-4">{feature.icon}</div>
                  <CardTitle className="text-2xl">{feature.title}</CardTitle>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.features.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                        <span className="text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Features */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Additional Capabilities
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Beyond our core features, we provide additional tools and capabilities to enhance 
              your due diligence workflow.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalFeatures.map((feature, index) => (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center mb-2">
                    <div className="p-2 bg-primary/10 rounded-lg mr-3">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                  <CardDescription>
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/50">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Seamless Integration
          </h2>
          <p className="text-xl text-muted-foreground mb-12 max-w-3xl mx-auto">
            Our platform integrates with the tools you already use, ensuring a smooth workflow 
            without disrupting your existing processes.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold mb-2">Document Systems</h3>
              <p className="text-muted-foreground text-sm">
                Connect with SharePoint, Google Drive, Dropbox, and other document management systems.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold mb-2">Communication Tools</h3>
              <p className="text-muted-foreground text-sm">
                Integrate with Slack, Microsoft Teams, and email systems for seamless communication.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold mb-2">Business Applications</h3>
              <p className="text-muted-foreground text-sm">
                Connect with CRM systems, project management tools, and financial applications.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Experience These Features Today
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Start your free trial and see how our comprehensive feature set can transform 
            your due diligence process.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/signup">
              <Button size="lg" className="w-full sm:w-auto">
                Start Free Trial
              </Button>
            </Link>
            <Link to="/contact">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Schedule Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-muted/30 py-12 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">Due Diligence Nexus</h3>
              <p className="text-muted-foreground">
                The leading platform for comprehensive due diligence management.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/features" className="hover:text-primary transition-colors">Features</Link></li>
                <li><Link to="/about" className="hover:text-primary transition-colors">About</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/contact" className="hover:text-primary transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Account</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link to="/login" className="hover:text-primary transition-colors">Sign In</Link></li>
                <li><Link to="/signup" className="hover:text-primary transition-colors">Sign Up</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 Due Diligence Nexus Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Features;
