import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  DollarSign,
  Calendar,
  AlertTriangle,
  FileText,
  Users,
  Building,
  TrendingUp,
  Upload,
  Bot,
  FileBarChart,
  Check,
  ChevronRight,
  Loader2,
  RefreshCw,
  HandCoins,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { dealService } from "@/services/dealService";
import {
  Deal,
  DealStatus,
  DealType,
  DEAL_STATUS_LABELS,
  DEAL_TYPE_LABELS,
} from "@/types/deal";
import { getCurrencySymbol } from "@/components/ui/currency-select";

const Deals = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<DealStatus | "all">("all");
  const [filterType, setFilterType] = useState<DealType | "all">("all");
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();

  // Load deals
  const loadDeals = async (showRefreshLoader = false) => {
    try {
      if (showRefreshLoader) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const query = {
        page,
        limit: 20,
        search: searchQuery || undefined,
        status: filterStatus !== "all" ? filterStatus : undefined,
        type: filterType !== "all" ? filterType : undefined,
        sortBy: "updatedAt" as const,
        sortOrder: "desc" as const,
      };

      const response = await dealService.getDeals(query);
      setDeals(response.deals);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error("Failed to load deals:", error);
      toast({
        title: "Error",
        description: "Failed to load deals. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load deals on component mount and when filters change
  useEffect(() => {
    loadDeals();
  }, [page, filterStatus, filterType]);

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (page === 1) {
        loadDeals();
      } else {
        setPage(1); // This will trigger loadDeals via the page useEffect
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle deal deletion
  const handleDeleteDeal = async (dealId: string) => {
    try {
      await dealService.deleteDeal(dealId);
      toast({
        title: "Success",
        description: "Deal deleted successfully.",
      });
      loadDeals(true);
    } catch (error) {
      console.error("Failed to delete deal:", error);
      toast({
        title: "Error",
        description: "Failed to delete deal. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Helper functions
  const getStatusColor = (status: DealStatus) => {
    return dealService.getStatusColor(status);
  };

  const getRiskLevel = (deal: Deal) => {
    return dealService.getRiskLevel(deal);
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "High":
        return "text-red-600";
      case "Medium":
        return "text-yellow-600";
      case "Low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const getCurrentStep = (deal: Deal) => {
    if (deal.status === "DRAFT") return 1;
    if (deal.status === "ACTIVE" && deal.progress < 50) return 1;
    if (deal.status === "ACTIVE" && deal.progress >= 50) return 2;
    if (deal.status === "DOCUMENTS_SUBMITTED") return 2; // Documents submitted, ready for AI review
    if (deal.status === "UNDER_REVIEW" || deal.status === "PENDING_APPROVAL")
      return 2;
    if (deal.status === "COMPLETED") return 3;
    return 1;
  };

  // No need for client-side filtering since we're doing it on the server

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-light text-foreground">Deals</h1>
            <p className="text-muted-foreground font-light">
              Manage and track all your deal processes
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadDeals(true)}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`w-4 h-4 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            <Link to="/register-deal">
              <Button className="flex items-center gap-2 font-light">
                <Plus className="w-4 h-4" />
                Register New Deal
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Total Deals</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">{deals.length}</div>
              <p className="text-xs text-muted-foreground font-light">
                Current page
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Active</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">
                {deals.filter((d) => d.status === "ACTIVE").length}
              </div>
              <p className="text-xs text-muted-foreground font-light">
                Active deals
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">
                {dealService.formatDealValue(
                  deals.reduce((acc, deal) => acc + (deal.value || 0), 0)
                )}
              </div>
              <p className="text-xs text-muted-foreground font-light">
                Combined value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">
                Avg. Progress
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">
                {deals.length > 0
                  ? Math.round(
                      deals.reduce((acc, deal) => acc + deal.progress, 0) /
                        deals.length
                    )
                  : 0}
                %
              </div>
              <p className="text-xs text-muted-foreground font-light">
                Overall progress
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search deals by name, buyer, seller, or jurisdiction..."
                    className="pl-10 font-light"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select
                  value={filterStatus}
                  onValueChange={(value: DealStatus | "all") =>
                    setFilterStatus(value)
                  }
                >
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    {Object.entries(DEAL_STATUS_LABELS).map(
                      ([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
                <Select
                  value={filterType}
                  onValueChange={(value: DealType | "all") =>
                    setFilterType(value)
                  }
                >
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {Object.entries(DEAL_TYPE_LABELS).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin" />
            <span className="ml-2">Loading deals...</span>
          </div>
        )}

        {/* Deals Grid */}
        {!loading && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {deals.map((deal) => (
              <Card key={deal.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <CardTitle className="text-lg font-medium">
                        {deal.title}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(deal.status)}>
                          {DEAL_STATUS_LABELS[deal.status]}
                        </Badge>
                        <Badge variant="outline" className="font-light">
                          {DEAL_TYPE_LABELS[deal.type]}
                        </Badge>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Edit Deal</DropdownMenuItem>
                        <DropdownMenuItem>View Details</DropdownMenuItem>
                        <DropdownMenuItem>Export</DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteDeal(deal.id)}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Deal Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <span className="w-4 h-4 flex items-center justify-center text-sm font-medium">
                          <HandCoins />
                        </span>
                        <span className="font-light">Value</span>
                      </div>
                      <p className="font-medium ml-1">
                        <span>{getCurrencySymbol(deal.currency)}</span>
                        {dealService.formatDealValue(deal.value)}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="w-4 h-4" />
                        <span className="font-light">Expected Close</span>
                      </div>
                      <p className="font-medium">
                        {dealService.formatDate(deal.expectedClosingDate)}
                      </p>
                    </div>
                  </div>

                  {/* Industry and Description */}
                  {(deal.industry || deal.description) && (
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      {deal.industry && (
                        <div className="space-y-1">
                          <span className="text-muted-foreground font-light">
                            Industry:
                          </span>
                          <span className="font-medium ml-2">
                            {deal.industry}
                          </span>
                        </div>
                      )}
                      {deal.description && (
                        <div className="space-y-1">
                          <span className="text-muted-foreground font-light">
                            Description:
                          </span>
                          <p className="text-sm font-light ml-2">
                            {deal.description}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Buyer */}
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-muted-foreground text-sm">
                      <Users className="w-4 h-4" />
                      <span className="font-light">Buyer</span>
                    </div>
                    <p className="text-sm font-light">
                      {deal.buyer || "Not specified"}
                    </p>
                  </div>

                  {/* Seller */}
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-muted-foreground text-sm">
                      <Building className="w-4 h-4" />
                      <span className="font-light">Seller</span>
                    </div>
                    <p className="text-sm font-light">
                      {deal.seller || "Not specified"}
                    </p>
                  </div>

                  {/* Progress */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-light">Progress</span>
                      <span className="font-medium">{deal.progress}%</span>
                    </div>
                    <Progress value={deal.progress} className="w-full" />
                  </div>

                  {/* Risk and Meta */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <AlertTriangle
                        className={`w-4 h-4 ${getRiskColor(
                          getRiskLevel(deal)
                        )}`}
                      />
                      <span className="font-light">
                        {getRiskLevel(deal)} Risk
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <FileText className="w-4 h-4" />
                        <span className="font-light">
                          {deal._count?.documents || 0} docs
                        </span>
                      </div>
                      <span className="font-light">
                        {dealService.formatDate(deal.updatedAt)}
                      </span>
                    </div>
                  </div>

                  {/* Process Stepper */}
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-muted-foreground">
                        Due Diligence Process
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Step {getCurrentStep(deal)} of 3
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Step 1: Documents */}
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                            getCurrentStep(deal) >= 1
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted text-muted-foreground"
                          }`}
                        >
                          {getCurrentStep(deal) > 1 ? (
                            <Check className="w-4 h-4" />
                          ) : (
                            <Upload className="w-4 h-4" />
                          )}
                        </div>
                        <span
                          className={`text-sm font-light ${
                            getCurrentStep(deal) >= 1
                              ? "text-foreground"
                              : "text-muted-foreground"
                          }`}
                        >
                          Documents
                        </span>
                      </div>

                      <ChevronRight className="w-4 h-4 text-muted-foreground" />

                      {/* Step 2: AI Review */}
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                            getCurrentStep(deal) >= 2
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted text-muted-foreground"
                          }`}
                        >
                          {getCurrentStep(deal) > 2 ? (
                            <Check className="w-4 h-4" />
                          ) : (
                            <Bot className="w-4 h-4" />
                          )}
                        </div>
                        <span
                          className={`text-sm font-light ${
                            getCurrentStep(deal) >= 2
                              ? "text-foreground"
                              : "text-muted-foreground"
                          }`}
                        >
                          AI Review
                        </span>
                      </div>

                      <ChevronRight className="w-4 h-4 text-muted-foreground" />

                      {/* Step 3: Report */}
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                            getCurrentStep(deal) >= 3
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted text-muted-foreground"
                          }`}
                        >
                          <FileBarChart className="w-4 h-4" />
                        </div>
                        <span
                          className={`text-sm font-light ${
                            getCurrentStep(deal) >= 3
                              ? "text-foreground"
                              : "text-muted-foreground"
                          }`}
                        >
                          Report
                        </span>
                      </div>
                    </div>

                    {/* Next Action Button */}
                    <div className="mt-4">
                      {getCurrentStep(deal) === 1 && (
                        <Link
                          to={`/document-upload?dealId=${deal.id}`}
                          className="w-full"
                        >
                          <Button size="sm" className="w-full font-light">
                            <Upload className="w-4 h-4 mr-2" />
                            Upload Documents
                          </Button>
                        </Link>
                      )}
                      {getCurrentStep(deal) === 2 && (
                        <Link
                          to={`/ai-review?dealId=${deal.id}`}
                          className="w-full"
                        >
                          <Button size="sm" className="w-full font-light">
                            <Bot className="w-4 h-4 mr-2" />
                            Start AI Review
                          </Button>
                        </Link>
                      )}
                      {getCurrentStep(deal) === 3 && (
                        <Link
                          to={`/report-generation?dealId=${deal.id}`}
                          className="w-full"
                        >
                          <Button size="sm" className="w-full font-light">
                            <FileBarChart className="w-4 h-4 mr-2" />
                            Generate Report
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && deals.length === 0 && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <FileText className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No deals found</h3>
              <p className="text-muted-foreground text-center mb-4 font-light">
                {searchQuery || filterStatus !== "all" || filterType !== "all"
                  ? "Try adjusting your search terms or filters"
                  : "Get started by registering your first deal"}
              </p>
              {!searchQuery &&
                filterStatus === "all" &&
                filterType === "all" && (
                  <Link to="/register-deal">
                    <Button className="font-light">
                      <Plus className="w-4 h-4 mr-2" />
                      Register First Deal
                    </Button>
                  </Link>
                )}
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {!loading && deals.length > 0 && totalPages > 1 && (
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Deals;
