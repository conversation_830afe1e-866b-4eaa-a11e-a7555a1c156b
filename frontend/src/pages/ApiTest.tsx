import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { dealService } from '@/services/dealService';
import { authService } from '@/services/authService';
import { DealType } from '@/types/deal';

const ApiTest = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const { toast } = useToast();

  const [testDeal, setTestDeal] = useState({
    title: 'Test Deal from Frontend',
    type: 'MA' as DealType,
    value: 1000000,
    jurisdiction: 'Delaware',
    buyer: 'Test Buyer',
    seller: 'Test Seller',
    notes: 'This is a test deal'
  });

  const [loginData, setLoginData] = useState({
    email: '<EMAIL>',
    password: 'Change@123'
  });

  const testLogin = async () => {
    try {
      setLoading(true);
      const result = await authService.login(loginData);
      setResult(result);
      toast({
        title: "Success",
        description: `Logged in as: ${result.user.firstName} ${result.user.lastName}`,
      });
    } catch (error) {
      console.error('Login test failed:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Login test failed',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testGetDeals = async () => {
    try {
      setLoading(true);
      const result = await dealService.getDeals();
      setResult(result);
      toast({
        title: "Success",
        description: `Retrieved ${result.deals.length} deals`,
      });
    } catch (error) {
      console.error('Test failed:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Test failed',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testCreateDeal = async () => {
    try {
      setLoading(true);
      const result = await dealService.createDeal(testDeal);
      setResult(result);
      toast({
        title: "Success",
        description: `Created deal: ${result.title}`,
      });
    } catch (error) {
      console.error('Test failed:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Test failed',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testGetStats = async () => {
    try {
      setLoading(true);
      const result = await dealService.getDealStats();
      setResult(result);
      toast({
        title: "Success",
        description: `Retrieved stats: ${result.total} total deals`,
      });
    } catch (error) {
      console.error('Test failed:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Test failed',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-3xl font-bold">API Test Page</h1>
          <p className="text-muted-foreground">Test the Deal Management API integration</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Test Controls */}
          <Card>
            <CardHeader>
              <CardTitle>API Tests</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={testLogin}
                disabled={loading}
                className="w-full"
                variant="default"
              >
                Test Login
              </Button>

              <Button
                onClick={testGetDeals}
                disabled={loading}
                className="w-full"
              >
                Test Get Deals
              </Button>

              <Button
                onClick={testCreateDeal}
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                Test Create Deal
              </Button>

              <Button
                onClick={testGetStats}
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                Test Get Stats
              </Button>
            </CardContent>
          </Card>

          {/* Test Data */}
          <Card>
            <CardHeader>
              <CardTitle>Test Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Login Credentials</Label>
                <div className="space-y-2">
                  <Input
                    placeholder="Email"
                    value={loginData.email}
                    onChange={(e) => setLoginData(prev => ({ ...prev, email: e.target.value }))}
                  />
                  <Input
                    placeholder="Password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData(prev => ({ ...prev, password: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Deal Data</Label>
                <div className="space-y-2">
                  <Input
                    placeholder="Title"
                    value={testDeal.title}
                    onChange={(e) => setTestDeal(prev => ({ ...prev, title: e.target.value }))}
                  />
                  <Input
                    placeholder="Value"
                    type="number"
                    value={testDeal.value}
                    onChange={(e) => setTestDeal(prev => ({ ...prev, value: Number(e.target.value) }))}
                  />
                  <Input
                    placeholder="Buyer"
                    value={testDeal.buyer}
                    onChange={(e) => setTestDeal(prev => ({ ...prev, buyer: e.target.value }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Results */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Debug Info */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>Frontend URL:</strong> {window.location.origin}</p>
              <p><strong>API Base URL:</strong> {import.meta.env.VITE_API_URL || 'http://localhost:8000'}</p>
              <p><strong>Auth Token:</strong> {localStorage.getItem('accessToken') ? 'Present' : 'Missing'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ApiTest;
