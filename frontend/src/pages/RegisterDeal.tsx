import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import {
  CalendarIcon,
  ArrowLeft,
  Upload,
  Plus,
  X,
  Trash2,
  Building,
  DollarSign,
  FileCheck,
  Scale,
  Shield,
  Briefcase,
  Users,
  Globe,
  Gavel,
  Leaf,
  ArrowRight,
  CheckCircle,
  Loader2
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Link, useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { dealService } from "@/services/dealService";
import { DealType, DEAL_TYPE_LABELS } from "@/types/deal";

// Initial requisition categories
const initialRequisitionCategories = [
  {
    id: "corporate",
    title: "Corporate and Secretarial",
    icon: Building,
    items: [
      "Organizational structure of the Company, covering all its subsidiaries and affiliates",
      "Articles and Memorandum of Association of the Company, along with Certificate of Incorporation",
      "Minutes book and minutes of meetings of Board of directors, Committees of directors, and Shareholders",
      "Copies of all registers maintained by the Company",
      "Details of share capital amount and shareholding structure",
      "Details and documents pertaining to any convertible securities issued by the Company",
      "List of agreements in force, executed between the Company and its shareholders or third parties",
      "Any obligations relating to the sale, purchase, assignment, transfer, issuance, acquisition or encumbrance of shares",
      "Details of auditors for the last three financial years"
    ]
  },
  {
    id: "financial",
    title: "Financial General Aspects",
    icon: DollarSign,
    items: [
      "Audited Financial Statements for the last three years and latest YTD financial information",
      "Particulars and list of bank accounts in any currency and jurisdiction",
      "Reports of board of directors, Company's auditors, unaudited financials"
    ]
  },
  {
    id: "contracts",
    title: "Contracts",
    icon: FileCheck,
    items: [
      "Copies of all commercial contracts for development projects",
      "Copies of utilities agreements (power, water, etc.)",
      "Details and copies of any other contracts/agreements relating to the business",
      "Copy of all agreements involving exclusivity, reciprocity or non-competition arrangements"
    ]
  },
  {
    id: "liabilities",
    title: "Liabilities",
    icon: Scale,
    items: [
      "List showing structure of financial liabilities and all financing documents",
      "Details of guarantees, bank/performance guarantees, standby letters of credit",
      "Details of contingent liabilities or off-balance-sheet obligations"
    ]
  },
  {
    id: "regulatory",
    title: "Regulatory/Legal Approvals and Permits",
    icon: Shield,
    items: [
      "Detailed list and copies of all licenses, permits, registrations, concessions, approvals",
      "Details of conditions for maintaining licenses, permits, authorizations, and consents",
      "Copies of all relevant tax registrations, capital subsidy schemes, licenses"
    ]
  },
  {
    id: "assets",
    title: "Assets",
    icon: Briefcase,
    items: [
      "Details and documents pertaining to immovable properties owned/taken on lease",
      "Details and documents pertaining to movable properties including charges and encumbrances"
    ]
  },
  {
    id: "ip",
    title: "Industrial and Intellectual Property",
    icon: Globe,
    items: [
      "Details of trademarks, patents, copyrights, designs and models or other intellectual property rights"
    ]
  },
  {
    id: "labour",
    title: "Labour and Social Security Matter",
    icon: Users,
    items: [
      "Detailed list of employees (permanent and contractual)",
      "Copies of standard employment contracts and service agreements",
      "List of insurances taken by Company for Employees"
    ]
  },
  {
    id: "litigation",
    title: "Litigation",
    icon: Gavel,
    items: [
      "List of Disputes before any court, tribunal, quasi judicial or administrative body",
      "List of all resolved Disputes involving the Company or affiliates",
      "Details of any current or threatened tax Disputes with relevant tax authority"
    ]
  },
  {
    id: "environment",
    title: "Environment",
    icon: Leaf,
    items: [
      "List and copies of all licenses, authorizations, permits from environmental agencies",
      "Details of waste/effluents/hazardous substances management and disposal systems"
    ]
  }
];

const RegisterDeal = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    title: "",
    type: "" as DealType | "",
    value: "",
    jurisdiction: "",
    buyer: "",
    seller: "",
    startDate: "",
    expectedSigningDate: "",
    expectedClosingDate: "",
    notes: "",
    industry: "",
    description: "",
    riskLevel: "MEDIUM" as "LOW" | "MEDIUM" | "HIGH",
    confidentialityLevel: "INTERNAL" as "PUBLIC" | "INTERNAL" | "CONFIDENTIAL" | "RESTRICTED",
  });

  // Suppliers state
  const [suppliers, setSuppliers] = useState<Array<{
    name: string;
    jurisdiction: string;
    contactInfo: string;
    description: string;
  }>>([{ name: "", jurisdiction: "", contactInfo: "", description: "" }]);

  const [signingDate, setSigningDate] = useState<Date>();
  const [closingDate, setClosingDate] = useState<Date>();
  const [documents, setDocuments] = useState<File[]>([]);
  const [requisitionCategories, setRequisitionCategories] = useState(initialRequisitionCategories);
  const [newItemInputs, setNewItemInputs] = useState<{ [categoryId: string]: string }>({});
  const [showNewItemInputs, setShowNewItemInputs] = useState<{ [categoryId: string]: boolean }>({});

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setDocuments([...documents, ...Array.from(e.target.files)]);
    }
  };

  const removeDocument = (index: number) => {
    setDocuments(documents.filter((_, i) => i !== index));
  };

  const addNewItem = (categoryId: string) => {
    const newItemText = newItemInputs[categoryId]?.trim();
    if (!newItemText) return;

    setRequisitionCategories(prev =>
      prev.map(category =>
        category.id === categoryId
          ? { ...category, items: [...category.items, newItemText] }
          : category
      )
    );

    setNewItemInputs(prev => ({ ...prev, [categoryId]: '' }));
    setShowNewItemInputs(prev => ({ ...prev, [categoryId]: false }));

    toast({
      title: "Item Added",
      description: "New requisition item has been added successfully.",
    });
  };

  const deleteItem = (categoryId: string, itemIndex: number) => {
    setRequisitionCategories(prev =>
      prev.map(category =>
        category.id === categoryId
          ? { ...category, items: category.items.filter((_, index) => index !== itemIndex) }
          : category
      )
    );

    toast({
      title: "Item Deleted",
      description: "Requisition item has been deleted successfully.",
    });
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle supplier changes
  const handleSupplierChange = (index: number, field: string, value: string) => {
    setSuppliers(prev => prev.map((supplier, i) =>
      i === index ? { ...supplier, [field]: value } : supplier
    ));
  };

  // Add new supplier
  const addSupplier = () => {
    setSuppliers(prev => [...prev, { name: "", jurisdiction: "", contactInfo: "", description: "" }]);
  };

  // Remove supplier
  const removeSupplier = (index: number) => {
    if (suppliers.length > 1) {
      setSuppliers(prev => prev.filter((_, i) => i !== index));
    }
  };

  // Validate step 1
  const validateStep1 = () => {
    const errors = [];
    if (!formData.title.trim()) errors.push("Deal title is required");
    if (!formData.type) errors.push("Deal type is required");
    if (formData.value && isNaN(Number(formData.value))) errors.push("Deal value must be a number");
    return errors;
  };

  const handleStep1Submit = (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateStep1();
    if (errors.length > 0) {
      toast({
        title: "Validation Error",
        description: errors.join(", "),
        variant: "destructive",
      });
      return;
    }
    setCurrentStep(2);
  };

  const handleFinalSubmit = async () => {
    try {
      setLoading(true);

      // Prepare deal data
      const dealData = {
        title: formData.title,
        type: formData.type as DealType,
        value: formData.value ? Number(formData.value) : undefined,
        jurisdiction: formData.jurisdiction || undefined,
        buyer: formData.buyer || undefined,
        seller: formData.seller || undefined,
        suppliers: suppliers.filter(s => s.name.trim()).map(supplier => ({
          name: supplier.name.trim(),
          jurisdiction: supplier.jurisdiction.trim(),
          contactInfo: supplier.contactInfo ? { email: supplier.contactInfo } : undefined,
          description: supplier.description || undefined,
        })),
        startDate: formData.startDate || undefined,
        expectedSigningDate: signingDate ? signingDate.toISOString() : undefined,
        expectedClosingDate: closingDate ? closingDate.toISOString() : undefined,
        dates: [
          ...(signingDate ? [{
            dateType: "SIGNING" as const,
            dateValue: signingDate.toISOString(),
            description: "Expected signing date"
          }] : []),
          ...(closingDate ? [{
            dateType: "CLOSING" as const,
            dateValue: closingDate.toISOString(),
            description: "Expected closing date"
          }] : [])
        ],
        notes: formData.notes || undefined,
        industry: formData.industry || undefined,
        description: formData.description || undefined,
        riskLevel: formData.riskLevel,
        confidentialityLevel: formData.confidentialityLevel,
        // Include custom requisition data
        customRequisitionData: {
          categories: requisitionCategories.map(category => ({
            id: category.id,
            title: category.title,
            items: category.items
          }))
        }
      };

      // Create the deal
      const createdDeal = await dealService.createDeal(dealData);

      toast({
        title: "Deal Registered Successfully!",
        description: "Your deal has been registered and is now ready for due diligence.",
      });

      // Navigate to the created deal or deals page
      navigate(`/deals`);
    } catch (error) {
      console.error("Failed to create deal:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to register deal. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <form onSubmit={handleStep1Submit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>Enter the fundamental details of the deal including main parties</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dealTitle">Deal Title *</Label>
              <Input
                id="dealTitle"
                placeholder="e.g., TechCorp Acquisition"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dealType">Deal Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange("type", value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select deal type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(DEAL_TYPE_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dealValue">Deal Value</Label>
              <Input
                id="dealValue"
                placeholder="e.g., 50000000"
                type="number"
                value={formData.value}
                onChange={(e) => handleInputChange("value", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="jurisdiction">Jurisdiction</Label>
              <Input
                id="jurisdiction"
                placeholder="e.g., Delaware, California"
                value={formData.jurisdiction}
                onChange={(e) => handleInputChange("jurisdiction", e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="buyer">Buyer/Acquirer</Label>
              <Input
                id="buyer"
                placeholder="e.g., Global Holdings Inc"
                value={formData.buyer}
                onChange={(e) => handleInputChange("buyer", e.target.value)}
              />
              <p className="text-xs text-muted-foreground">The acquiring entity or buyer</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="seller">Seller/Target</Label>
              <Input
                id="seller"
                placeholder="e.g., TechCorp Ltd"
                value={formData.seller}
                onChange={(e) => handleInputChange("seller", e.target.value)}
              />
              <p className="text-xs text-muted-foreground">The target company or seller</p>
            </div>
          </div>

          {/* Suppliers Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Suppliers</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addSupplier}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Supplier
              </Button>
            </div>

            {suppliers.map((supplier, index) => (
              <Card key={index} className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Supplier {index + 1}</h4>
                    {suppliers.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSupplier(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`supplier-name-${index}`}>Supplier Name *</Label>
                      <Input
                        id={`supplier-name-${index}`}
                        placeholder="e.g., ABC Corp"
                        value={supplier.name}
                        onChange={(e) => handleSupplierChange(index, "name", e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`supplier-jurisdiction-${index}`}>Jurisdiction *</Label>
                      <Input
                        id={`supplier-jurisdiction-${index}`}
                        placeholder="e.g., Delaware, UK"
                        value={supplier.jurisdiction}
                        onChange={(e) => handleSupplierChange(index, "jurisdiction", e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`supplier-contact-${index}`}>Contact Email</Label>
                      <Input
                        id={`supplier-contact-${index}`}
                        type="email"
                        placeholder="e.g., <EMAIL>"
                        value={supplier.contactInfo}
                        onChange={(e) => handleSupplierChange(index, "contactInfo", e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground">Primary contact email for this supplier</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`supplier-description-${index}`}>Description</Label>
                      <Input
                        id={`supplier-description-${index}`}
                        placeholder="Brief description"
                        value={supplier.description}
                        onChange={(e) => handleSupplierChange(index, "description", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Additional Deal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="industry">Industry</Label>
              <Input
                id="industry"
                placeholder="e.g., Technology, Healthcare"
                value={formData.industry}
                onChange={(e) => handleInputChange("industry", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="riskLevel">Risk Level</Label>
              <Select
                value={formData.riskLevel}
                onValueChange={(value) => handleInputChange("riskLevel", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select risk level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">Low Risk</SelectItem>
                  <SelectItem value="MEDIUM">Medium Risk</SelectItem>
                  <SelectItem value="HIGH">High Risk</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="confidentialityLevel">Confidentiality Level</Label>
              <Select
                value={formData.confidentialityLevel}
                onValueChange={(value) => handleInputChange("confidentialityLevel", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select confidentiality level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PUBLIC">Public</SelectItem>
                  <SelectItem value="INTERNAL">Internal</SelectItem>
                  <SelectItem value="CONFIDENTIAL">Confidential</SelectItem>
                  <SelectItem value="RESTRICTED">Restricted</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                placeholder="Brief description of the deal"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Additional notes about the deal..."
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Parties */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Parties</CardTitle>
          <CardDescription>Other parties involved in the deal (advisors, banks, legal counsel, etc.)</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="additionalParties">Additional Parties</Label>
            <Textarea
              id="additionalParties"
              placeholder="List any additional parties involved (advisors, investment banks, legal counsel, auditors, etc.)"
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              Optional: Describe other parties and their roles in the transaction
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Key Dates */}
      < Card >
        <CardHeader>
          <CardTitle>Key Dates</CardTitle>
          <CardDescription>Important timeline milestones</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Expected Signing Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !signingDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {signingDate ? format(signingDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={signingDate}
                    onSelect={setSigningDate}
                    initialFocus
                    className="p-3 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>Expected Closing Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !closingDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {closingDate ? format(closingDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={closingDate}
                    onSelect={setClosingDate}
                    initialFocus
                    className="p-3 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card >



      {/* Preliminary Documents */}
      {/* < Card >
        <CardHeader>
          <CardTitle>Preliminary Documents</CardTitle>
          <CardDescription>Upload initial documents (LOI, Term Sheet, etc.)</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <div className="space-y-2">
              <Label htmlFor="file-upload" className="cursor-pointer text-primary hover:text-primary/80">
                Click to upload or drag and drop
              </Label>
              <p className="text-sm text-muted-foreground">PDF, DOCX, XLSX (Max 10MB each)</p>
              <Input
                id="file-upload"
                type="file"
                multiple
                accept=".pdf,.docx,.xlsx"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>
          </div>

          {documents.length > 0 && (
            <div className="space-y-2">
              <Label>Uploaded Documents</Label>
              <div className="space-y-2">
                {documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                    <span className="text-sm">{doc.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDocument(index)}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card > */}

      {/* Notes */}
      < Card >
        <CardHeader>
          <CardTitle>Additional Notes</CardTitle>
          <CardDescription>Any additional information or special considerations</CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Enter any additional notes, special requirements, or context for this deal..."
            rows={4}
          />
        </CardContent>
      </Card >

      {/* Actions */}
      < div className="flex justify-end gap-4" >
        <Link to="/dashboard">
          <Button variant="outline">Cancel</Button>
        </Link>
        <Button type="submit" className="flex items-center gap-2">
          Next: Customize Requisition List
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div >
    </form >
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      {/* Step 2 Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            Step 2: Customize Requisition List
          </CardTitle>
          <CardDescription>
            Review and customize the document requisition list that will be sent to parties involved in the deal.
            You can add new items, remove existing ones, or modify the requirements as needed.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Requisition Categories */}
      <Accordion type="multiple" className="space-y-4">
        {requisitionCategories.map((category, categoryIndex) => {
          const Icon = category.icon;

          return (
            <AccordionItem key={category.id} value={category.id}>
              <Card>
                <AccordionTrigger className="hover:no-underline p-0">
                  <CardHeader className="flex-row items-center space-y-0 space-x-4 w-full">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1 text-left">
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      <CardDescription>
                        {category.items.length} items
                      </CardDescription>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary">
                        {category.items.length} items
                      </Badge>
                    </div>
                  </CardHeader>
                </AccordionTrigger>
                <AccordionContent>
                  <CardContent className="pt-0">
                    <div className="space-y-4">
                      {category.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="border rounded-lg p-4 space-y-2">
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <span className="text-sm font-medium text-muted-foreground">
                                  {categoryIndex + 1}.{itemIndex + 1}
                                </span>
                              </div>
                              <p className="text-sm">{item}</p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteItem(category.id, itemIndex)}
                              className="text-destructive hover:text-destructive hover:bg-destructive/10"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}

                      {/* Add new item */}
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                        {showNewItemInputs[category.id] ? (
                          <div className="space-y-3">
                            <Textarea
                              placeholder="Enter new requisition item..."
                              value={newItemInputs[category.id] || ''}
                              onChange={(e) => setNewItemInputs(prev => ({ ...prev, [category.id]: e.target.value }))}
                              rows={3}
                            />
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                onClick={() => addNewItem(category.id)}
                                disabled={!newItemInputs[category.id]?.trim()}
                              >
                                Add Item
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setShowNewItemInputs(prev => ({ ...prev, [category.id]: false }))}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowNewItemInputs(prev => ({ ...prev, [category.id]: true }))}
                            className="w-full flex items-center gap-2 text-muted-foreground hover:text-foreground"
                          >
                            <Plus className="w-4 h-4" />
                            Add New Requisition Item
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </AccordionContent>
              </Card>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Actions */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep(1)}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Deal Details
        </Button>
        <Button
          onClick={handleFinalSubmit}
          disabled={loading}
          className="flex items-center gap-2"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <CheckCircle className="w-4 h-4" />
          )}
          {loading ? "Registering Deal..." : "Register Deal"}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link to="/dashboard">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Register New Deal - Step {currentStep} of 2
            </h1>
            <p className="text-muted-foreground">
              {currentStep === 1 ? "Create a new due diligence process" : "Customize document requirements"}
            </p>
          </div>
        </div>

        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
      </div>
    </div>
  );
};

export default RegisterDeal;