import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Bot,
  FileText,
  AlertTriangle,
  CheckCircle,
  Eye,
  DollarSign,
  Shield,
  Scale,
  Cog,
  Building,
  Clock,
  Play,
  Loader2,
  RefreshCw,
  Target
} from "lucide-react";
import { Link, useSearchParams } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { aiReviewService, AIReviewResult } from "../services/aiReviewService";

interface PersonaAnalysis {
  id: string;
  name: string;
  icon: any;
  color: string;
  status: 'completed' | 'reviewing' | 'pending';
  assignedTo: string;
  findings: {
    summary: string;
    risks: string[];
    recommendations: string[];
    confidence: number;
  };
  reviewedAt?: string;
  comments: {
    id: string;
    user: string;
    text: string;
    timestamp: string;
  }[];
}

interface ExtractedData {
  category: string;
  field: string;
  value: string;
  confidence: number;
  source: string;
  status: 'verified' | 'flagged' | 'pending';
  notes?: string;
}

const AIReview = () => {
  const [searchParams] = useSearchParams();
  const dealId = searchParams.get('dealId') || '1';
  const { toast } = useToast();

  // New state for real AI review system
  const [isLoading, setIsLoading] = useState(false);
  const [isStarting, setIsStarting] = useState(false);
  const [reviewData, setReviewData] = useState<{
    results: AIReviewResult[];
    summary: {
      dealId: string;
      isRunning: boolean;
      overallProgress: number;
      totalDocuments: number;
      totalProcessedDocuments: number;
      totalPersonas: number;
      completedPersonas: number;
      failedPersonas: number;
      estimatedCompletionTime: Date | null;
    };
  } | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [authError, setAuthError] = useState<string | null>(null);

  // Legacy state for compatibility (will be removed once real data is fully integrated)
  const [personas, setPersonas] = useState<PersonaAnalysis[]>([]);

  const [extractedData, setExtractedData] = useState<ExtractedData[]>([]);

  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [newComment, setNewComment] = useState('');
  const [activePersona, setActivePersona] = useState<string | null>(null);

  // Load initial data and set up polling
  useEffect(() => {
    loadReviewData();

    // Set up polling for real-time updates
    const interval = setInterval(() => {
      if (reviewData?.summary?.isRunning) {
        loadReviewData();
      }
    }, 5000); // Poll every 5 seconds when review is running

    return () => clearInterval(interval);
  }, [dealId, reviewData?.summary?.isRunning]);

  // Load review data from API
  const loadReviewData = async () => {
    try {
      setIsLoading(true);

      // Load results with progress and status
      const data = await aiReviewService.getResults(dealId);
      setReviewData(data);
      setLastUpdated(new Date());

      // Update extracted data from AI review results
      if (data.results && data.results.length > 0) {
        const allExtractedData = aiReviewService.getAllExtractedData(data.results);
        const formattedData = allExtractedData.map(data => ({
          category: data.category,
          field: data.field,
          value: data.value,
          confidence: data.confidence,
          source: data.source_document,
          status: data.verification_status === 'VERIFIED' ? 'verified' as const :
            data.verification_status === 'FLAGGED' ? 'flagged' as const : 'pending' as const,
          notes: data.verification_status === 'FLAGGED' ? 'Requires verification' : undefined
        }));
        setExtractedData(formattedData);
      }

    } catch (error: any) {
      console.error('Failed to load review data:', error);
      if (error.message?.includes('Authentication required')) {
        setAuthError(error.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Start AI review
  const handleStartReview = async () => {
    try {
      setIsStarting(true);

      const result = await aiReviewService.startReview(dealId);

      toast({
        title: "AI Review Started",
        description: result.message,
      });

      // Immediately refresh data
      await loadReviewData();

    } catch (error: any) {
      toast({
        title: "Failed to Start Review",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsStarting(false);
    }
  };

  // Reset AI review
  const handleResetReview = async () => {
    try {
      setIsLoading(true);

      const result = await aiReviewService.resetReview(dealId);

      toast({
        title: "AI Review Reset",
        description: result.message,
      });

      // Immediately refresh data
      await loadReviewData();

    } catch (error: any) {
      toast({
        title: "Failed to Reset Review",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get persona icon component
  const getPersonaIcon = (type: string) => {
    const iconMap: { [key: string]: any } = {
      'LEGAL': Scale,
      'FINANCIAL': DollarSign,
      'TECHNICAL': Cog,
      'OPERATIONAL': Building,
      'COMPLIANCE': Shield,
      'STRATEGIC': Target,
      'RISK': AlertTriangle
    };
    return iconMap[type] || Bot;
  };

  // Get persona color
  const getPersonaColor = (type: string): string => {
    return aiReviewService.getPersonaColor(type);
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'PENDING':
        return 'bg-gray-100 text-gray-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status icon for AI review status
  const getAIStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'IN_PROGRESS':
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'PENDING':
        return <Clock className="w-4 h-4 text-gray-600" />;
      case 'FAILED':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Eye className="w-4 h-4 text-gray-600" />;
    }
  };

  // Format time remaining
  const formatTimeRemaining = (seconds: number): string => {
    if (seconds <= 0) return 'Completed';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else if (minutes > 0) {
      return `${minutes}m remaining`;
    } else {
      return `${seconds}s remaining`;
    }
  };

  const handleEdit = (field: string, currentValue: string) => {
    setEditingField(field);
    setEditValue(currentValue);
  };

  const handleSave = (field: string) => {
    setExtractedData(prev => prev.map(item =>
      item.field === field
        ? { ...item, value: editValue, status: 'verified' as const }
        : item
    ));
    setEditingField(null);
    setEditValue('');
    toast({
      title: "Field Updated",
      description: "The extracted data has been updated and verified.",
    });
  };

  const handleStatusChange = (field: string, status: ExtractedData['status']) => {
    setExtractedData(prev => prev.map(item =>
      item.field === field ? { ...item, status } : item
    ));
  };

  const handleApprovePersona = (personaId: string) => {
    setPersonas(prev => prev.map(persona =>
      persona.id === personaId
        ? { ...persona, status: 'completed' as const, reviewedAt: new Date().toISOString() }
        : persona
    ));
    toast({
      title: "Review Approved",
      description: "Persona analysis has been approved and marked as complete.",
    });
  };

  const handleAddComment = (personaId: string) => {
    if (!newComment.trim()) return;

    const comment = {
      id: Date.now().toString(),
      user: 'Current User',
      text: newComment,
      timestamp: new Date().toISOString()
    };

    setPersonas(prev => prev.map(persona =>
      persona.id === personaId
        ? { ...persona, comments: [...persona.comments, comment] }
        : persona
    ));
    setNewComment('');
    toast({
      title: "Comment Added",
      description: "Your comment has been added to the review.",
    });
  };

  const getStatusColor = (status: ExtractedData['status']) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'flagged':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600';
    if (confidence >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const categorizeData = () => {
    return extractedData.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    }, {} as Record<string, ExtractedData[]>);
  };

  const getRiskAnalysis = () => {
    const flaggedItems = extractedData.filter(item => item.status === 'flagged');
    const lowConfidenceItems = extractedData.filter(item => item.confidence < 80);

    return {
      totalRisks: flaggedItems.length,
      lowConfidenceItems: lowConfidenceItems.length,
      overallRiskLevel: flaggedItems.length > 2 ? 'High' : flaggedItems.length > 0 ? 'Medium' : 'Low'
    };
  };

  const riskAnalysis = getRiskAnalysis();
  const categorizedData = categorizeData();

  const getPersonaProgress = () => {
    const completed = personas.filter(p => p.status === 'completed').length;
    return (completed / personas.length) * 100;
  };

  const getPersonaStatusIcon = (status: PersonaAnalysis['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'reviewing':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'pending':
        return <Eye className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPersonaStatusColor = (status: PersonaAnalysis['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'reviewing':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link to="/dashboard">
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-light text-foreground flex items-center gap-3">
                <Bot className="w-8 h-8 text-primary" />
                Multi-Perspective AI Review
              </h1>
              <p className="text-muted-foreground font-light">
                Deal ID: {dealId} - TechCorp Acquisition
                {lastUpdated && (
                  <span className="ml-4 text-xs">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                )}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* AI Review Controls */}
            <div className="flex items-center gap-2">
              {!reviewData?.summary?.isRunning && (
                <Button
                  onClick={handleStartReview}
                  disabled={isStarting}
                  className="flex items-center gap-2"
                >
                  {isStarting ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  {isStarting ? 'Starting...' : 'Start AI Review'}
                </Button>
              )}

              {/* Reset button for stuck reviews */}
              {reviewData?.results && reviewData.results.some(r => r.status === 'IN_PROGRESS') && (
                <Button
                  variant="outline"
                  onClick={handleResetReview}
                  disabled={isLoading}
                  className="flex items-center gap-2 text-orange-600 border-orange-200 hover:bg-orange-50"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                  Reset Review
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={loadReviewData}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                Refresh
              </Button>
            </div>

            {/* Progress Display */}
            <div className="text-right">
              <div className="text-sm font-light text-muted-foreground">
                {reviewData?.summary?.isRunning ? 'AI Review in Progress' : 'Review Progress'}
              </div>
              <div className="flex items-center gap-2">
                <Progress
                  value={reviewData?.summary?.overallProgress || getPersonaProgress()}
                  className="w-32"
                />
                <span className="text-sm font-medium">
                  {Math.round(reviewData?.summary?.overallProgress || getPersonaProgress())}%
                </span>
              </div>
              {reviewData?.summary?.isRunning && reviewData.summary && (
                <div className="text-xs text-muted-foreground mt-1">
                  {reviewData.summary.totalProcessedDocuments} of {reviewData.summary.totalDocuments * reviewData.summary.totalPersonas} analyses completed
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Persona Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-light">AI Persona Analysis</CardTitle>
            <CardDescription className="font-light">
              {reviewData?.summary?.isRunning
                ? "AI analysts are currently processing documents in real-time"
                : "Multi-perspective review by specialized AI analysts"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Real-time AI Review Status */}
            {reviewData?.results && reviewData.results.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {reviewData.results.map((result) => {
                  const personaType = result.persona.type;
                  const Icon = getPersonaIcon(personaType);
                  const color = getPersonaColor(personaType);

                  return (
                    <Card key={personaType} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Icon className={`w-5 h-5 ${color}`} />
                            <span className="font-medium text-sm">
                              {aiReviewService.formatStatus(personaType)}
                            </span>
                          </div>
                          {getAIStatusIcon(result.status)}
                        </div>
                        <div className="space-y-2">
                          <Badge className={getStatusBadgeColor(result.status)} variant="secondary">
                            {aiReviewService.formatStatus(result.status)}
                          </Badge>

                          {/* Progress Bar */}
                          <div className="space-y-1">
                            <Progress value={result.progress} className="h-2" />
                            <div className="text-xs text-muted-foreground">
                              {result.documentsAnalyzed} of {result.totalDocuments} docs
                            </div>
                          </div>

                          {/* Current Document */}
                          {result.currentDocument && result.status === 'IN_PROGRESS' && (
                            <div className="text-xs text-blue-600 font-medium">
                              Processing document...
                            </div>
                          )}

                          {/* Time Remaining */}
                          {result.estimatedTimeRemaining > 0 && result.status === 'IN_PROGRESS' && (
                            <div className="text-xs text-muted-foreground">
                              {formatTimeRemaining(result.estimatedTimeRemaining)}
                            </div>
                          )}

                          {/* Completion Time */}
                          {result.status === 'COMPLETED' && result.completedAt && (
                            <div className="text-xs text-green-600 font-medium">
                              Completed {new Date(result.completedAt).toLocaleTimeString()}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              /* Fallback to mock data when no real data available */
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {personas.map((persona) => {
                  const Icon = persona.icon;
                  return (
                    <Card key={persona.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Icon className={`w-5 h-5 ${persona.color}`} />
                            <span className="font-medium text-sm">{persona.name}</span>
                          </div>
                          {getPersonaStatusIcon(persona.status)}
                        </div>
                        <div className="space-y-2">
                          <Badge className={getPersonaStatusColor(persona.status)} variant="secondary">
                            {persona.status}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            <div>Assigned: {persona.assignedTo}</div>
                            {persona.reviewedAt && (
                              <div>Reviewed: {new Date(persona.reviewedAt).toLocaleDateString()}</div>
                            )}
                          </div>
                          {persona.findings.confidence > 0 && (
                            <div className="text-xs">
                              <span className={`font-medium ${persona.findings.confidence >= 80 ? 'text-green-600' : 'text-yellow-600'}`}>
                                {persona.findings.confidence}% confidence
                              </span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Authentication Warning */}
        {authError && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                <div>
                  <h3 className="font-medium text-yellow-800">Authentication Required</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    {authError} Some features are available without authentication.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content - Tabbed AI Persona Analysis */}
        <div className="space-y-6">
          {/* Real AI Review Results with Tabs */}
          {reviewData?.results && reviewData.results.length > 0 ? (
            <Tabs defaultValue={reviewData.results[0]?.persona.type} className="w-full">
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-5 xl:grid-cols-8 gap-1 h-auto p-1">
                {reviewData.results.map((result) => {
                  const Icon = getPersonaIcon(result.persona.type);
                  const color = getPersonaColor(result.persona.type);

                  return (
                    <TabsTrigger
                      key={result.persona.type}
                      value={result.persona.type}
                      className="flex flex-col items-center gap-1 p-2 h-auto text-xs"
                    >
                      <Icon className={`w-4 h-4 ${color}`} />
                      <span className="truncate max-w-[80px]">
                        {result.persona.name.split(' ')[0]}
                      </span>
                      {getAIStatusIcon(result.status)}
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {reviewData.results.map((result) => {
                const Icon = getPersonaIcon(result.persona.type);
                const color = getPersonaColor(result.persona.type);

                // Check if this result has meaningful content
                const hasContent = result.analysis && (
                  (result.analysis.summary && result.analysis.summary !== "Analysis in progress...") ||
                  (result.analysis.risks && result.analysis.risks.length > 0) ||
                  (result.analysis.recommendations && result.analysis.recommendations.length > 0) ||
                  (result.analysis.extracted_data && result.analysis.extracted_data.length > 0)
                );

                return (
                  <TabsContent key={result.persona.type} value={result.persona.type}>
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Icon className={`w-6 h-6 ${color}`} />
                            <div>
                              <CardTitle className="text-lg font-light">{result.persona.name}</CardTitle>
                              <CardDescription className="font-light">
                                {result.status === 'COMPLETED' ? (
                                  hasContent ?
                                    `Completed ${result.completedAt ? new Date(result.completedAt).toLocaleString() : 'recently'}` :
                                    'Completed - No compatible documents found'
                                ) : result.status === 'IN_PROGRESS' ? (
                                  'Analysis in progress...'
                                ) : result.status === 'REQUIRES_ATTENTION' ? (
                                  'Requires attention - Check document formats'
                                ) : (
                                  'Pending analysis'
                                )}
                              </CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusBadgeColor(result.status)} variant="secondary">
                              {aiReviewService.formatStatus(result.status)}
                            </Badge>
                            {getAIStatusIcon(result.status)}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {/* Progress Bar */}
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">Progress</h4>
                              <span className="text-sm font-medium">{result.progress}%</span>
                            </div>
                            <Progress value={result.progress} className="h-2" />
                          </div>

                          {/* Show message if no content available */}
                          {!hasContent && result.status === 'COMPLETED' && (
                            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                              <div className="flex items-start gap-3">
                                <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
                                <div>
                                  <h4 className="font-medium text-amber-800 mb-1">No Analysis Available</h4>
                                  <p className="text-sm text-amber-700">
                                    This persona couldn't analyze any documents. The uploaded documents may not be in a format supported by AI analysis.
                                  </p>
                                  <p className="text-xs text-amber-600 mt-2">
                                    <strong>Supported formats:</strong> PDF, TXT, JPG, PNG
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Show message if requires attention */}
                          {result.status === 'REQUIRES_ATTENTION' && (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                              <div className="flex items-start gap-3">
                                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                                <div>
                                  <h4 className="font-medium text-red-800 mb-1">Analysis Failed</h4>
                                  <p className="text-sm text-red-700">
                                    This persona encountered issues during analysis. Please check that your documents are in supported formats and try again.
                                  </p>
                                  <p className="text-xs text-red-600 mt-2">
                                    <strong>Supported formats:</strong> PDF, TXT, JPG, PNG
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Analysis Results (only show if completed) */}
                          {result.status === 'COMPLETED' && result.analysis && (
                            <>
                              <div className="space-y-6">
                                {/* Executive Summary */}
                                <div className="bg-slate-50 p-4 rounded-lg">
                                  <h4 className="font-semibold mb-3 text-slate-800">Executive Summary</h4>
                                  <p className="text-sm text-slate-700 leading-relaxed">
                                    {result.analysis.summary}
                                  </p>
                                </div>

                                {/* Confidence Score */}
                                {result.analysis.confidence_score && (
                                  <div className="bg-white border rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-3">
                                      <h4 className="font-semibold text-slate-800">Analysis Confidence</h4>
                                      <span className={`text-lg font-bold ${result.analysis.confidence_score >= 80 ? 'text-green-600' :
                                        result.analysis.confidence_score >= 60 ? 'text-yellow-600' : 'text-red-600'
                                        }`}>
                                        {result.analysis.confidence_score}%
                                      </span>
                                    </div>
                                    <Progress value={result.analysis.confidence_score} className="h-3" />
                                    <p className="text-xs text-slate-500 mt-2">
                                      Based on document quality and data completeness
                                    </p>
                                  </div>
                                )}

                                {/* Risk Assessment */}
                                {result.analysis.risks && result.analysis.risks.length > 0 && (
                                  <div className="bg-white border rounded-lg p-4">
                                    <h4 className="font-semibold mb-4 text-slate-800 flex items-center gap-2">
                                      <Shield className="w-5 h-5 text-red-500" />
                                      Risk Assessment
                                    </h4>
                                    <div className="space-y-3">
                                      {result.analysis.risks.map((risk, index) => (
                                        <div key={index} className={`border-l-4 pl-4 py-3 ${risk.severity === 'CRITICAL' ? 'border-red-500 bg-red-50' :
                                          risk.severity === 'HIGH' ? 'border-red-400 bg-red-25' :
                                            risk.severity === 'MEDIUM' ? 'border-yellow-400 bg-yellow-25' :
                                              'border-blue-400 bg-blue-25'
                                          }`}>
                                          <div className="flex items-start justify-between mb-2">
                                            <h5 className="font-medium text-slate-800">{risk.title}</h5>
                                            <Badge
                                              variant={risk.severity === 'CRITICAL' ? 'destructive' : 'outline'}
                                              className="text-xs"
                                            >
                                              {risk.severity}
                                            </Badge>
                                          </div>
                                          <p className="text-sm text-slate-600 mb-2 leading-relaxed">{risk.description}</p>
                                          {risk.category && (
                                            <p className="text-xs text-slate-500 mb-2">
                                              <strong>Category:</strong> {risk.category}
                                            </p>
                                          )}
                                          {risk.recommendation && (
                                            <div className="bg-blue-50 p-3 rounded border-l-2 border-blue-200">
                                              <p className="text-sm text-blue-800">
                                                <strong>Mitigation:</strong> {risk.recommendation}
                                              </p>
                                            </div>
                                          )}
                                          {risk.source_documents && risk.source_documents.length > 0 && (
                                            <p className="text-xs text-slate-400 mt-2">
                                              <strong>Source:</strong> {risk.source_documents.join(', ')}
                                            </p>
                                          )}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {/* Strategic Recommendations */}
                                {result.analysis.recommendations && result.analysis.recommendations.length > 0 && (
                                  <div className="bg-white border rounded-lg p-4">
                                    <h4 className="font-semibold mb-4 text-slate-800 flex items-center gap-2">
                                      <Target className="w-5 h-5 text-green-500" />
                                      Strategic Recommendations
                                    </h4>
                                    <div className="space-y-3">
                                      {result.analysis.recommendations.map((rec, index) => (
                                        <div key={index} className={`border-l-4 pl-4 py-3 ${rec.priority === 'HIGH' ? 'border-green-500 bg-green-50' :
                                          rec.priority === 'MEDIUM' ? 'border-yellow-400 bg-yellow-25' :
                                            'border-blue-400 bg-blue-25'
                                          }`}>
                                          <div className="flex items-start justify-between mb-2">
                                            <h5 className="font-medium text-slate-800">{rec.title}</h5>
                                            <div className="flex items-center gap-2">
                                              <Badge
                                                variant={rec.priority === 'HIGH' ? 'default' : 'outline'}
                                                className="text-xs"
                                              >
                                                {rec.priority} Priority
                                              </Badge>
                                              {rec.action_required && (
                                                <Badge variant="destructive" className="text-xs">
                                                  Action Required
                                                </Badge>
                                              )}
                                            </div>
                                          </div>
                                          <p className="text-sm text-slate-600 leading-relaxed">{rec.description}</p>
                                          {rec.source_documents && rec.source_documents.length > 0 && (
                                            <p className="text-xs text-slate-400 mt-2">
                                              <strong>Source:</strong> {rec.source_documents.join(', ')}
                                            </p>
                                          )}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {/* Key Financial & Legal Data */}
                                {result.analysis.extracted_data && result.analysis.extracted_data.length > 0 && (
                                  <div className="bg-white border rounded-lg p-4">
                                    <h4 className="font-semibold mb-4 text-slate-800 flex items-center gap-2">
                                      <DollarSign className="w-5 h-5 text-blue-500" />
                                      Key Data Extracted
                                    </h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                      {result.analysis.extracted_data.map((data, index) => (
                                        <div key={index} className={`p-3 rounded-lg border ${data.verification_status === 'VERIFIED' ? 'bg-green-25 border-green-200' :
                                          data.verification_status === 'FLAGGED' ? 'bg-red-25 border-red-200' :
                                            'bg-gray-25 border-gray-200'
                                          }`}>
                                          <div className="flex items-start justify-between mb-2">
                                            <h5 className="font-medium text-sm text-slate-800">{data.field}</h5>
                                            <div className="flex items-center gap-1">
                                              <Badge
                                                variant={data.confidence >= 80 ? 'default' : 'outline'}
                                                className="text-xs"
                                              >
                                                {data.confidence}%
                                              </Badge>
                                              {data.verification_status === 'VERIFIED' && (
                                                <CheckCircle className="w-3 h-3 text-green-600" />
                                              )}
                                              {data.verification_status === 'FLAGGED' && (
                                                <AlertTriangle className="w-3 h-3 text-red-600" />
                                              )}
                                            </div>
                                          </div>
                                          <p className="text-sm text-slate-700 font-medium mb-1">{data.value}</p>
                                          <p className="text-xs text-slate-500">
                                            <strong>Source:</strong> {data.source_document}
                                          </p>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {/* Document Insights */}
                                {result.analysis.document_insights && Object.keys(result.analysis.document_insights).length > 0 && (
                                  <div className="bg-white border rounded-lg p-4">
                                    <h4 className="font-semibold mb-4 text-slate-800 flex items-center gap-2">
                                      <FileText className="w-5 h-5 text-purple-500" />
                                      Document Analysis Summary
                                    </h4>
                                    <div className="space-y-3">
                                      {Object.entries(result.analysis.document_insights).map(([docId, insights]) => (
                                        <div key={docId} className="border-l-4 border-purple-200 pl-4 py-2 bg-purple-25">
                                          <div className="flex items-center justify-between mb-2">
                                            <h5 className="font-medium text-sm text-slate-800">Document Analysis</h5>
                                            <Badge
                                              variant={insights.risk_level === 'HIGH' ? 'destructive' :
                                                insights.risk_level === 'MEDIUM' ? 'outline' : 'default'}
                                              className="text-xs"
                                            >
                                              {insights.risk_level} Risk
                                            </Badge>
                                          </div>
                                          {insights.key_findings && insights.key_findings.length > 0 && (
                                            <div className="mb-2">
                                              <p className="text-xs font-medium text-slate-600 mb-1">Key Findings:</p>
                                              <ul className="text-xs text-slate-600 space-y-1">
                                                {insights.key_findings.map((finding, idx) => (
                                                  <li key={idx} className="flex items-start gap-1">
                                                    <span className="text-purple-500 mt-1">•</span>
                                                    {finding}
                                                  </li>
                                                ))}
                                              </ul>
                                            </div>
                                          )}
                                          <div className="flex items-center gap-4 text-xs text-slate-500">
                                            <span><strong>Completeness:</strong> {insights.completeness_score}%</span>
                                            <span><strong>Processed:</strong> {new Date(insights.processed_at).toLocaleString()}</span>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </>
                          )}

                          {/* In Progress State */}
                          {result.status === 'IN_PROGRESS' && (
                            <div className="text-center py-4">
                              <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-blue-600" />
                              <p className="text-sm text-muted-foreground">
                                AI analysis in progress...
                              </p>
                            </div>
                          )}

                          {/* Pending State */}
                          {result.status === 'PENDING' && (
                            <div className="text-center py-4">
                              <Clock className="w-6 h-6 mx-auto mb-2 text-gray-400" />
                              <p className="text-sm text-muted-foreground">
                                Waiting to start analysis...
                              </p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                );
              })}
            </Tabs>
          ) : (
            /* No Results State */
            <Card>
              <CardContent className="text-center py-8">
                <Bot className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">No AI Analysis Available</h3>
                <p className="text-muted-foreground mb-4">
                  Start an AI review to see detailed analysis from our specialized AI personas.
                </p>
                {!reviewData?.summary?.isRunning && (
                  <Button onClick={handleStartReview} disabled={isStarting}>
                    {isStarting ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <Play className="w-4 h-4 mr-2" />
                    )}
                    Start AI Review
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Action Buttons */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                {reviewData?.results && reviewData.results.length > 0
                  ? `${reviewData.results.filter(r => r.status === 'COMPLETED').length} of ${reviewData.results.length} analyses completed`
                  : 'Ready to start AI analysis'
                }
              </div>
              <div className="flex gap-3">
                <Link to="/document-upload">
                  <Button variant="outline" className="flex items-center gap-2">
                    <ArrowLeft className="w-4 h-4" />
                    Back to Upload
                  </Button>
                </Link>

                <Link to={`/report-generation?dealId=${dealId}`}>
                  <Button
                    className="flex items-center gap-2"
                    disabled={!reviewData?.results || reviewData.results.filter(r => r.status === 'COMPLETED').length < reviewData.results.length}
                  >
                    Generate Report
                    <FileText className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AIReview;
