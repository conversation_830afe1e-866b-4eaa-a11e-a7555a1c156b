import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Upload, 
  Search, 
  Filter, 
  FileText, 
  Download, 
  Eye,
  MoreHorizontal,
  Calendar,
  User,
  FolderOpen,
  Star,
  Archive
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Documents = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");

  const documents = [
    {
      id: 1,
      name: "TechCorp_Acquisition_Contract.pdf",
      category: "Legal",
      dealTitle: "TechCorp Acquisition",
      uploadDate: "2024-07-10",
      uploadedBy: "John Doe",
      size: "2.4 MB",
      status: "Reviewed",
      aiExtracted: true,
      starred: true
    },
    {
      id: 2,
      name: "Financial_Statements_Q2_2024.xlsx",
      category: "Financial",
      dealTitle: "TechCorp Acquisition",
      uploadDate: "2024-07-12",
      uploadedBy: "Jane Smith",
      size: "856 KB",
      status: "Pending Review",
      aiExtracted: false,
      starred: false
    },
    {
      id: 3,
      name: "Due_Diligence_Checklist.docx",
      category: "Operational",
      dealTitle: "StartupXYZ Investment",
      uploadDate: "2024-07-08",
      uploadedBy: "Mike Johnson",
      size: "124 KB",
      status: "Reviewed",
      aiExtracted: true,
      starred: false
    },
    {
      id: 4,
      name: "GDPR_Compliance_Report.pdf",
      category: "Compliance",
      dealTitle: "European Expansion Deal",
      uploadDate: "2024-07-15",
      uploadedBy: "Sarah Wilson",
      size: "1.8 MB",
      status: "Under Review",
      aiExtracted: true,
      starred: true
    },
    {
      id: 5,
      name: "Partnership_Agreement_Draft.pdf",
      category: "Legal",
      dealTitle: "Global Partners LLC",
      uploadDate: "2024-07-05",
      uploadedBy: "David Brown",
      size: "3.2 MB",
      status: "Approved",
      aiExtracted: true,
      starred: false
    },
    {
      id: 6,
      name: "Audit_Report_2023.pdf",
      category: "Financial",
      dealTitle: "Renewable Energy Project",
      uploadDate: "2024-07-14",
      uploadedBy: "Lisa Garcia",
      size: "4.1 MB",
      status: "Reviewed",
      aiExtracted: true,
      starred: true
    }
  ];

  const categories = ["all", "Legal", "Financial", "Operational", "Compliance"];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Legal": return "bg-blue-100 text-blue-800 border-blue-200";
      case "Financial": return "bg-green-100 text-green-800 border-green-200";
      case "Operational": return "bg-purple-100 text-purple-800 border-purple-200";
      case "Compliance": return "bg-orange-100 text-orange-800 border-orange-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved": return "bg-green-100 text-green-800 border-green-200";
      case "Reviewed": return "bg-blue-100 text-blue-800 border-blue-200";
      case "Under Review": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Pending Review": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.dealTitle.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === "all" || doc.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-light text-foreground">Documents</h1>
            <p className="text-muted-foreground font-light">Central repository for all deal documents</p>
          </div>
          <Button className="flex items-center gap-2 font-light">
            <Upload className="w-4 h-4" />
            Upload Documents
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">{documents.length}</div>
              <p className="text-xs text-muted-foreground font-light">Across all deals</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">AI Processed</CardTitle>
              <Archive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">{documents.filter(d => d.aiExtracted).length}</div>
              <p className="text-xs text-muted-foreground font-light">Data extracted</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Pending Review</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">{documents.filter(d => d.status === "Pending Review").length}</div>
              <p className="text-xs text-muted-foreground font-light">Need attention</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-light">Total Size</CardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-light">12.5 MB</div>
              <p className="text-xs text-muted-foreground font-light">Storage used</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input 
                    placeholder="Search documents by name or deal..." 
                    className="pl-10 font-light"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2 flex-wrap">
                {categories.map(category => (
                  <Button 
                    key={category}
                    variant={categoryFilter === category ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setCategoryFilter(category)}
                    className="font-light capitalize"
                  >
                    {category === "all" ? "All Categories" : category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documents List */}
        <Card>
          <CardHeader>
            <CardTitle className="font-light">Document Library</CardTitle>
            <CardDescription className="font-light">
              {filteredDocuments.length} of {documents.length} documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredDocuments.map((doc) => (
                <div key={doc.id} className="border rounded-lg p-4 hover:bg-muted/30 transition-colors">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 space-y-3">
                      {/* Document Header */}
                      <div className="flex items-center gap-3">
                        <FileText className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                        <div className="flex-1">
                          <h3 className="font-medium text-foreground">{doc.name}</h3>
                          <p className="text-sm text-muted-foreground font-light">{doc.dealTitle}</p>
                        </div>
                        {doc.starred && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                      </div>

                      {/* Badges */}
                      <div className="flex items-center gap-2 flex-wrap">
                        <Badge className={getCategoryColor(doc.category)}>{doc.category}</Badge>
                        <Badge className={getStatusColor(doc.status)}>{doc.status}</Badge>
                        {doc.aiExtracted && (
                          <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                            AI Processed
                          </Badge>
                        )}
                      </div>

                      {/* Document Meta */}
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          <span className="font-light">{doc.uploadedBy}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span className="font-light">{doc.uploadDate}</span>
                        </div>
                        <span className="font-light">{doc.size}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="font-light">
                        <Eye className="w-4 h-4 mr-2" />
                        Preview
                      </Button>
                      <Button variant="outline" size="sm" className="font-light">
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Star className="w-4 h-4 mr-2" />
                            {doc.starred ? "Unstar" : "Star"}
                          </DropdownMenuItem>
                          <DropdownMenuItem>Edit Tags</DropdownMenuItem>
                          <DropdownMenuItem>View History</DropdownMenuItem>
                          <DropdownMenuItem>Share</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredDocuments.length === 0 && (
              <div className="flex flex-col items-center justify-center py-12">
                <FileText className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No documents found</h3>
                <p className="text-muted-foreground text-center mb-4 font-light">
                  {searchQuery ? "Try adjusting your search terms or filters" : "Upload your first document to get started"}
                </p>
                {!searchQuery && (
                  <Button className="font-light">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Document
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Documents;