import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Upload,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  X,
  Download,
  Building,
  DollarSign,
  FileCheck,
  Scale,
  Shield,
  Briefcase,
  Users,
  Globe,
  Gavel,
  Leaf,
  Plus,
  Trash2,
  Edit,
  Loader2
} from "lucide-react";
import { Link, useSearchParams } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import documentService, { Document, UploadProgress } from "@/services/documentService";
import { DocumentService } from "@/services/documentService";
import { requisitionService } from "@/services/requisitionService";
import { dealService } from "@/services/dealService";
import { DealRequisition, CustomRequisitionItem } from "@/types/requisition";

// Define the initial requisition categories and items
const initialRequisitionCategories = [
  // ... keep existing code ...
  {
    id: "corporate",
    title: "Corporate and Secretarial",
    icon: Building,
    items: [
      "Organizational structure of the Company, covering all its subsidiaries and affiliates",
      "Articles and Memorandum of Association of the Company, along with Certificate of Incorporation",
      "Minutes book and minutes of meetings of Board of directors, Committees of directors, and Shareholders",
      "Copies of all registers maintained by the Company (members, foreign members, directors and KMPs, transfer of shares, debenture holders, charges/mortgages/debentures, guarantees, loans, etc.)",
      "Details of share capital amount and shareholding structure",
      "Details and documents pertaining to any convertible securities issued by the Company",
      "List of agreements in force, executed between the Company and its shareholders or third parties",
      "Any obligations relating to the sale, purchase, assignment, transfer, issuance, acquisition or encumbrance of shares",
      "Any restrictions, impairments or provisions requiring authorization for disposal of control or change in shareholding",
      "Restrictions or liens of any nature on the shares or quotas of the Company",
      "Copies and full description of share subscription and shareholders agreements, joint ventures, management agreements",
      "Disclosure in respect of any merger, spin-off, acquisition or sale of assets or legal reorganization procedure",
      "List of all powers of attorney in force with relevant scope, beneficiaries, authorities and expiration date",
      "List of all foreign investments and correspondence with Reserve Bank of India",
      "List and copy of all related party transactions over the last 3 years",
      "Statutory annual reports and board reports",
      "Copies of all returns, forms and statements filed with Registrar of Companies for the last 3 years",
      "Details of the management structure of the Company and all significant committees",
      "Agreements executed with directors and senior manager personnel",
      "Agreements with personnel having interest in equity",
      "Particulars of interests of directors/key managerial personnel in transactions",
      "Details of loans granted to directors/key managerial personnel/employees",
      "Copies of documents relating to changes in corporate structures",
      "List of all offices where the Company operates",
      "Details of shares/loan capital allotted since last audited financial statements",
      "Details of dividends/other distributions declared",
      "Copies of corporate brochures, trade pamphlets and marketing literature",
      "Details of all subsidiaries and shareholding patterns",
      "Form of Company's share certificates and security certificates",
      "Any agreement affecting the Company or its business with affiliates",
      "Details of auditors for the last three financial years"
    ]
  },
  {
    id: "financial",
    title: "Financial General Aspects",
    icon: DollarSign,
    items: [
      "Audited Financial Statements for the last three years and latest YTD financial information plus internal audit reports",
      "Particulars and list of bank accounts in any currency and jurisdiction with overdraft limits and authorized signatures",
      "Reports of board of directors, Company's auditors, unaudited financials"
    ]
  },
  {
    id: "contracts",
    title: "Contracts",
    icon: FileCheck,
    items: [
      "Copies of all commercial contracts for development of power projects/compressed bio-gas plant/infrastructure/lease agreements",
      "Copies of utilities agreements (power, water, jetty, etc.)",
      "Details and copies of any other contracts/agreements relating to the business",
      "Copy of all agreements involving exclusivity, reciprocity or non-competition arrangements",
      "Formats of all standard documentation utilized in usual course of business",
      "Contracts which could be terminated, amended or affected due to proposed transaction",
      "Details of any guarantees/indemnities, including letters of comfort",
      "Details of all consortium or joint venture or profit sharing agreements",
      "Details of intra-group agreements/arrangements, loans including indemnities or guarantees",
      "Details of intra-group arrangements involving sharing of resources",
      "Details of any other financing arrangements including off-balance sheet financing",
      "Copies of all contracts relating to capital commitments",
      "Details of all acquisitions or disposal of assets, businesses, companies",
      "Details of waivers, releases or write-offs of material claims, debts or rights",
      "Details and copies of all material service and maintenance agreements",
      "Copies of all agreements restricting sale or transfer of material assets",
      "Copies of all agreements imposing restrictions on business operations",
      "Details of any contract outside Company's ordinary course of business",
      "Copies of all agreements with governmental instrumentality, agency or authority",
      "Details of any escrow agreements",
      "Details of recurring payments not registered in Company's records",
      "Details of all matters under negotiation"
    ]
  },
  {
    id: "liabilities",
    title: "Liabilities",
    icon: Scale,
    items: [
      "List showing structure of financial liabilities and all financing documents",
      "Details of guarantees, bank/performance guarantees, standby letters of credit, indemnities, security bonds",
      "Details of guarantees granted to the Company by shareholders/directors",
      "Details of contingent liabilities or off-balance-sheet obligations",
      "List of all judicial and administrative lawsuits filed by or against the Company",
      "Details and documents of all securities provided for loan/credit facilities",
      "Current repayment status of all outstanding loans, credit, finance facilities",
      "Details of all inter-corporate loan/deposit arrangements",
      "Details of all financial lease, sale/leaseback arrangements, hire purchase arrangement",
      "Details of any other arrangements to raise finance",
      "Details and copies of any derivative contracts",
      "Details and copies of any shareholder commitments/obligations/undertakings",
      "Details of breaches of terms of any loan/credit facility",
      "Details of loan conversions, settlements, security invocations and financial statements",
      "Details of and documents relating to any trust deed",
      "Copies of all documents/communications regarding defaults or waivers"
    ]
  },
  {
    id: "regulatory",
    title: "Regulatory/Legal Approvals and Permits",
    icon: Shield,
    items: [
      "Detailed list and copies of all licenses, permits, registrations, concessions, approvals and authorizations",
      "Copy of application submitted to KERC for development of compressed bio-gas plant",
      "Details of necessary formalities, registration, certificates for import/export of goods",
      "Details of conditions for maintaining licenses, permits, authorizations, and consents",
      "Details of any notices/claims for alleged violations/non-compliance",
      "Copies of any government or regulatory report relating to business/affairs",
      "Copies of all other consents, approvals, licenses, registrations obtained from regulatory authorities",
      "Copies of all relevant tax registrations, capital subsidy schemes, licenses",
      "In-principle clearance approval and final approval granted",
      "Commissioning certificate",
      "Clearance from forest department",
      "Gram panchayat NOC approval",
      "Factories Registration with Labour Department",
      "Any other approval/license for development/establishment/operations of CBG Plant",
      "Details on governmental, multilateral or bilateral institution grants or subsidies"
    ]
  },
  {
    id: "assets",
    title: "Assets",
    icon: Briefcase,
    items: [
      "Details and documents pertaining to immovable properties owned/taken on lease",
      "Details and documents pertaining to movable properties including charges and encumbrances",
      "A schedule of easements, liens, restrictions, violations, covenants, and agreements affecting assets and leases"
    ]
  },
  {
    id: "ip",
    title: "Industrial and Intellectual Property",
    icon: Globe,
    items: [
      "Details of trademarks, patents, copyrights, designs and models or other intellectual property rights used/owned by Company"
    ]
  },
  {
    id: "labour",
    title: "Labour and Social Security Matter",
    icon: Users,
    items: [
      "Detailed list of employees (permanent and contractual) with age, gender, functions, salary and length of service",
      "Copies of standard employment contracts and service agreements with employees",
      "List of insurances taken by Company for Employees",
      "List of employees dismissed or suspended due to economic or disciplinary reasons in last 24 months",
      "Copies of all licenses/authorizations/permissions under relevant labour statutes",
      "Copies of returns and forms filed with statutory authorities under labour statutes for last 3 years",
      "Copy of registration documents under Employees' Provident Funds and Employee State Insurance Acts",
      "Details of loans/advances/credit facilities offered to employees"
    ]
  },
  {
    id: "insurance",
    title: "Insurance",
    icon: Shield,
    items: [
      "List of all insurance policies in force including directors and officers liability insurance",
      "List of all claims raised/settled in past three years and current unsettled claims",
      "List of all notices from insurance companies regarding alleged defaults or waivers",
      "Description of any pending negotiation and material correspondence with insurance companies",
      "Report about existence of insurance policies covering risks from business activities"
    ]
  },
  {
    id: "litigation",
    title: "Litigation",
    icon: Gavel,
    items: [
      "List of Disputes before any court, tribunal, quasi judicial or administrative body over last three years",
      "List of all resolved Disputes involving the Company or affiliates",
      "Correspondence or notices concerning Disputes with vendors, service providers, contractors, suppliers, customers, competitors or landlords",
      "Details of any circumstances which may give rise to any claim, litigation or Dispute",
      "Details of any current or threatened tax Disputes with relevant tax authority",
      "Details of any investigations, allegations, complaints for breaches of anti-trust, fair trading, consumer protection legislation",
      "All details of any claims or proceedings by regulatory authority including Competition Commission of India, Reserve Bank of India",
      "Details and copies of correspondence with Monopolies and Restrictive Trade Practices Commission, CCI or Competition Appellate Tribunal"
    ]
  },
  {
    id: "environment",
    title: "Environment",
    icon: Leaf,
    items: [
      "List and copies of all licenses, authorizations, permits from environmental and pollution control agencies",
      "Details and copies of all notices received for alleged violation of environmental law",
      "Details of waste/effluents/hazardous substances management and disposal systems",
      "Reports of any environmental impact assessments, audits, investigations or inspections",
      "Copies of compliance reports, statements and annual returns under Environmental Protection Act",
      "Copies of insurance policies under Public Liability Insurance Act"
    ]
  }
];

interface DocumentStatus {
  [categoryId: string]: {
    [itemId: string]: {
      status: 'pending' | 'uploaded' | 'approved' | 'rejected';
      documents: Document[];
      remarks: string;
      isUploading?: boolean;
      uploadProgress?: UploadProgress[];
    }
  }
}

const DocumentUpload = () => {
  const [searchParams] = useSearchParams();
  const dealId = searchParams.get('dealId') || '1';
  const { toast } = useToast();
  const [documentStatus, setDocumentStatus] = useState<DocumentStatus>({});
  const [isLoading, setIsLoading] = useState(true);
  const [dealRequisition, setDealRequisition] = useState<DealRequisition | null>(null);
  const [newItemInputs, setNewItemInputs] = useState<{ [categoryId: string]: string }>({});
  const [showNewItemInputs, setShowNewItemInputs] = useState<{ [categoryId: string]: boolean }>({});
  const [requisitionNotFound, setRequisitionNotFound] = useState(false);
  const [isCreatingRequisition, setIsCreatingRequisition] = useState(false);


  // Load deal requisition and existing documents
  useEffect(() => {
    const loadDealData = async () => {
      try {
        setIsLoading(true);

        // Load deal requisition
        const requisition = await requisitionService.getDealRequisition(dealId);
        if (requisition) {
          setDealRequisition(requisition);
          setRequisitionNotFound(false);

          // Load existing documents
          const documentsResponse = await documentService.getDocuments(dealId);

          // Initialize document status from existing data
          const initialStatus: DocumentStatus = {};

          console.log('📄 Loading existing documents:', {
            totalDocuments: documentsResponse.documents.length,
            documents: documentsResponse.documents.map(d => ({
              id: d.id,
              name: d.originalName,
              requisitionItemId: d.requisitionItemId
            }))
          });

          requisition.customData.categories.forEach(category => {
            initialStatus[category.id] = {};

            category.items.forEach(item => {
              // Match documents by templateItemId (preferred) or requisitionItemId (fallback)
              let itemDocuments = documentsResponse.documents.filter(
                doc => doc.templateItemId === item.id || doc.requisitionItemId === item.id
              );

              console.log(`📋 Item ${item.id} (${item.name}):`, {
                templateMatches: documentsResponse.documents.filter(doc => doc.templateItemId === item.id).length,
                requisitionMatches: documentsResponse.documents.filter(doc => doc.requisitionItemId === item.id).length,
                totalMatches: itemDocuments.length,
                totalDealDocuments: documentsResponse.documents.length
              });

              initialStatus[category.id][item.id] = {
                status: itemDocuments.length > 0 ? 'uploaded' : 'pending',
                documents: itemDocuments,
                remarks: itemDocuments[0]?.remarks || '',
              };
            });
          });



          setDocumentStatus(initialStatus);
        } else {
          setRequisitionNotFound(true);
        }
      } catch (error) {
        console.error('Error loading deal data:', error);

        // Check if it's a 404 error (requisition not found)
        if (error instanceof Error && error.message.includes('404')) {
          setRequisitionNotFound(true);
        } else {
          toast({
            title: "Error",
            description: "Failed to load deal requisition data.",
            variant: "destructive",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (dealId) {
      loadDealData();
    }
  }, [dealId, toast]);

  const createRequisition = async () => {
    try {
      setIsCreatingRequisition(true);

      // Create a requisition using the default template
      const newRequisition = await requisitionService.createDealRequisition({
        dealId,
        templateId: undefined // Let backend choose default template
      });

      if (newRequisition) {
        setDealRequisition(newRequisition);
        setRequisitionNotFound(false);

        // Initialize empty document status
        const initialStatus: DocumentStatus = {};
        newRequisition.customData.categories.forEach(category => {
          initialStatus[category.id] = {};
          category.items.forEach(item => {
            initialStatus[category.id][item.id] = {
              status: 'pending',
              documents: [],
              remarks: '',
            };
          });
        });
        setDocumentStatus(initialStatus);

        toast({
          title: "Requisition Created",
          description: "Document requisition has been created successfully.",
        });
      }
    } catch (error) {
      console.error('Error creating requisition:', error);
      toast({
        title: "Error",
        description: "Failed to create document requisition.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingRequisition(false);
    }
  };

  const addNewItem = async (categoryId: string) => {
    const newItemText = newItemInputs[categoryId]?.trim();
    if (!newItemText || !dealRequisition) return;

    try {
      // Add item to the deal requisition
      await requisitionService.addItemToCategory(dealId, categoryId, {
        name: newItemText,
        description: '',
        isRequired: false,
        acceptedFormats: [],
        maxFileSize: undefined,
        examples: [],
      });

      // Reload the deal requisition to get the updated data
      const updatedRequisition = await requisitionService.getDealRequisition(dealId);
      if (updatedRequisition) {
        setDealRequisition(updatedRequisition);

        // Find the new item and initialize its document status
        const category = updatedRequisition.customData.categories.find(c => c.id === categoryId);
        const newItem = category?.items.find(item => item.name === newItemText);

        if (newItem) {
          setDocumentStatus(prev => ({
            ...prev,
            [categoryId]: {
              ...prev[categoryId],
              [newItem.id]: {
                status: 'pending',
                documents: [],
                remarks: '',
              }
            }
          }));
        }
      }

      setNewItemInputs(prev => ({ ...prev, [categoryId]: '' }));
      setShowNewItemInputs(prev => ({ ...prev, [categoryId]: false }));

      toast({
        title: "Item Added",
        description: "New requisition item has been added successfully.",
      });
    } catch (error) {
      console.error('Error adding item:', error);
      toast({
        title: "Error",
        description: "Failed to add new requisition item.",
        variant: "destructive",
      });
    }
  };

  const deleteItem = async (categoryId: string, itemId: string) => {
    if (!dealRequisition) return;

    try {
      // Remove item from the deal requisition
      await requisitionService.removeItemFromCategory(dealId, categoryId, itemId);

      // Reload the deal requisition to get the updated data
      const updatedRequisition = await requisitionService.getDealRequisition(dealId);
      if (updatedRequisition) {
        setDealRequisition(updatedRequisition);

        // Remove the item from document status
        setDocumentStatus(prev => {
          const newCategoryStatus = { ...prev[categoryId] };
          delete newCategoryStatus[itemId];
          return { ...prev, [categoryId]: newCategoryStatus };
        });
      }

      toast({
        title: "Item Deleted",
        description: "Requisition item has been deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      toast({
        title: "Error",
        description: "Failed to delete requisition item.",
        variant: "destructive",
      });
    }
  };



  const handleFileUpload = async (categoryId: string, itemId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handleFileUpload called', { categoryId, itemId });
    const files = event.target.files;
    if (!files || !dealRequisition) {
      console.log('No files or no deal requisition', { files: !!files, dealRequisition: !!dealRequisition });
      return;
    }

    // Create array from files immediately to preserve references
    const fileArray = Array.from(files);

    // Clear the input to allow re-uploading the same file
    event.target.value = '';
    console.log('Files selected:', fileArray.map(f => ({
      name: f.name,
      size: f.size,
      type: f.type,
      lastModified: f.lastModified,
      isEmpty: f.size === 0
    })));

    // Check for empty files
    const emptyFiles = fileArray.filter(f => f.size === 0);
    if (emptyFiles.length > 0) {
      console.error('❌ Empty files detected:', emptyFiles.map(f => f.name));
      toast({
        title: "Invalid Files",
        description: `${emptyFiles.length} file(s) are empty and cannot be uploaded.`,
        variant: "destructive",
      });
      return;
    }

    // Validate files
    const invalidFiles = fileArray.filter(file =>
      !DocumentService.isFileTypeAllowed(file.name) ||
      !DocumentService.isFileSizeValid(file)
    );

    if (invalidFiles.length > 0) {
      toast({
        title: "Invalid Files",
        description: `${invalidFiles.length} file(s) are invalid. Please check file types and sizes.`,
        variant: "destructive",
      });
      return;
    }

    // Set uploading state
    setDocumentStatus(prev => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [itemId]: {
          ...prev[categoryId]?.[itemId],
          isUploading: true,
          uploadProgress: fileArray.map(file => ({
            filename: file.name,
            progress: 0,
            status: 'uploading' as const,
          })),
        }
      }
    }));

    try {
      console.log('Starting upload with params:', {
        fileCount: fileArray.length,
        dealId,
        itemId,
        remarks: documentStatus[categoryId]?.[itemId]?.remarks
      });

      const result = await documentService.uploadDocuments(
        fileArray,
        dealId,
        itemId,
        documentStatus[categoryId]?.[itemId]?.remarks,
        (progress) => {
          setDocumentStatus(prev => ({
            ...prev,
            [categoryId]: {
              ...prev[categoryId],
              [itemId]: {
                ...prev[categoryId]?.[itemId],
                uploadProgress: progress,
              }
            }
          }));
        }
      );

      // Update document status with uploaded documents
      setDocumentStatus(prev => ({
        ...prev,
        [categoryId]: {
          ...prev[categoryId],
          [itemId]: {
            ...prev[categoryId]?.[itemId],
            status: 'uploaded',
            documents: [...(prev[categoryId]?.[itemId]?.documents || []), ...result.documents],
            isUploading: false,
            uploadProgress: undefined,
          }
        }
      }));



      console.log('Upload successful:', result);

      toast({
        title: "Upload Successful",
        description: `${result.documents.length} file(s) uploaded successfully.`,
      });

      if (result.failed && result.failed.length > 0) {
        toast({
          title: "Some Files Failed",
          description: `${result.failed.length} file(s) failed to upload.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Upload error:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      setDocumentStatus(prev => ({
        ...prev,
        [categoryId]: {
          ...prev[categoryId],
          [itemId]: {
            ...prev[categoryId]?.[itemId],
            isUploading: false,
            uploadProgress: undefined,
          }
        }
      }));

      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload files.",
        variant: "destructive",
      });
    }

    // Clear the input
    event.target.value = '';
  };

  const downloadDocument = async (documentId: string, filename: string) => {
    try {
      const blob = await documentService.downloadDocument(documentId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Download Failed",
        description: error instanceof Error ? error.message : "Failed to download document.",
        variant: "destructive",
      });
    }
  };

  const deleteDocument = async (documentId: string, categoryId: string, itemId: string) => {
    try {
      await documentService.deleteDocument(documentId);

      // Update document status by removing the deleted document
      setDocumentStatus(prev => {
        const updatedDocuments = prev[categoryId]?.[itemId]?.documents.filter(
          doc => doc.id !== documentId
        ) || [];

        return {
          ...prev,
          [categoryId]: {
            ...prev[categoryId],
            [itemId]: {
              ...prev[categoryId]?.[itemId],
              documents: updatedDocuments,
              status: updatedDocuments.length > 0 ? 'uploaded' : 'pending',
            }
          }
        };
      });

      toast({
        title: "Document Deleted",
        description: "Document has been deleted successfully.",
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete document.",
        variant: "destructive",
      });
    }
  };

  const updateRemarks = (categoryId: string, itemId: string, remarks: string) => {
    setDocumentStatus(prev => ({
      ...prev,
      [categoryId]: {
        ...prev[categoryId],
        [itemId]: {
          ...prev[categoryId]?.[itemId],
          remarks
        }
      }
    }));
  };

  const handleSubmitDocuments = async () => {
    if (!dealRequisition) return;

    try {
      // Update the deal requisition as finalized
      await requisitionService.updateDealRequisition(dealId, {
        customData: dealRequisition.customData,
        isFinalized: true,
      });

      // Submit documents for AI review (changes deal status to DOCUMENTS_SUBMITTED)
      await dealService.submitDocumentsForReview(dealId);

      toast({
        title: "Documents Submitted",
        description: "Your documents have been submitted for AI review. The deal status has been updated.",
      });

      // Navigate to AI Review after a short delay
      setTimeout(() => {
        window.location.href = `/ai-review?dealId=${dealId}`;
      }, 1000);
    } catch (error) {
      console.error('Submit error:', error);
      toast({
        title: "Submit Failed",
        description: error instanceof Error ? error.message : "Failed to submit documents.",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: 'pending' | 'uploaded' | 'approved' | 'rejected') => {
    const variants = {
      pending: { variant: "secondary" as const, icon: Clock, text: "Pending", className: "" },
      uploaded: { variant: "default" as const, icon: CheckCircle, text: "Uploaded", className: "bg-green-100 text-green-800 border-green-200" },
      approved: { variant: "default" as const, icon: CheckCircle, text: "Approved", className: "bg-green-100 text-green-800 border-green-200" },
      rejected: { variant: "destructive" as const, icon: AlertCircle, text: "Rejected", className: "" }
    };

    const config = variants[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className={`flex items-center gap-1 ${config.className}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span>Loading deal requisition...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!dealRequisition) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="flex items-center gap-4">
            <Link to="/dashboard">
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {requisitionNotFound ? "No Requisition Found" : "Loading Requisition..."}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {requisitionNotFound
                    ? "No document requisition has been created for this deal yet. Create one to start uploading documents."
                    : "Please wait while we load the deal requisition data."
                  }
                </p>
                {requisitionNotFound && (
                  <Button
                    onClick={createRequisition}
                    disabled={isCreatingRequisition}
                    className="flex items-center gap-2"
                  >
                    {isCreatingRequisition ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Creating Requisition...
                      </>
                    ) : (
                      <>
                        <Plus className="w-4 h-4" />
                        Create Requisition
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link to="/dashboard">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
              <Upload className="w-8 h-8 text-primary" />
              Document Requisition List
            </h1>
            <p className="text-muted-foreground">
              {dealRequisition.deal.title} - {dealRequisition.deal.type} Due Diligence
            </p>
          </div>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
            <CardDescription>
              Please provide documents for each requisition item below. Documents should cover the last three (3) years unless otherwise specified.
            </CardDescription>
          </CardHeader>
        </Card>


        {/* Requisition Categories */}
        <Accordion type="multiple" className="space-y-4">
          {dealRequisition.customData.categories.map((category, categoryIndex) => {
            const categoryStatus = documentStatus[category.id] || {};
            const totalItems = category.items.length;
            const uploadedItems = Object.values(categoryStatus).filter(item => item.status === 'uploaded').length;

            return (
              <AccordionItem key={category.id} value={category.id}>
                <Card>
                  <AccordionTrigger className="hover:no-underline p-0">
                    <CardHeader className="flex-row items-center space-y-0 space-x-4 w-full">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <FileText className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1 text-left">
                        <CardTitle className="text-lg">{category.name}</CardTitle>
                        <CardDescription>
                          {uploadedItems} of {totalItems} items completed
                          {category.description && ` • ${category.description}`}
                        </CardDescription>
                      </div>
                      <div className="text-right">
                        <Badge variant={uploadedItems === totalItems ? "default" : "secondary"}>
                          {uploadedItems}/{totalItems}
                        </Badge>
                      </div>
                    </CardHeader>
                  </AccordionTrigger>
                  <AccordionContent>
                    <CardContent className="pt-0">
                      <div className="space-y-6">
                        {category.items.map((item, itemIndex) => {
                          const itemStatus = categoryStatus[item.id] || { status: 'pending' as const, documents: [], remarks: '' };

                          return (
                            <div key={item.id} className="border rounded-lg p-4 space-y-4">
                              <div className="flex items-start justify-between gap-4">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <span className="text-sm font-medium text-muted-foreground">
                                      {categoryIndex + 1}.{itemIndex + 1}
                                    </span>
                                    {getStatusBadge(itemStatus.status)}
                                    {item.isRequired && (
                                      <Badge variant="destructive" className="text-xs">
                                        Required
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-sm font-medium">{item.name}</p>
                                  {item.description && (
                                    <p className="text-xs text-muted-foreground mt-1">{item.description}</p>
                                  )}
                                  {item.acceptedFormats && item.acceptedFormats.length > 0 && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      Accepted formats: {item.acceptedFormats.join(', ')}
                                    </p>
                                  )}
                                  {item.maxFileSize && (
                                    <p className="text-xs text-muted-foreground">
                                      Max file size: {DocumentService.formatFileSize(item.maxFileSize)}
                                    </p>
                                  )}

                                  {/* Show uploaded documents for this item */}
                                  {itemStatus.documents && itemStatus.documents.length > 0 && (
                                    <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded-md">
                                      <div className="flex items-center gap-2 mb-2">
                                        <CheckCircle className="w-4 h-4 text-green-600" />
                                        <span className="text-sm font-medium text-green-800">
                                          {itemStatus.documents.length} document{itemStatus.documents.length > 1 ? 's' : ''} uploaded
                                        </span>
                                      </div>
                                      <div className="space-y-1">
                                        {itemStatus.documents.map((document, docIndex) => (
                                          <div key={document.id} className="flex items-center gap-2 text-xs text-green-700">
                                            <FileText className="w-3 h-3" />
                                            <span className="truncate flex-1">{document.originalName}</span>
                                            <span className="text-green-600">
                                              {DocumentService.formatFileSize(document.fileSize)}
                                            </span>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                                {item.isCustom && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deleteItem(category.id, item.id)}
                                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                )}
                              </div>

                              {/* File Upload */}
                              <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={itemStatus.isUploading}
                                    onClick={() => {
                                      console.log('Upload button clicked for:', { categoryId: category.id, itemId: item.id });
                                      const fileInput = document.getElementById(`file-${category.id}-${item.id}`) as HTMLInputElement;
                                      if (fileInput) {
                                        fileInput.click();
                                      } else {
                                        console.error('File input not found:', `file-${category.id}-${item.id}`);
                                      }
                                    }}
                                  >
                                    {itemStatus.isUploading ? (
                                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    ) : (
                                      <Upload className="w-4 h-4 mr-2" />
                                    )}
                                    {itemStatus.isUploading
                                      ? 'Uploading...'
                                      : itemStatus.documents && itemStatus.documents.length > 0
                                        ? 'Add More Documents'
                                        : 'Upload Documents'
                                    }
                                  </Button>
                                  <Input
                                    id={`file-${category.id}-${item.id}`}
                                    type="file"
                                    multiple
                                    accept={item.acceptedFormats?.length ?
                                      item.acceptedFormats.map(f => `.${f}`).join(',') :
                                      ".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                                    }
                                    onChange={(e) => handleFileUpload(category.id, item.id, e)}
                                    className="hidden"
                                    disabled={itemStatus.isUploading}
                                  />


                                </div>

                                {/* Upload Progress */}
                                {itemStatus.uploadProgress && (
                                  <div className="space-y-2">
                                    {itemStatus.uploadProgress.map((progress, idx) => (
                                      <div key={idx} className="space-y-1">
                                        <div className="flex items-center justify-between text-sm">
                                          <span className="truncate">{progress.filename}</span>
                                          <span>{progress.progress}%</span>
                                        </div>
                                        <Progress value={progress.progress} className="h-2" />
                                        {progress.error && (
                                          <p className="text-xs text-destructive">{progress.error}</p>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                )}

                                {/* Uploaded Documents */}
                                {itemStatus.documents.length > 0 && (
                                  <div className="space-y-2">
                                    <Label className="text-sm font-medium">Uploaded Documents:</Label>
                                    {itemStatus.documents.map((document) => (
                                      <div key={document.id} className="flex items-center justify-between p-2 bg-muted rounded">
                                        <div className="flex items-center gap-2">
                                          <FileText className="w-4 h-4 text-primary" />
                                          <div className="flex-1">
                                            <span className="text-sm font-medium">{document.originalName}</span>
                                            <div className="text-xs text-muted-foreground">
                                              {DocumentService.formatFileSize(document.fileSize)} •
                                              {new Date(document.createdAt).toLocaleDateString()}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => downloadDocument(document.id, document.originalName)}
                                            className="h-6 w-6 p-0"
                                          >
                                            <Download className="w-3 h-3" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => deleteDocument(document.id, category.id, item.id)}
                                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                          >
                                            <X className="w-3 h-3" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}

                                {/* Remarks */}
                                <div className="space-y-2">
                                  <Label htmlFor={`remarks-${category.id}-${item.id}`} className="text-sm font-medium">
                                    Remarks/Status:
                                  </Label>
                                  <Textarea
                                    id={`remarks-${category.id}-${item.id}`}
                                    placeholder="Add any remarks, status updates, or reasons if documents are not available..."
                                    value={itemStatus.remarks}
                                    onChange={(e) => updateRemarks(category.id, item.id, e.target.value)}
                                    className="min-h-[60px]"
                                  />
                                </div>
                              </div>
                            </div>
                          );
                        })}

                        {/* Add New Item Section */}
                        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                          {!showNewItemInputs[category.id] ? (
                            <Button
                              variant="ghost"
                              onClick={() => setShowNewItemInputs(prev => ({ ...prev, [category.id]: true }))}
                              className="w-full text-muted-foreground hover:text-foreground"
                            >
                              <Plus className="w-4 h-4 mr-2" />
                              Add New Requisition Item
                            </Button>
                          ) : (
                            <div className="space-y-3">
                              <Label className="text-sm font-medium">New Requisition Item:</Label>
                              <Textarea
                                placeholder="Enter the new requisition item description..."
                                value={newItemInputs[category.id] || ''}
                                onChange={(e) => setNewItemInputs(prev => ({ ...prev, [category.id]: e.target.value }))}
                                className="min-h-[80px]"
                              />
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={() => addNewItem(category.id)}
                                  disabled={!newItemInputs[category.id]?.trim()}
                                >
                                  <Plus className="w-4 h-4 mr-2" />
                                  Add Item
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setShowNewItemInputs(prev => ({ ...prev, [category.id]: false }));
                                    setNewItemInputs(prev => ({ ...prev, [category.id]: '' }));
                                  }}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </AccordionContent>
                </Card>
              </AccordionItem>
            );
          })}
        </Accordion>

        {/* Actions */}
        <div className="flex justify-between">
          <Link to="/deals">
            <Button variant="outline">
              Back to Deals
            </Button>
          </Link>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download Checklist
            </Button>
            <Button onClick={handleSubmitDocuments}>
              Submit Documents
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUpload;