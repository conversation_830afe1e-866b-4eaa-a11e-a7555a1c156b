import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { personaService, PersonaDefinition } from "../services/personaService";
import {
  User,
  Bell,
  Shield,
  Palette,
  Database,
  Users,
  Key,
  Mail,
  Globe,
  Smartphone,
  Brain,
  Loader2
} from "lucide-react";

const Settings = () => {
  const [notifications, setNotifications] = useState({
    email: true,
    push: false,
    dealUpdates: true,
    riskAlerts: true,
    weeklyReports: false
  });

  const [theme, setTheme] = useState("light");
  const [personas, setPersonas] = useState<PersonaDefinition[]>([]);
  const [selectedPersonas, setSelectedPersonas] = useState<string[]>([]);
  const [loadingPersonas, setLoadingPersonas] = useState(true);
  const [savingPersonas, setSavingPersonas] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Load personas and user preferences
  useEffect(() => {
    const loadPersonaData = async () => {
      try {
        setLoadingPersonas(true);
        const [personaDefinitions, userSelectedPersonas] = await Promise.all([
          personaService.getPersonaDefinitions(),
          personaService.getUserSelectedPersonas()
        ]);
        setPersonas(personaDefinitions);
        setSelectedPersonas(userSelectedPersonas);
      } catch (error) {
        console.error('Error loading persona data:', error);
      } finally {
        setLoadingPersonas(false);
      }
    };

    loadPersonaData();
  }, []);

  // Handle persona selection change
  const handlePersonaToggle = (personaType: string, checked: boolean) => {
    setSelectedPersonas(prev => {
      if (checked) {
        return [...prev, personaType];
      } else {
        return prev.filter(p => p !== personaType);
      }
    });
  };

  // Save persona preferences
  const savePersonaPreferences = async () => {
    try {
      setSavingPersonas(true);
      await personaService.updateUserSelectedPersonas(selectedPersonas);
      // You could add a toast notification here
    } catch (error) {
      console.error('Error saving persona preferences:', error);
      // You could add an error toast notification here
    } finally {
      setSavingPersonas(false);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-light text-foreground">Settings</h1>
          <p className="text-muted-foreground font-light">Manage your account preferences and application settings</p>
        </div>

        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-light">
              <User className="w-5 h-5" />
              Profile Settings
            </CardTitle>
            <CardDescription className="font-light">Update your personal information and preferences</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="font-light">First Name</Label>
                <Input
                  id="firstName"
                  defaultValue={user?.firstName || ""}
                  className="font-light"
                  placeholder="Enter your first name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName" className="font-light">Last Name</Label>
                <Input
                  id="lastName"
                  defaultValue={user?.lastName || ""}
                  className="font-light"
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="font-light">Email Address</Label>
              <Input
                id="email"
                type="email"
                defaultValue={user?.email || ""}
                className="font-light"
                disabled
              />
              <p className="text-xs text-muted-foreground">Email cannot be changed. Contact support if needed.</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="organization" className="font-light">Organization</Label>
              <Input
                id="organization"
                defaultValue={user?.organization?.name || "Your Organization"}
                className="font-light"
                disabled
              />
            </div>

            <div className="space-y-2">
              <Label className="font-light">Role</Label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{user?.role?.replace('_', ' ') || 'User'}</Badge>
                <Button variant="outline" size="sm" className="font-light">Request Role Change</Button>
              </div>
            </div>

            <div className="pt-4">
              <Button className="font-light">Save Changes</Button>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-light">
              <Bell className="w-5 h-5" />
              Notifications
            </CardTitle>
            <CardDescription className="font-light">Configure how you receive updates and alerts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <Label className="font-light">Email Notifications</Label>
                  </div>
                  <p className="text-sm text-muted-foreground font-light">Receive notifications via email</p>
                </div>
                <Switch
                  checked={notifications.email}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, email: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Smartphone className="w-4 h-4 text-muted-foreground" />
                    <Label className="font-light">Push Notifications</Label>
                  </div>
                  <p className="text-sm text-muted-foreground font-light">Browser and mobile push notifications</p>
                </div>
                <Switch
                  checked={notifications.push}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, push: checked }))}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-light">Deal Updates</Label>
                  <p className="text-sm text-muted-foreground font-light">Status changes and progress updates</p>
                </div>
                <Switch
                  checked={notifications.dealUpdates}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, dealUpdates: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-light">Risk Alerts</Label>
                  <p className="text-sm text-muted-foreground font-light">High-priority risk and compliance alerts</p>
                </div>
                <Switch
                  checked={notifications.riskAlerts}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, riskAlerts: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-light">Weekly Reports</Label>
                  <p className="text-sm text-muted-foreground font-light">Summary of deals and insights</p>
                </div>
                <Switch
                  checked={notifications.weeklyReports}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, weeklyReports: checked }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-light">
              <Palette className="w-5 h-5" />
              Appearance
            </CardTitle>
            <CardDescription className="font-light">Customize the look and feel of the application</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label className="font-light">Theme</Label>
              <div className="flex gap-2">
                <Button
                  variant={theme === "light" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTheme("light")}
                  className="font-light"
                >
                  Light
                </Button>
                <Button
                  variant={theme === "dark" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTheme("dark")}
                  className="font-light"
                >
                  Dark
                </Button>
                <Button
                  variant={theme === "system" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTheme("system")}
                  className="font-light"
                >
                  System
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI Persona Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-light">
              <Brain className="w-5 h-5" />
              AI Persona Preferences
            </CardTitle>
            <CardDescription className="font-light">
              Select which AI personas you want to use for due diligence analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {loadingPersonas ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin" />
                <span className="ml-2 font-light">Loading personas...</span>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {personas.map((persona) => (
                    <div key={persona.type} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Checkbox
                              id={persona.type}
                              checked={selectedPersonas.includes(persona.type)}
                              onCheckedChange={(checked) =>
                                handlePersonaToggle(persona.type, checked as boolean)
                              }
                            />
                            <Label
                              htmlFor={persona.type}
                              className="font-medium cursor-pointer"
                            >
                              {persona.name}
                            </Label>
                          </div>
                          <p className="text-sm text-muted-foreground font-light mb-2">
                            {persona.description}
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {persona.focusAreas.slice(0, 3).map((area) => (
                              <Badge key={area} variant="secondary" className="text-xs">
                                {area.replace(/_/g, ' ')}
                              </Badge>
                            ))}
                            {persona.focusAreas.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{persona.focusAreas.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">
                      Selected: {selectedPersonas.length} of {personas.length} personas
                    </p>
                    <p className="text-xs text-muted-foreground font-light">
                      Choose the personas that best match your due diligence requirements
                    </p>
                  </div>
                  <Button
                    onClick={savePersonaPreferences}
                    disabled={savingPersonas}
                    className="font-light"
                  >
                    {savingPersonas ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Preferences'
                    )}
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-light">
              <Shield className="w-5 h-5" />
              Security & Privacy
            </CardTitle>
            <CardDescription className="font-light">Manage your security settings and data privacy</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Key className="w-4 h-4 text-muted-foreground" />
                  <Label className="font-light">Two-Factor Authentication</Label>
                </div>
                <p className="text-sm text-muted-foreground font-light">Add an extra layer of security to your account</p>
              </div>
              <Button variant="outline" size="sm" className="font-light">Enable 2FA</Button>
            </div>

            <Separator />

            <div className="space-y-2">
              <Button
                variant="outline"
                className="w-full font-light"
                onClick={() => navigate('/change-password')}
              >
                Change Password
              </Button>
              <Button variant="outline" className="w-full font-light">
                Download My Data
              </Button>
              <Button variant="outline" className="w-full font-light">
                Privacy Settings
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Team & Organization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-light">
              <Users className="w-5 h-5" />
              Team & Organization
            </CardTitle>
            <CardDescription className="font-light">Manage team members and organization settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button variant="outline" className="font-light">
                <Users className="w-4 h-4 mr-2" />
                Manage Team
              </Button>
              <Button variant="outline" className="font-light">
                <Globe className="w-4 h-4 mr-2" />
                Organization Settings
              </Button>
            </div>

            <div className="text-sm text-muted-foreground font-light">
              <p>Current team: 8 members</p>
              <p>Organization: Acme Investment Corp</p>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button className="font-light">Save All Changes</Button>
        </div>
      </div>
    </div>
  );
};

export default Settings;