// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
  ENDPOINTS: {
    AUTH: '/auth',
    DEALS: '/deals',
    REQUISITIONS: '/requisitions',
    DOCUMENTS: '/documents',
    REVIEWS: '/reviews',
    REPORTS: '/reports',
    USERS: '/users',
    AI_REVIEW: '/ai-review',
  },
  TIMEOUT: 30000, // 30 seconds
};

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string) => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Helper function to get auth headers
export const getAuthHeaders = () => {
  const token = localStorage.getItem('accessToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};
