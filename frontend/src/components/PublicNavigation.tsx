import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";

interface PublicNavigationProps {
  showBackButton?: boolean;
  currentPage?: string;
}

export const PublicNavigation = ({ showBackButton = false, currentPage }: PublicNavigationProps) => {
  return (
    <nav className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-4">
            {showBackButton && (
              <Link to="/" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Link>
            )}
            {!showBackButton && (
              <Link to="/" className="flex items-center">
                <h1 className="text-2xl font-bold text-primary">Due Diligence Nexus</h1>
              </Link>
            )}
          </div>
          
          {!showBackButton && (
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-primary">Due Diligence Nexus</h1>
            </div>
          )}
          
          {showBackButton && (
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-primary">Due Diligence Nexus</h1>
            </div>
          )}
          
          <div className="hidden md:flex items-center space-x-8">
            {currentPage !== "about" && (
              <Link to="/about" className="text-muted-foreground hover:text-primary transition-colors">
                About
              </Link>
            )}
            {currentPage !== "features" && (
              <Link to="/features" className="text-muted-foreground hover:text-primary transition-colors">
                Features
              </Link>
            )}
            {currentPage !== "contact" && (
              <Link to="/contact" className="text-muted-foreground hover:text-primary transition-colors">
                Contact
              </Link>
            )}
            <Link to="/login">
              <Button variant="outline" size="sm">
                Sign In
              </Button>
            </Link>
            <Link to="/signup">
              <Button size="sm">
                Get Started
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};
