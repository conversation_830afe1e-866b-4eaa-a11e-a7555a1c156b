import React from "react";
import { cn } from "@/lib/utils";
import { CurrencySelect } from "./currency-select";
import { Input } from "./input";

interface CurrencyValueInputProps {
  currencyValue?: string;
  onCurrencyChange?: (value: string) => void;
  numberValue?: string;
  onNumberChange?: (value: string) => void;
  currencyName: string;
  numberName: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  id?: string;
}

/**
 * Combined Currency and Value Input Component
 *
 * This component creates a phone-number-like input where:
 * - Left side: Currency selector (shows symbol/flag)
 * - Right side: Number input for the value
 * - Both parts are visually connected in a single input-like container
 */
export const CurrencyValueInput = React.forwardRef<
  HTMLDivElement,
  CurrencyValueInputProps
>(
  (
    {
      currencyValue,
      onCurrencyChange,
      numberValue,
      onNumberChange,
      currencyName,
      numberName,
      placeholder = "Enter amount",
      className,
      disabled = false,
      required = false,
      id,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex items-stretch rounded-md border border-input bg-background ring-offset-background h-10",
          "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
          disabled && "cursor-not-allowed opacity-50",
          className
        )}
        {...props}
      >
        {/* Currency Selector */}
        <div className="flex items-center px-3 border-r border-border bg-muted/30">
          <CurrencySelect
            name={currencyName}
            value={currencyValue}
            onValueChange={onCurrencyChange}
            variant="small"
            showFlag={true}
            showSymbol={true}
            disabled={disabled}
            className="border-0 bg-transparent focus:ring-0 focus:ring-offset-0 shadow-none h-auto min-w-0"
          />
        </div>

        {/* Number Input */}
        <Input
          id={id}
          name={numberName}
          type="number"
          value={numberValue}
          onChange={(e) => onNumberChange?.(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none flex-1 h-full rounded-l-none"
          min="0"
          step="any"
        />
      </div>
    );
  }
);

CurrencyValueInput.displayName = "CurrencyValueInput";

export default CurrencyValueInput;

/**
 * Usage Example:
 *
 * const [currency, setCurrency] = useState("USD");
 * const [amount, setAmount] = useState("");
 *
 * <CurrencyValueInput
 *   currencyValue={currency}
 *   onCurrencyChange={setCurrency}
 *   numberValue={amount}
 *   onNumberChange={setAmount}
 *   currencyName="currency"
 *   numberName="amount"
 *   placeholder="Enter deal value"
 *   required
 * />
 *
 * This will look like: [🇺🇸 $ USD | 1000000]
 * Where the left part (with gray background) is the currency selector
 * and right part is the number input - just like a phone number field!
 */
