import React from "react";

import { cn } from "@/lib/utils";

// shadcn
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// radix-ui
import { SelectProps } from "@radix-ui/react-select";

// types
export interface Currency {
  code: string;
  decimals: number;
  name: string;
  number: string;
  symbol: string;
  flag?: string;
}

// Popular currencies with their symbols and flag emojis
const POPULAR_CURRENCIES: Currency[] = [
  {
    code: "USD",
    name: "US Dollar",
    symbol: "$",
    decimals: 2,
    number: "840",
    flag: "🇺🇸",
  },
  {
    code: "EUR",
    name: "Euro",
    symbol: "€",
    decimals: 2,
    number: "978",
    flag: "🇪🇺",
  },
  {
    code: "GBP",
    name: "British Pound",
    symbol: "£",
    decimals: 2,
    number: "826",
    flag: "🇬🇧",
  },
  {
    code: "JP<PERSON>",
    name: "Japanese Yen",
    symbol: "¥",
    decimals: 0,
    number: "392",
    flag: "🇯🇵",
  },
  {
    code: "CAD",
    name: "Canadian Dollar",
    symbol: "C$",
    decimals: 2,
    number: "124",
    flag: "🇨🇦",
  },
  {
    code: "AUD",
    name: "Australian Dollar",
    symbol: "A$",
    decimals: 2,
    number: "036",
    flag: "🇦🇺",
  },
  {
    code: "CHF",
    name: "Swiss Franc",
    symbol: "CHF",
    decimals: 2,
    number: "756",
    flag: "🇨🇭",
  },
  {
    code: "CNY",
    name: "Chinese Yuan",
    symbol: "¥",
    decimals: 2,
    number: "156",
    flag: "🇨🇳",
  },
  {
    code: "INR",
    name: "Indian Rupee",
    symbol: "₹",
    decimals: 2,
    number: "356",
    flag: "🇮🇳",
  },
  {
    code: "KRW",
    name: "South Korean Won",
    symbol: "₩",
    decimals: 0,
    number: "410",
    flag: "🇰🇷",
  },
  {
    code: "SGD",
    name: "Singapore Dollar",
    symbol: "S$",
    decimals: 2,
    number: "702",
    flag: "🇸🇬",
  },
  {
    code: "HKD",
    name: "Hong Kong Dollar",
    symbol: "HK$",
    decimals: 2,
    number: "344",
    flag: "🇭🇰",
  },
  {
    code: "SEK",
    name: "Swedish Krona",
    symbol: "kr",
    decimals: 2,
    number: "752",
    flag: "🇸🇪",
  },
  {
    code: "NOK",
    name: "Norwegian Krone",
    symbol: "kr",
    decimals: 2,
    number: "578",
    flag: "🇳🇴",
  },
  {
    code: "DKK",
    name: "Danish Krone",
    symbol: "kr",
    decimals: 2,
    number: "208",
    flag: "🇩🇰",
  },
  {
    code: "NZD",
    name: "New Zealand Dollar",
    symbol: "NZ$",
    decimals: 2,
    number: "554",
    flag: "🇳🇿",
  },
  {
    code: "MXN",
    name: "Mexican Peso",
    symbol: "$",
    decimals: 2,
    number: "484",
    flag: "🇲🇽",
  },
  {
    code: "BRL",
    name: "Brazilian Real",
    symbol: "R$",
    decimals: 2,
    number: "986",
    flag: "🇧🇷",
  },
  {
    code: "RUB",
    name: "Russian Ruble",
    symbol: "₽",
    decimals: 2,
    number: "643",
    flag: "🇷🇺",
  },
  {
    code: "ZAR",
    name: "South African Rand",
    symbol: "R",
    decimals: 2,
    number: "710",
    flag: "🇿🇦",
  },
];

interface CurrencySelectProps extends Omit<SelectProps, "onValueChange"> {
  onValueChange?: (value: string) => void;
  onCurrencySelect?: (currency: Currency) => void;
  name: string;
  placeholder?: string;
  variant?: "default" | "small" | "icon-only";
  valid?: boolean;
  showFlag?: boolean;
  showSymbol?: boolean;
  className?: string;
}

const CurrencySelect = React.forwardRef<HTMLButtonElement, CurrencySelectProps>(
  (
    {
      value,
      onValueChange,
      onCurrencySelect,
      name,
      placeholder = "Select currency",
      variant = "default",
      valid = true,
      showFlag = true,
      showSymbol = true,
      className,
      ...props
    },
    ref
  ) => {
    // Use the popular currencies list
    const availableCurrencies = React.useMemo<Currency[]>(() => {
      return POPULAR_CURRENCIES.sort((a, b) => a.name.localeCompare(b.name));
    }, []);

    const handleValueChange = (newValue: string) => {
      const fullCurrencyData = availableCurrencies.find(
        (curr: Currency) => curr.code === newValue
      );
      if (fullCurrencyData) {
        if (onValueChange) {
          onValueChange(newValue);
        }
        if (onCurrencySelect) {
          onCurrencySelect(fullCurrencyData);
        }
      }
    };

    // Get the selected currency for display
    const currentCurrency = React.useMemo(() => {
      return availableCurrencies.find((curr: Currency) => curr.code === value);
    }, [value, availableCurrencies]);

    return (
      <Select
        value={value}
        onValueChange={handleValueChange}
        {...props}
        name={name}
        data-valid={valid}
      >
        <SelectTrigger
          className={cn(
            "w-full",
            variant === "small" && "w-fit gap-2",
            variant === "icon-only" && "w-10 justify-center",
            className
          )}
          data-valid={valid}
          ref={ref}
        >
          {currentCurrency ? (
            <SelectValue placeholder={placeholder}>
              <div className="flex items-center gap-2">
                <span className="font-medium text-lg">
                  {currentCurrency.symbol}
                </span>
              </div>
            </SelectValue>
          ) : (
            <SelectValue placeholder={placeholder} />
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {availableCurrencies.map((currency: Currency) => (
              <SelectItem key={currency.code} value={currency.code}>
                <span className="text-md w-8 text-center">
                  {currency.symbol}
                </span>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    );
  }
);

CurrencySelect.displayName = "CurrencySelect";

// Utility function to get currency symbol by code
export const getCurrencySymbol = (currencyCode?: string): string => {
  if (!currencyCode) {
    console.log("getCurrencySymbol: No currency code provided");
    return "$"; // Default fallback
  }

  console.log("getCurrencySymbol: Looking for currency:", currencyCode);

  const currency = POPULAR_CURRENCIES.find(
    (curr) => curr.code === currencyCode.toUpperCase()
  );

  console.log("getCurrencySymbol: Found currency:", currency);

  return currency?.symbol || "$"; // Fallback to $ if currency not found
};

// Utility function to get currency flag by code
export const getCurrencyFlag = (currencyCode?: string): string => {
  if (!currencyCode) return "🇺🇸"; // Default fallback

  const currency = POPULAR_CURRENCIES.find(
    (curr) => curr.code === currencyCode.toUpperCase()
  );

  return currency?.flag || "🇺🇸"; // Fallback to US flag if currency not found
};

// Utility function to get full currency data by code
export const getCurrencyData = (currencyCode?: string): Currency | null => {
  if (!currencyCode) return null;

  return (
    POPULAR_CURRENCIES.find(
      (curr) => curr.code === currencyCode.toUpperCase()
    ) || null
  );
};

export { CurrencySelect };

/**
 * Currency Select Component
 *
 * This component returns the currency CODE (e.g., "USD", "EUR") as the value,
 * while displaying the currency SYMBOL (e.g., "$", "€") prominently in the UI.
 *
 * Usage examples:
 *
 * // Basic usage - shows symbol prominently with code and name
 * <CurrencySelect
 *   name="currency"
 *   value={currencyCode}
 *   onValueChange={setCurrencyCode}
 * />
 *
 * // Small variant - shows symbol with small code
 * <CurrencySelect
 *   name="currency"
 *   variant="small"
 *   value={currencyCode}
 *   onValueChange={setCurrencyCode}
 * />
 *
 * // Icon only - shows only the symbol
 * <CurrencySelect
 *   name="currency"
 *   variant="icon-only"
 *   value={currencyCode}
 *   onValueChange={setCurrencyCode}
 * />
 *
 * // Without flags
 * <CurrencySelect
 *   name="currency"
 *   showFlag={false}
 *   value={currencyCode}
 *   onValueChange={setCurrencyCode}
 * />
 */
