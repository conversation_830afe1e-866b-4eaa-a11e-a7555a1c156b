import { useState } from "react";
import { <PERSON>Circle, X, Send, Bot, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  text: string;
  sender: "user" | "bot";
  timestamp: Date;
}

export function ChatBot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      text: "Hello! I'm your AI assistant for due diligence processes. I can help you navigate the platform, analyze documents, and answer questions about your deals. How can I assist you today?",
      sender: "bot",
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState("");

  const sendMessage = () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: input,
      sender: "user",
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput("");

    // Simulate bot response
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: getBotResponse(input),
        sender: "bot",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const getBotResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes("deal") || input.includes("register")) {
      return "To register a new deal, click the 'New Deal' button on the dashboard or navigate to the Deals section. I can guide you through the required fields like deal title, type, value, and key dates.";
    }
    
    if (input.includes("document") || input.includes("upload")) {
      return "You can upload documents in the Documents section or through individual deal pages. Supported formats include PDF, DOCX, and XLSX. Documents are automatically categorized as Legal, Financial, Operational, or Compliance.";
    }
    
    if (input.includes("ai") || input.includes("analysis")) {
      return "Our AI can extract key information from your documents including names, dates, financial figures, and risk factors. Visit the AI Insights section to ask specific questions about your deals or documents.";
    }
    
    if (input.includes("risk") || input.includes("compliance")) {
      return "Risk assessment is automatically performed on all deals. You can view risk scores, compliance checks, and regulatory flags in the dashboard. High-risk items are highlighted for immediate attention.";
    }
    
    if (input.includes("report")) {
      return "Generate comprehensive due diligence reports from the Report Generation section. Reports include executive summaries, key findings, compliance status, and recommendations, exportable as PDF or DOCX.";
    }
    
    return "I understand you're asking about the due diligence process. Could you be more specific about what you'd like help with? I can assist with deals, documents, AI analysis, risk assessment, or report generation.";
  };

  return (
    <>
      {/* Floating Chat Button */}
      {!isOpen && (
        <Button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50"
          size="lg"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      )}

      {/* Chat Window */}
      {isOpen && (
        <Card className="fixed bottom-6 right-6 w-96 h-[500px] flex flex-col shadow-2xl z-50 border-2">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <Bot className="h-5 w-5 text-primary" />
              AI Assistant
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          
          <CardContent className="flex-1 flex flex-col p-0">
            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-3",
                      message.sender === "user" ? "justify-end" : "justify-start"
                    )}
                  >
                    {message.sender === "bot" && (
                      <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                        <Bot className="h-4 w-4 text-primary-foreground" />
                      </div>
                    )}
                    
                    <div
                      className={cn(
                        "max-w-[80%] p-3 rounded-lg text-sm font-light",
                        message.sender === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted text-muted-foreground"
                      )}
                    >
                      {message.text}
                    </div>
                    
                    {message.sender === "user" && (
                      <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                        <User className="h-4 w-4 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Input */}
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask me anything about due diligence..."
                  onKeyPress={(e) => e.key === "Enter" && sendMessage()}
                  className="flex-1 font-light"
                />
                <Button onClick={sendMessage} size="sm" className="px-3">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}