export type DealType =
  | "MA"
  | "INVESTMENT"
  | "PARTNERSHIP"
  | "JOINT_VENTURE"
  | "ASSET_PURCHASE"
  | "IPO"
  | "DEBT_FINANCING";

export type DealStatus =
  | "DRAFT"
  | "ACTIVE"
  | "DOCUMENTS_SUBMITTED"
  | "UNDER_REVIEW"
  | "PENDING_APPROVAL"
  | "COMPLETED"
  | "CANCELLED"
  | "ON_HOLD";

export type PartyType =
  | "BUYER"
  | "SELLER"
  | "ADVISOR"
  | "BANK"
  | "LEGAL_COUNSEL"
  | "AUDITOR"
  | "OTHER";

export type DateType =
  | "SIGNING"
  | "CLOSING"
  | "DUE_DILIGENCE_START"
  | "DUE_DILIGENCE_END"
  | "BOARD_APPROVAL"
  | "REGULATORY_APPROVAL"
  | "FINANCING_COMMITMENT"
  | "OTHER";

export interface Deal {
  id: string;
  title: string;
  type: DealType;
  currency?: string;
  value?: number;
  jurisdiction?: string;
  buyer?: string;
  seller?: string;
  status: DealStatus;
  startDate?: string;
  expectedSigningDate?: string;
  expectedClosingDate?: string;
  actualClosingDate?: string;
  progress: number;
  industry?: string;
  description?: string;
  riskLevel?: "LOW" | "MEDIUM" | "HIGH";
  confidentialityLevel?: "PUBLIC" | "INTERNAL" | "CONFIDENTIAL" | "RESTRICTED";
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  createdById: string;
  assignedToId?: string;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  organization: {
    id: string;
    name: string;
  };
  parties: DealParty[];
  suppliers: DealSupplier[];
  dates: DealDate[];
  notes: DealNote[];
  _count?: {
    documents: number;
    reviews: number;
    comments: number;
  };
}

export interface DealParty {
  id: string;
  partyType: PartyType;
  name: string;
  role?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
    contactPerson?: string;
  };
  createdAt: string;
}

export interface DealSupplier {
  id: string;
  name: string;
  jurisdiction: string;
  contactInfo?: any;
  description?: string;
  createdAt: string;
}

export interface DealDate {
  id: string;
  dateType: DateType;
  dateValue: string;
  description?: string;
  createdAt: string;
}

export interface DealNote {
  id: string;
  note: string;
  createdAt: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface CreateDealSupplierRequest {
  name: string;
  jurisdiction: string;
  contactInfo?: any;
  description?: string;
}

export interface CreateDealRequest {
  title: string;
  type: DealType;
  value?: number;
  jurisdiction?: string;
  buyer?: string;
  seller?: string;
  suppliers?: CreateDealSupplierRequest[];
  startDate?: string;
  expectedSigningDate?: string;
  expectedClosingDate?: string;
  assignedToId?: string;
  parties?: CreateDealPartyRequest[];
  dates?: CreateDealDateRequest[];
  notes?: string;
  // Additional fields
  industry?: string;
  description?: string;
  riskLevel?: "LOW" | "MEDIUM" | "HIGH";
  confidentialityLevel?: "PUBLIC" | "INTERNAL" | "CONFIDENTIAL" | "RESTRICTED";
  // Requisition template selection
  requisitionTemplateId?: string;
  createDefaultRequisition?: boolean;
  // Requisition setup
  createRequisition?: boolean;
  customRequisitionData?: {
    categories: Array<{
      id: string;
      title: string;
      items: string[];
    }>;
  };
}

export interface CreateDealPartyRequest {
  partyType: PartyType;
  name: string;
  role?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
    contactPerson?: string;
  };
}

export interface CreateDealDateRequest {
  dateType: DateType;
  dateValue: string;
  description?: string;
}

export interface UpdateDealRequest {
  title?: string;
  type?: DealType;
  value?: number;
  jurisdiction?: string;
  buyer?: string;
  seller?: string;
  status?: DealStatus;
  startDate?: string;
  expectedSigningDate?: string;
  expectedClosingDate?: string;
  actualClosingDate?: string;
  progress?: number;
  assignedToId?: string;
}

export interface DealListQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: DealStatus;
  type?: DealType;
  assignedToId?: string;
  createdById?: string;
  organizationId?: string;
  sortBy?: "createdAt" | "updatedAt" | "title" | "value" | "progress";
  sortOrder?: "asc" | "desc";
  startDate?: string;
  endDate?: string;
}

export interface DealListResponse {
  deals: Deal[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    status?: DealStatus;
    type?: DealType;
    assignedToId?: string;
    createdById?: string;
  };
}

export interface DealAssignmentRequest {
  assignedToId: string;
  note?: string;
}

export interface DealStatusUpdateRequest {
  status: DealStatus;
  note?: string;
}

export interface AddDealNoteRequest {
  note: string;
}

export interface DealStatsResponse {
  total: number;
  byStatus: Record<DealStatus, number>;
  byType: Record<DealType, number>;
  totalValue: number;
  averageProgress: number;
  recentActivity: number;
}

// Frontend-specific interfaces for UI components
export interface DealCardProps {
  deal: Deal;
  onEdit?: (deal: Deal) => void;
  onDelete?: (dealId: string) => void;
  onAssign?: (dealId: string, userId: string) => void;
  onStatusChange?: (dealId: string, status: DealStatus) => void;
}

export interface DealFormData {
  title: string;
  type: DealType;
  value: string;
  jurisdiction: string;
  buyer: string;
  seller: string;
  startDate: string;
  expectedSigningDate: string;
  expectedClosingDate: string;
  assignedToId: string;
  notes: string;
}

export interface DealFilters {
  search: string;
  status: DealStatus | "all";
  type: DealType | "all";
  assignedToId: string | "all";
  sortBy: "createdAt" | "updatedAt" | "title" | "value" | "progress";
  sortOrder: "asc" | "desc";
}

// Deal type display mappings
export const DEAL_TYPE_LABELS: Record<DealType, string> = {
  MA: "M&A",
  INVESTMENT: "Investment",
  PARTNERSHIP: "Partnership",
  JOINT_VENTURE: "Joint Venture",
  ASSET_PURCHASE: "Asset Purchase",
  IPO: "IPO",
  DEBT_FINANCING: "Debt Financing",
};

export const DEAL_STATUS_LABELS: Record<DealStatus, string> = {
  DRAFT: "Draft",
  ACTIVE: "Active",
  DOCUMENTS_SUBMITTED: "Documents Submitted",
  UNDER_REVIEW: "Under Review",
  PENDING_APPROVAL: "Pending Approval",
  COMPLETED: "Completed",
  CANCELLED: "Cancelled",
  ON_HOLD: "On Hold",
};

export const PARTY_TYPE_LABELS: Record<PartyType, string> = {
  BUYER: "Buyer",
  SELLER: "Seller",
  ADVISOR: "Advisor",
  BANK: "Bank",
  LEGAL_COUNSEL: "Legal Counsel",
  AUDITOR: "Auditor",
  OTHER: "Other",
};

export const DATE_TYPE_LABELS: Record<DateType, string> = {
  SIGNING: "Signing Date",
  CLOSING: "Closing Date",
  DUE_DILIGENCE_START: "Due Diligence Start",
  DUE_DILIGENCE_END: "Due Diligence End",
  BOARD_APPROVAL: "Board Approval",
  REGULATORY_APPROVAL: "Regulatory Approval",
  FINANCING_COMMITMENT: "Financing Commitment",
  OTHER: "Other",
};
