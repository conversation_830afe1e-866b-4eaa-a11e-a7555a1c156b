import {
  LoginCredentials,
  RegisterData,
  AuthResponse,
  ApiResponse,
  User,
  ChangePasswordData,
  ForgotPasswordData,
  ResetPasswordData
} from '../types/auth';
import { buildApiUrl, API_CONFIG } from '@/config/api';

const API_BASE_URL = buildApiUrl(API_CONFIG.ENDPOINTS.AUTH);

class AuthService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    // Load tokens from localStorage on initialization
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  // Helper method to make API requests
  private async apiRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authorization header if token exists
    if (this.accessToken) {
      defaultHeaders.Authorization = `Bearer ${this.accessToken}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Store tokens in localStorage and memory
  private setTokens(tokens: { accessToken: string; refreshToken: string }) {
    this.accessToken = tokens.accessToken;
    this.refreshToken = tokens.refreshToken;

    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
  }

  // Clear tokens from localStorage and memory
  private clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;

    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  }

  // Store user data in localStorage
  private setUser(user: User) {
    localStorage.setItem('user', JSON.stringify(user));
  }

  // Get user data from localStorage
  getStoredUser(): User | null {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.accessToken && !!this.getStoredUser();
  }

  // Get current access token
  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Login
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.apiRequest<AuthResponse>('/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data) {
      this.setTokens(response.data.tokens);
      this.setUser(response.data.user);
      return response.data;
    }

    throw new Error('Login failed');
  }

  // Register
  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await this.apiRequest<AuthResponse>('/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      this.setTokens(response.data.tokens);
      this.setUser(response.data.user);
      return response.data;
    }

    throw new Error('Registration failed');
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await this.apiRequest('/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  // Refresh access token
  async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await this.apiRequest<{ accessToken: string; expiresIn: string }>('/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken: this.refreshToken }),
      });

      if (response.success && response.data) {
        this.accessToken = response.data.accessToken;
        localStorage.setItem('accessToken', response.data.accessToken);
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearTokens();
      throw error;
    }
  }

  // Get current user profile
  async getCurrentUser(): Promise<User> {
    const response = await this.apiRequest<User>('/me', {
      method: 'GET',
    });

    if (response.success && response.data) {
      this.setUser(response.data);
      return response.data;
    }

    throw new Error('Failed to get user profile');
  }

  // Update user profile
  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await this.apiRequest<User>('/me', {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      this.setUser(response.data);
      return response.data;
    }

    throw new Error('Failed to update profile');
  }

  // Change password
  async changePassword(data: ChangePasswordData): Promise<void> {
    const response = await this.apiRequest('/change-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error('Failed to change password');
    }
  }

  // Forgot password
  async forgotPassword(data: ForgotPasswordData): Promise<void> {
    const response = await this.apiRequest('/forgot-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error('Failed to send password reset email');
    }
  }

  // Reset password
  async resetPassword(data: ResetPasswordData): Promise<void> {
    const response = await this.apiRequest('/reset-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error('Failed to reset password');
    }
  }

  // Logout from all devices
  async logoutAll(): Promise<void> {
    try {
      await this.apiRequest('/logout-all', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout all API call failed:', error);
    } finally {
      this.clearTokens();
    }
  }
}

// Create and export a singleton instance
export const authService = new AuthService();
export default authService;
