import { buildApiUrl, API_CONFIG, getAuthHeaders } from '@/config/api';
import {
  RequisitionTemplate,
  DealRequisition,
  CreateRequisitionTemplateRequest,
  UpdateDealRequisitionRequest,
  CreateDealRequisitionRequest,
  RequisitionTemplateListQuery,
  RequisitionTemplateListResponse,
  RequisitionStats,
  RequisitionProgress,
} from '@/types/requisition';
import { ApiResponse } from '@/types/auth';

const API_BASE_URL = buildApiUrl(API_CONFIG.ENDPOINTS.REQUISITIONS);

class RequisitionService {
  // Helper method to make API requests
  private async apiRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...getAuthHeaders(),
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || data.error || 'Request failed');
    }

    return data;
  }

  // Get requisition templates
  async getRequisitionTemplates(
    query: RequisitionTemplateListQuery = {}
  ): Promise<RequisitionTemplateListResponse> {
    const searchParams = new URLSearchParams();

    if (query.page) searchParams.append('page', query.page.toString());
    if (query.limit) searchParams.append('limit', query.limit.toString());
    if (query.dealType) searchParams.append('dealType', query.dealType);
    if (query.industry) searchParams.append('industry', query.industry);
    if (query.search) searchParams.append('search', query.search);
    if (query.isDefault !== undefined) searchParams.append('isDefault', query.isDefault.toString());

    const endpoint = `/templates${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await this.apiRequest<RequisitionTemplateListResponse>(endpoint);

    return response.data;
  }

  // Get requisition template by ID
  async getRequisitionTemplateById(templateId: string): Promise<RequisitionTemplate> {
    const response = await this.apiRequest<RequisitionTemplate>(`/templates/${templateId}`);
    return response.data;
  }

  // Create requisition template
  async createRequisitionTemplate(
    data: CreateRequisitionTemplateRequest
  ): Promise<RequisitionTemplate> {
    const response = await this.apiRequest<RequisitionTemplate>('/templates', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response.data;
  }

  // Get deal requisition
  async getDealRequisition(dealId: string): Promise<DealRequisition | null> {
    try {
      const response = await this.apiRequest<DealRequisition>(`/deals/${dealId}`);
      return response.data;
    } catch (error) {
      // Return null if not found, throw for other errors
      if (error instanceof Error && error.message.includes('not found')) {
        return null;
      }
      throw error;
    }
  }

  // Create deal requisition
  async createDealRequisition(
    data: CreateDealRequisitionRequest
  ): Promise<DealRequisition> {
    const response = await this.apiRequest<DealRequisition>('/deals', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response.data;
  }

  // Update deal requisition
  async updateDealRequisition(
    dealId: string,
    data: UpdateDealRequisitionRequest
  ): Promise<DealRequisition> {
    const response = await this.apiRequest<DealRequisition>(`/deals/${dealId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return response.data;
  }

  // Get deal requisition progress
  async getDealRequisitionProgress(dealId: string): Promise<{
    stats: RequisitionStats;
    categoryProgress: RequisitionProgress[];
  }> {
    const response = await this.apiRequest<{
      stats: RequisitionStats;
      categoryProgress: RequisitionProgress[];
    }>(`/deals/${dealId}/progress`);
    return response.data;
  }

  // Finalize deal requisition
  async finalizeDealRequisition(dealId: string): Promise<DealRequisition> {
    const dealRequisition = await this.getDealRequisition(dealId);
    if (!dealRequisition) {
      throw new Error('Deal requisition not found');
    }

    return this.updateDealRequisition(dealId, {
      customData: dealRequisition.customData,
      isFinalized: true,
    });
  }

  // Add custom category to deal requisition
  async addCustomCategory(
    dealId: string,
    category: {
      name: string;
      description?: string;
      isRequired: boolean;
      items: {
        name: string;
        description?: string;
        isRequired: boolean;
        acceptedFormats?: string[];
        maxFileSize?: number;
        examples?: string[];
      }[];
    }
  ): Promise<DealRequisition> {
    const dealRequisition = await this.getDealRequisition(dealId);
    if (!dealRequisition) {
      throw new Error('Deal requisition not found');
    }

    const newCategory = {
      id: `custom_${Date.now()}`,
      name: category.name,
      description: category.description,
      orderIndex: dealRequisition.customData.categories.length,
      isRequired: category.isRequired,
      isCustom: true,
      items: category.items.map((item, index) => ({
        id: `custom_item_${Date.now()}_${index}`,
        name: item.name,
        description: item.description,
        isRequired: item.isRequired,
        orderIndex: index,
        isCustom: true,
        acceptedFormats: item.acceptedFormats || [],
        maxFileSize: item.maxFileSize,
        examples: item.examples || [],
        status: 'pending' as const,
        uploadedDocuments: [],
      })),
    };

    const updatedCustomData = {
      ...dealRequisition.customData,
      categories: [...dealRequisition.customData.categories, newCategory],
      metadata: {
        ...dealRequisition.customData.metadata,
        lastModified: new Date().toISOString(),
        version: dealRequisition.customData.metadata.version + 1,
      },
    };

    return this.updateDealRequisition(dealId, {
      customData: updatedCustomData,
    });
  }

  // Add custom item to existing category
  async addCustomItem(
    dealId: string,
    categoryId: string,
    item: {
      name: string;
      description?: string;
      isRequired: boolean;
      acceptedFormats?: string[];
      maxFileSize?: number;
      examples?: string[];
    }
  ): Promise<DealRequisition> {
    const dealRequisition = await this.getDealRequisition(dealId);
    if (!dealRequisition) {
      throw new Error('Deal requisition not found');
    }

    const categoryIndex = dealRequisition.customData.categories.findIndex(
      cat => cat.id === categoryId
    );

    if (categoryIndex === -1) {
      throw new Error('Category not found');
    }

    const category = dealRequisition.customData.categories[categoryIndex];
    const newItem = {
      id: `custom_item_${Date.now()}`,
      name: item.name,
      description: item.description,
      isRequired: item.isRequired,
      orderIndex: category.items.length,
      isCustom: true,
      acceptedFormats: item.acceptedFormats || [],
      maxFileSize: item.maxFileSize,
      examples: item.examples || [],
      status: 'pending' as const,
      uploadedDocuments: [],
    };

    const updatedCategories = [...dealRequisition.customData.categories];
    updatedCategories[categoryIndex] = {
      ...category,
      items: [...category.items, newItem],
    };

    const updatedCustomData = {
      ...dealRequisition.customData,
      categories: updatedCategories,
      metadata: {
        ...dealRequisition.customData.metadata,
        lastModified: new Date().toISOString(),
        version: dealRequisition.customData.metadata.version + 1,
      },
    };

    return this.updateDealRequisition(dealId, {
      customData: updatedCustomData,
    });
  }

  // Update item status (when document is uploaded/approved/rejected)
  async updateItemStatus(
    dealId: string,
    categoryId: string,
    itemId: string,
    status: 'pending' | 'uploaded' | 'approved' | 'rejected',
    uploadedDocument?: {
      id: string;
      filename: string;
      uploadedAt: string;
    }
  ): Promise<DealRequisition> {
    const dealRequisition = await this.getDealRequisition(dealId);
    if (!dealRequisition) {
      throw new Error('Deal requisition not found');
    }

    const categoryIndex = dealRequisition.customData.categories.findIndex(
      cat => cat.id === categoryId
    );

    if (categoryIndex === -1) {
      throw new Error('Category not found');
    }

    const category = dealRequisition.customData.categories[categoryIndex];
    const itemIndex = category.items.findIndex(item => item.id === itemId);

    if (itemIndex === -1) {
      throw new Error('Item not found');
    }

    const updatedCategories = [...dealRequisition.customData.categories];
    const updatedItems = [...category.items];

    updatedItems[itemIndex] = {
      ...updatedItems[itemIndex],
      status,
      uploadedDocuments: uploadedDocument
        ? [...(updatedItems[itemIndex].uploadedDocuments || []), uploadedDocument]
        : updatedItems[itemIndex].uploadedDocuments,
    };

    updatedCategories[categoryIndex] = {
      ...category,
      items: updatedItems,
    };

    const updatedCustomData = {
      ...dealRequisition.customData,
      categories: updatedCategories,
      metadata: {
        ...dealRequisition.customData.metadata,
        lastModified: new Date().toISOString(),
        version: dealRequisition.customData.metadata.version + 1,
      },
    };

    return this.updateDealRequisition(dealId, {
      customData: updatedCustomData,
    });
  }

  // Alias methods for compatibility
  async addItemToCategory(
    dealId: string,
    categoryId: string,
    item: {
      name: string;
      description?: string;
      isRequired: boolean;
      acceptedFormats?: string[];
      maxFileSize?: number;
      examples?: string[];
    }
  ): Promise<DealRequisition> {
    return this.addCustomItem(dealId, categoryId, item);
  }

  async removeItemFromCategory(
    dealId: string,
    categoryId: string,
    itemId: string
  ): Promise<DealRequisition> {
    return this.removeCustomItem(dealId, categoryId, itemId);
  }
}

// Create and export a singleton instance
export const requisitionService = new RequisitionService();
