import { buildApiUrl, API_CONFIG, getAuthHeaders } from '@/config/api';
import { ApiResponse } from '@/types/auth';

const API_BASE_URL = buildApiUrl(''); // Use base API URL since document routes are mounted at the base

// Document interfaces
export interface Document {
  id: string;
  filename: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  status: 'PENDING' | 'UPLOADED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'MISSING';
  version: number;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  dealId: string;
  requisitionItemId?: string;
  templateItemId?: string; // Frontend template item ID for matching
  uploadedBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  requisitionItem?: {
    id: string;
    name: string;
    description?: string;
    isRequired: boolean;
  };
  downloadUrl?: string;
}

export interface UploadDocumentResponse {
  success: boolean;
  message: string;
  documents: Document[];
  failed?: {
    filename: string;
    error: string;
  }[];
}

export interface DocumentListResponse {
  documents: Document[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface DocumentStats {
  totalDocuments: number;
  documentsByStatus: {
    [key: string]: number;
  };
  totalFileSize: number;
  averageFileSize: number;
  documentsByRequisitionItem: {
    itemId: string;
    itemName: string;
    documentCount: number;
    isRequired: boolean;
    hasDocuments: boolean;
  }[];
}

export interface UploadProgress {
  filename: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

class DocumentService {
  // Helper method to make API requests
  private async apiRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultHeaders: HeadersInit = {
      ...getAuthHeaders(),
    };

    // Don't set Content-Type for FormData requests - let browser set it with boundary
    if (!(options.body instanceof FormData)) {
      defaultHeaders['Content-Type'] = 'application/json';
    } else {
      // Explicitly remove Content-Type for FormData to let browser set it
      delete defaultHeaders['Content-Type'];
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    console.log('🌐 Making API request:', {
      url,
      method: config.method || 'GET',
      isFormData: config.body instanceof FormData,
      headers: config.headers,
      bodyType: config.body ? config.body.constructor.name : 'none'
    });

    const response = await fetch(url, config);

    console.log('📡 API Response:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      url: response.url
    });

    const data = await response.json();
    console.log('📦 Response data:', data);

    if (!response.ok) {
      console.error('❌ API Error:', {
        status: response.status,
        statusText: response.statusText,
        data
      });
      throw new Error(data.message || data.error || 'Request failed');
    }

    return data;
  }

  // Upload documents
  async uploadDocuments(
    files: File[],
    dealId: string,
    requisitionItemId?: string,
    remarks?: string,
    onProgress?: (progress: UploadProgress[]) => void
  ): Promise<UploadDocumentResponse> {
    console.log('DocumentService.uploadDocuments called with:', {
      fileCount: files.length,
      dealId,
      requisitionItemId,
      remarks
    });

    const formData = new FormData();

    // Validate and add files to FormData
    for (let index = 0; index < files.length; index++) {
      const file = files[index];
      console.log(`Processing file ${index}:`, {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        isEmpty: file.size === 0
      });

      // Check if file is empty
      if (file.size === 0) {
        console.error(`❌ File ${file.name} is empty!`);
        throw new Error(`File ${file.name} is empty and cannot be uploaded`);
      }

      // Test reading file content to ensure it's accessible
      try {
        const arrayBuffer = await file.arrayBuffer();
        console.log(`✅ File ${file.name} content verified:`, {
          contentSize: arrayBuffer.byteLength,
          firstBytes: Array.from(new Uint8Array(arrayBuffer.slice(0, 10))).map(b => b.toString(16).padStart(2, '0')).join(' ')
        });

        // Create a new File object from the arrayBuffer to ensure content is preserved
        const fileBlob = new Blob([arrayBuffer], { type: file.type });
        const newFile = new File([fileBlob], file.name, {
          type: file.type,
          lastModified: file.lastModified
        });

        console.log(`📎 Adding file to FormData:`, {
          name: newFile.name,
          size: newFile.size,
          type: newFile.type
        });

        formData.append('files', newFile);
      } catch (error) {
        console.error(`❌ Failed to read file ${file.name}:`, error);
        throw new Error(`Failed to read file ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (requisitionItemId) {
      console.log('Adding requisitionItemId:', requisitionItemId);
      formData.append('requisitionItemId', requisitionItemId);
    }

    if (remarks) {
      console.log('Adding remarks:', remarks);
      formData.append('remarks', remarks);
    }

    // Debug: Log FormData contents
    console.log('FormData contents:');
    const formDataEntries = [];
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`);
        formDataEntries.push({ key, type: 'File', name: value.name, size: value.size });
      } else {
        console.log(`  ${key}: ${value}`);
        formDataEntries.push({ key, type: 'String', value });
      }
    }

    console.log('FormData summary:', formDataEntries);

    // Initialize progress tracking
    const progressArray: UploadProgress[] = files.map(file => ({
      filename: file.name,
      progress: 0,
      status: 'uploading' as const,
    }));

    if (onProgress) {
      onProgress(progressArray);
    }

    try {
      console.log('Making API request to:', `/deals/${dealId}/documents/upload`);
      const response = await this.apiRequest<Document[]>(`/deals/${dealId}/documents/upload`, {
        method: 'POST',
        body: formData,
      });
      console.log('API response received:', response);

      // Update progress to completed
      const completedProgress = progressArray.map(p => ({
        ...p,
        progress: 100,
        status: 'completed' as const,
      }));

      if (onProgress) {
        onProgress(completedProgress);
      }

      return {
        success: response.success,
        message: response.message,
        documents: response.data,
        failed: (response as any).failed,
      };
    } catch (error) {
      // Update progress to error
      const errorProgress = progressArray.map(p => ({
        ...p,
        status: 'error' as const,
        error: error instanceof Error ? error.message : 'Upload failed',
      }));

      if (onProgress) {
        onProgress(errorProgress);
      }

      throw error;
    }
  }

  // Get documents for a deal
  async getDocuments(
    dealId: string,
    params?: {
      page?: number;
      limit?: number;
      status?: string;
      requisitionItemId?: string;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ): Promise<DocumentListResponse> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const queryString = queryParams.toString();
    const endpoint = `/deals/${dealId}/documents${queryString ? `?${queryString}` : ''}`;

    const response = await this.apiRequest<Document[]>(endpoint);

    return {
      documents: response.data,
      pagination: (response as any).pagination,
    };
  }

  // Get document by ID
  async getDocumentById(documentId: string): Promise<Document> {
    const response = await this.apiRequest<Document>(`/documents/${documentId}`);
    return response.data;
  }

  // Download document
  async downloadDocument(documentId: string): Promise<Blob> {
    const url = `${API_BASE_URL}/documents/${documentId}/download`;

    const response = await fetch(url, {
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Download failed');
    }

    return response.blob();
  }

  // Update document status
  async updateDocumentStatus(
    documentId: string,
    status: string,
    remarks?: string
  ): Promise<Document> {
    const response = await this.apiRequest<Document>(`/documents/${documentId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, remarks }),
    });

    return response.data;
  }

  // Delete document
  async deleteDocument(documentId: string): Promise<void> {
    await this.apiRequest(`/documents/${documentId}`, {
      method: 'DELETE',
    });
  }

  // Get document statistics
  async getDocumentStats(dealId: string): Promise<DocumentStats> {
    const response = await this.apiRequest<DocumentStats>(`/deals/${dealId}/documents/stats`);
    return response.data;
  }

  // Helper method to format file size
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Helper method to get file extension
  static getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || '';
  }

  // Helper method to check if file type is allowed
  static isFileTypeAllowed(filename: string): boolean {
    const allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png'];
    const extension = this.getFileExtension(filename);
    return allowedExtensions.includes(extension);
  }

  // Helper method to check if file type is supported by AI analysis
  static isFileTypeSupportedByAI(filename: string): boolean {
    const aiSupportedExtensions = ['pdf', 'txt', 'jpg', 'jpeg', 'png'];
    const extension = this.getFileExtension(filename);
    return aiSupportedExtensions.includes(extension);
  }

  // Helper method to validate file size
  static isFileSizeValid(file: File, maxSize: number = 50 * 1024 * 1024): boolean {
    return file.size <= maxSize;
  }
}

export { DocumentService };
export default new DocumentService();
