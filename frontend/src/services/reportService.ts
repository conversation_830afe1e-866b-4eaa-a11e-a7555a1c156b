import { buildApiUrl, getAuthHeaders } from '../config/api';

export type ReportType = 'EXECUTIVE_SUMMARY' | 'DETAILED_ANALYSIS' | 'RISK_ASSESSMENT' | 'COMPLIANCE_REPORT' | 'FINANCIAL_ANALYSIS' | 'LEGAL_REVIEW' | 'TECHNICAL_ASSESSMENT' | 'CUSTOM';

export type ReportStatus = 'GENERATING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

export interface ReportGeneration {
  id: string;
  type: ReportType;
  status: ReportStatus;
  progress: number;
  filePath?: string;
  fileSize?: number;
  errorMessage?: string;
  generatedAt?: string;
  createdAt: string;
  updatedAt: string;
  dealId: string;
  generatedById: string;
  generatedBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  deal?: {
    id: string;
    title: string;
    type: string;
    value?: number;
    buyer?: string;
    seller?: string;
  };
}

export interface ReportContent {
  content?: string;
  format?: string;
  generated_at?: string;
  sections?: {
    executive_summary?: any;
    financial_analysis?: any;
    legal_compliance?: any;
    operational_analysis?: any;
    market_analysis?: any;
    risk_assessment?: any;
    recommendations?: any;
  };
}

export interface GenerateReportRequest {
  reportType?: ReportType;
}

export interface ReportStatusResponse {
  id: string;
  status: ReportStatus;
  progress: number;
  errorMessage?: string;
  generatedAt?: string;
}

class ReportService {
  // Helper method for API calls
  private async apiCall(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = buildApiUrl(endpoint);
    const headers = getAuthHeaders();

    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle authentication errors gracefully
      if (response.status === 401) {
        console.warn('Authentication required for endpoint:', endpoint);
        throw new Error('Authentication required. Please log in to access this feature.');
      }

      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Generate a new report
  async generateReport(dealId: string, request: GenerateReportRequest = {}): Promise<ReportGeneration> {
    try {
      const response = await this.apiCall(`/deals/${dealId}/reports/generate`, {
        method: 'POST',
        body: JSON.stringify(request),
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to generate report');
    }
  }

  // Get all reports for a deal
  async getReportsByDeal(dealId: string): Promise<ReportGeneration[]> {
    try {
      const response = await this.apiCall(`/deals/${dealId}/reports`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get reports');
    }
  }

  // Get a specific report by ID
  async getReportById(reportId: string): Promise<ReportGeneration> {
    try {
      const response = await this.apiCall(`/reports/${reportId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get report');
    }
  }

  // Get report generation status
  async getReportStatus(reportId: string): Promise<ReportStatusResponse> {
    try {
      const response = await this.apiCall(`/reports/${reportId}/status`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get report status');
    }
  }

  // Download report as PDF
  async downloadReport(reportId: string): Promise<Blob> {
    try {
      const url = buildApiUrl(`/reports/${reportId}/download`);
      const headers = getAuthHeaders();

      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return response.blob();
    } catch (error: any) {
      throw new Error(error.message || 'Failed to download report');
    }
  }

  // Parse report content from JSON
  parseReportContent(report: ReportGeneration): ReportContent | null {
    if (!report.filePath) {
      return null;
    }

    try {
      let parsed = JSON.parse(report.filePath);

      // Handle double-encoded JSON
      if (typeof parsed === 'string') {
        try {
          parsed = JSON.parse(parsed);
        } catch {
          // If it fails, treat as plain text
          return {
            content: parsed,
            format: 'text',
            generated_at: report.generatedAt
          };
        }
      }

      // If it's already in the new format with markdown content
      if (parsed.format === 'markdown') {
        return parsed;
      }

      // Legacy format - return as is
      return parsed;
    } catch (error) {
      console.warn('Failed to parse report content:', error);
      return {
        content: report.filePath,
        format: 'text',
        generated_at: report.generatedAt
      };
    }
  }

  // Helper method to format report type for display
  formatReportType(type: ReportType): string {
    const typeLabels: Record<ReportType, string> = {
      EXECUTIVE_SUMMARY: 'Executive Summary',
      DETAILED_ANALYSIS: 'Detailed Analysis',
      RISK_ASSESSMENT: 'Risk Assessment',
      COMPLIANCE_REPORT: 'Compliance Report',
      FINANCIAL_ANALYSIS: 'Financial Analysis',
      LEGAL_REVIEW: 'Legal Review',
      TECHNICAL_ASSESSMENT: 'Technical Assessment',
      CUSTOM: 'Custom Report'
    };
    return typeLabels[type] || type;
  }

  // Helper method to format report status for display
  formatReportStatus(status: ReportStatus): string {
    const statusLabels: Record<ReportStatus, string> = {
      GENERATING: 'Generating',
      COMPLETED: 'Completed',
      FAILED: 'Failed',
      CANCELLED: 'Cancelled'
    };
    return statusLabels[status] || status;
  }

  // Helper method to get status color
  getStatusColor(status: ReportStatus): string {
    const colorMap: Record<ReportStatus, string> = {
      GENERATING: 'text-blue-600 bg-blue-100',
      COMPLETED: 'text-green-600 bg-green-100',
      FAILED: 'text-red-600 bg-red-100',
      CANCELLED: 'text-gray-600 bg-gray-100'
    };
    return colorMap[status] || 'text-gray-600 bg-gray-100';
  }

  // Poll report status until completion
  async pollReportStatus(reportId: string, onProgress?: (status: ReportStatusResponse) => void): Promise<ReportStatusResponse> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const status = await this.getReportStatus(reportId);

          if (onProgress) {
            onProgress(status);
          }

          if (status.status === 'COMPLETED' || status.status === 'FAILED' || status.status === 'CANCELLED') {
            resolve(status);
          } else {
            // Continue polling every 2 seconds
            setTimeout(poll, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}

export const reportService = new ReportService();
