import { ApiResponse } from "@/types/auth";
import {
  Deal,
  CreateDealRequest,
  UpdateDealRequest,
  DealListQuery,
  DealListResponse,
  DealAssignmentRequest,
  DealStatusUpdateRequest,
  AddDealNoteRequest,
  DealStatsResponse,
} from "@/types/deal";
import { buildApiUrl, getAuthHeaders, API_CONFIG } from "@/config/api";

class DealService {
  private baseUrl = buildApiUrl(API_CONFIG.ENDPOINTS.DEALS);

  // Helper method to make API requests
  private async apiRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        ...getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || errorData.message || "Request failed");
    }

    return response.json();
  }

  // Create a new deal
  async createDeal(dealData: CreateDealRequest): Promise<Deal> {
    const response = await this.apiRequest<Deal>("", {
      method: "POST",
      body: JSON.stringify(dealData),
    });

    if (!response.success || !response.data) {
      throw new Error("Failed to create deal");
    }

    return response.data;
  }

  // Get deals list with filtering and pagination
  async getDeals(query: DealListQuery = {}): Promise<DealListResponse> {
    const searchParams = new URLSearchParams();

    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = queryString ? `?${queryString}` : "";

    const response = await this.apiRequest<DealListResponse>(endpoint);

    if (!response.success || !response.data) {
      throw new Error("Failed to get deals");
    }

    return response.data;
  }

  // Get deal by ID
  async getDealById(dealId: string): Promise<Deal> {
    const response = await this.apiRequest<Deal>(`/${dealId}`);

    if (!response.success || !response.data) {
      throw new Error("Failed to get deal");
    }

    return response.data;
  }

  // Update deal
  async updateDeal(
    dealId: string,
    updateData: UpdateDealRequest
  ): Promise<Deal> {
    const response = await this.apiRequest<Deal>(`/${dealId}`, {
      method: "PUT",
      body: JSON.stringify(updateData),
    });

    if (!response.success || !response.data) {
      throw new Error("Failed to update deal");
    }

    return response.data;
  }

  // Delete deal
  async deleteDeal(dealId: string): Promise<void> {
    const response = await this.apiRequest(`/${dealId}`, {
      method: "DELETE",
    });

    if (!response.success) {
      throw new Error("Failed to delete deal");
    }
  }

  // Assign deal to user
  async assignDeal(
    dealId: string,
    assignmentData: DealAssignmentRequest
  ): Promise<Deal> {
    const response = await this.apiRequest<Deal>(`/${dealId}/assign`, {
      method: "POST",
      body: JSON.stringify(assignmentData),
    });

    if (!response.success || !response.data) {
      throw new Error("Failed to assign deal");
    }

    return response.data;
  }

  // Update deal status
  async updateDealStatus(
    dealId: string,
    statusData: DealStatusUpdateRequest
  ): Promise<Deal> {
    const response = await this.apiRequest<Deal>(`/${dealId}/status`, {
      method: "PUT",
      body: JSON.stringify(statusData),
    });

    if (!response.success || !response.data) {
      throw new Error("Failed to update deal status");
    }

    return response.data;
  }

  // Submit documents for AI review
  async submitDocumentsForReview(dealId: string): Promise<Deal> {
    const response = await this.apiRequest<Deal>(
      `/${dealId}/submit-documents`,
      {
        method: "POST",
      }
    );

    if (!response.success || !response.data) {
      throw new Error("Failed to submit documents for review");
    }

    return response.data;
  }

  // Add note to deal
  async addDealNote(
    dealId: string,
    noteData: AddDealNoteRequest
  ): Promise<Deal> {
    const response = await this.apiRequest<Deal>(`/${dealId}/notes`, {
      method: "POST",
      body: JSON.stringify(noteData),
    });

    if (!response.success || !response.data) {
      throw new Error("Failed to add note");
    }

    return response.data;
  }

  // Get deal statistics
  async getDealStats(): Promise<DealStatsResponse> {
    const response = await this.apiRequest<DealStatsResponse>("/stats");

    if (!response.success || !response.data) {
      throw new Error("Failed to get deal statistics");
    }

    return response.data;
  }

  // Helper methods for formatting and validation

  // Format deal value for display
  formatDealValue(value?: number): string {
    if (!value) return "N/A";

    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    } else {
      return `${value.toLocaleString()}`;
    }
  }

  // Format date for display
  formatDate(dateString?: string): string {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return "Invalid Date";
    }
  }

  // Calculate days until deadline
  getDaysUntilDeadline(dateString?: string): number | null {
    if (!dateString) return null;

    try {
      const deadline = new Date(dateString);
      const today = new Date();
      const diffTime = deadline.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch {
      return null;
    }
  }

  // Get status color for UI
  getStatusColor(status: string): string {
    switch (status) {
      case "COMPLETED":
        return "bg-green-100 text-green-800 border-green-200";
      case "ACTIVE":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "UNDER_REVIEW":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "PENDING_APPROVAL":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "DRAFT":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "CANCELLED":
        return "bg-red-100 text-red-800 border-red-200";
      case "ON_HOLD":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  }

  // Get risk level based on deal characteristics
  getRiskLevel(deal: Deal): "Low" | "Medium" | "High" {
    let riskScore = 0;

    // Value-based risk
    if (deal.value && deal.value > 100000000) riskScore += 2; // >$100M
    else if (deal.value && deal.value > 50000000) riskScore += 1; // >$50M

    // Type-based risk
    if (deal.type === "MA" || deal.type === "JOINT_VENTURE") riskScore += 1;

    // Status-based risk
    if (deal.status === "UNDER_REVIEW" || deal.status === "PENDING_APPROVAL")
      riskScore += 1;

    // Progress-based risk
    if (deal.progress < 30) riskScore += 1;

    // Deadline risk
    const daysUntil = this.getDaysUntilDeadline(deal.expectedClosingDate);
    if (daysUntil !== null && daysUntil < 30) riskScore += 1;

    if (riskScore >= 4) return "High";
    if (riskScore >= 2) return "Medium";
    return "Low";
  }

  // Validate deal form data
  validateDealData(data: Partial<CreateDealRequest>): string[] {
    const errors: string[] = [];

    if (!data.title || data.title.trim().length < 3) {
      errors.push("Title must be at least 3 characters long");
    }

    if (!data.type) {
      errors.push("Deal type is required");
    }

    if (data.value && data.value < 0) {
      errors.push("Deal value must be positive");
    }

    if (data.startDate && data.expectedClosingDate) {
      const startDate = new Date(data.startDate);
      const closingDate = new Date(data.expectedClosingDate);
      if (startDate >= closingDate) {
        errors.push("Expected closing date must be after start date");
      }
    }

    return errors;
  }
}

// Export singleton instance
export const dealService = new DealService();
export default dealService;
