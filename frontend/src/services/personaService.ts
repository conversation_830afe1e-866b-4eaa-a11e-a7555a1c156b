import { buildApiUrl, getAuthHeaders } from '../config/api';

export interface PersonaDefinition {
  type: string;
  name: string;
  description: string;
  focusAreas: string[];
  expertise: string;
}

export interface UserPersonaPreferences {
  selectedPersonas: string[];
}

class PersonaService {
  // Get all available persona definitions
  async getPersonaDefinitions(): Promise<PersonaDefinition[]> {
    try {
      const response = await fetch(buildApiUrl('/personas/definitions'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error fetching persona definitions:', error);
      throw error;
    }
  }

  // Get user's selected personas
  async getUserSelectedPersonas(): Promise<string[]> {
    try {
      const response = await fetch(buildApiUrl('/user/selected-personas'), {
        method: 'GET',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data.selectedPersonas;
    } catch (error) {
      console.error('Error fetching user selected personas:', error);
      throw error;
    }
  }

  // Update user's selected personas
  async updateUserSelectedPersonas(selectedPersonas: string[]): Promise<void> {
    try {
      const response = await fetch(buildApiUrl('/user/selected-personas'), {
        method: 'PUT',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ selectedPersonas }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || 'Failed to update selected personas');
      }
    } catch (error) {
      console.error('Error updating user selected personas:', error);
      throw error;
    }
  }

  // Get persona display name for UI
  getPersonaDisplayName(type: string): string {
    const displayNames: Record<string, string> = {
      FINANCIAL: 'Financial',
      LEGAL: 'Legal',
      OPERATIONAL: 'Operational',
      HR: 'Human Resources',
      COMMERCIAL_MARKET: 'Commercial & Market',
      IT_TECHNOLOGY: 'IT & Technology',
      ESG: 'ESG',
      TAX: 'Tax',
      STRATEGIC_FIT: 'Strategic Fit',
      REGULATORY: 'Regulatory',
      INSURANCE: 'Insurance',
      INTELLECTUAL_PROPERTY: 'Intellectual Property',
      REAL_ESTATE_ASSET: 'Real Estate & Asset',
      CUSTOMER_SUPPLIER: 'Customer & Supplier',
      COMPLIANCE_RISK: 'Compliance & Risk'
    };
    return displayNames[type] || type;
  }

  // Get persona icon class for UI
  getPersonaIcon(type: string): string {
    const iconClasses: Record<string, string> = {
      FINANCIAL: 'text-green-600',
      LEGAL: 'text-blue-600',
      OPERATIONAL: 'text-purple-600',
      HR: 'text-orange-600',
      COMMERCIAL_MARKET: 'text-red-600',
      IT_TECHNOLOGY: 'text-cyan-600',
      ESG: 'text-emerald-600',
      TAX: 'text-yellow-600',
      STRATEGIC_FIT: 'text-indigo-600',
      REGULATORY: 'text-gray-600',
      INSURANCE: 'text-pink-600',
      INTELLECTUAL_PROPERTY: 'text-violet-600',
      REAL_ESTATE_ASSET: 'text-brown-600',
      CUSTOMER_SUPPLIER: 'text-teal-600',
      COMPLIANCE_RISK: 'text-slate-600'
    };
    return iconClasses[type] || 'text-gray-600';
  }
}

export const personaService = new PersonaService();
