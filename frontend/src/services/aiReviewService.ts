import { buildApiUrl, getAuthHeaders } from '../config/api';

export interface AIPersona {
  id: string;
  name: string;
  type: string;
  description: string;
  isActive: boolean;
}



export interface AIReviewResult {
  persona: {
    id: string;
    name: string;
    type: string;
  };
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'REQUIRES_ATTENTION';
  progress: number;
  analysis: {
    summary: string;
    confidence_score: number;
    risks: Array<{
      id: string;
      title: string;
      description: string;
      severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
      category: string;
      recommendation: string;
      source_documents: string[];
    }>;
    recommendations: Array<{
      id: string;
      title: string;
      description: string;
      priority: 'LOW' | 'MEDIUM' | 'HIGH';
      action_required: boolean;
      source_documents: string[];
    }>;
    extracted_data: Array<{
      field: string;
      value: string;
      confidence: number;
      source_document: string;
      verification_status: 'VERIFIED' | 'FLAGGED' | 'PENDING';
    }>;
    document_insights: {
      [documentId: string]: {
        processed_at: string;
        key_findings: string[];
        risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
        completeness_score: number;
      };
    };
  };
  documentsAnalyzed: number;
  totalDocuments: number;
  currentDocument?: string;
  errorMessage?: string;
  estimatedTimeRemaining: number;
  startedAt?: string;
  completedAt?: string;
  lastUpdated: string;
}



class AIReviewService {
  // Helper method for API calls
  private async apiCall(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = buildApiUrl(endpoint);
    const headers = getAuthHeaders();

    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle authentication errors gracefully
      if (response.status === 401) {
        console.warn('Authentication required for endpoint:', endpoint);
        throw new Error('Authentication required. Please log in to access this feature.');
      }

      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Start AI review for a deal
  async startReview(dealId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.apiCall('/ai-review/start', {
        method: 'POST',
        body: JSON.stringify({ dealId }),
      });
      return response;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to start AI review');
    }
  }

  // Get AI review results with progress and status
  async getResults(dealId: string): Promise<{
    results: AIReviewResult[];
    summary: {
      dealId: string;
      isRunning: boolean;
      overallProgress: number;
      totalDocuments: number;
      totalProcessedDocuments: number;
      totalPersonas: number;
      completedPersonas: number;
      failedPersonas: number;
      estimatedCompletionTime: Date | null;
    };
  }> {
    try {
      const response = await this.apiCall(`/ai-review/results/${dealId}`);
      return {
        results: response.data,
        summary: response.summary
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get AI review results');
    }
  }

  // Get available AI personas
  async getPersonas(): Promise<AIPersona[]> {
    try {
      const response = await this.apiCall('/ai-review/personas');
      return response.data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get AI personas');
    }
  }

  // Test Gemini API connection
  async testConnection(): Promise<{ connected: boolean; message: string }> {
    try {
      const response = await this.apiCall('/ai-review/test-connection', {
        method: 'POST',
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to test connection');
    }
  }

  // Initialize AI personas
  async initializePersonas(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.apiCall('/ai-review/initialize-personas', {
        method: 'POST',
      });
      return response;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to initialize personas');
    }
  }

  // Reset stuck AI review
  async resetReview(dealId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.apiCall(`/ai-review/reset/${dealId}`, {
        method: 'POST',
      });
      return response;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to reset AI review');
    }
  }

  // Helper method to get persona icon based on type
  getPersonaIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'OPERATIONAL': 'Building',
      'COMPLIANCE': 'Shield',
      'ESG': 'Leaf',
      'STRATEGIC': 'Target',
      'RISK': 'AlertTriangle'
    };
    return iconMap[type] || 'Bot';
  }

  // Helper method to get persona color based on type
  getPersonaColor(type: string): string {
    const colorMap: { [key: string]: string } = {
      'OPERATIONAL': 'text-orange-600',
      'COMPLIANCE': 'text-red-600',
      'ESG': 'text-emerald-600',
      'STRATEGIC': 'text-indigo-600',
      'RISK': 'text-yellow-600'
    };
    return colorMap[type] || 'text-gray-600';
  }

  // Helper method to format status for display
  formatStatus(status: string): string {
    return status.toLowerCase().replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  // Helper method to calculate overall confidence
  calculateOverallConfidence(results: AIReviewResult[]): number {
    if (results.length === 0) return 0;

    const completedResults = results.filter(r => r.status === 'COMPLETED' && r.analysis);
    if (completedResults.length === 0) return 0;

    const totalConfidence = completedResults.reduce((sum, result) =>
      sum + (result.analysis?.confidence_score || 0), 0
    );

    return Math.round(totalConfidence / completedResults.length);
  }

  // Helper method to get all risks from results
  getAllRisks(results: AIReviewResult[]): Array<any> {
    const allRisks: Array<any> = [];

    results.forEach(result => {
      if (result.analysis?.risks) {
        result.analysis.risks.forEach(risk => {
          allRisks.push({
            ...risk,
            persona: result.persona.name,
            personaType: result.persona.type
          });
        });
      }
    });

    // Sort by severity
    const severityOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
    return allRisks.sort((a, b) =>
      (severityOrder[b.severity as keyof typeof severityOrder] || 0) -
      (severityOrder[a.severity as keyof typeof severityOrder] || 0)
    );
  }

  // Helper method to get all recommendations from results
  getAllRecommendations(results: AIReviewResult[]): Array<any> {
    const allRecommendations: Array<any> = [];

    results.forEach(result => {
      if (result.analysis?.recommendations) {
        result.analysis.recommendations.forEach(rec => {
          allRecommendations.push({
            ...rec,
            persona: result.persona.name,
            personaType: result.persona.type
          });
        });
      }
    });

    // Sort by priority
    const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
    return allRecommendations.sort((a, b) =>
      (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) -
      (priorityOrder[a.priority as keyof typeof priorityOrder] || 0)
    );
  }

  // Helper method to get all extracted data from results
  getAllExtractedData(results: AIReviewResult[]): Array<any> {
    const allData: Array<any> = [];

    results.forEach(result => {
      if (result.analysis?.extracted_data) {
        result.analysis.extracted_data.forEach(data => {
          allData.push({
            ...data,
            persona: result.persona.name,
            personaType: result.persona.type,
            category: this.getCategoryFromPersonaType(result.persona.type)
          });
        });
      }
    });

    return allData.sort((a, b) => b.confidence - a.confidence);
  }

  // Helper method to get category from persona type
  private getCategoryFromPersonaType(type: string): string {
    const categoryMap: { [key: string]: string } = {
      'LEGAL': 'Legal',
      'FINANCIAL': 'Financial',
      'TECHNICAL': 'Technical',
      'OPERATIONAL': 'Operational',
      'COMPLIANCE': 'Compliance',
      'ESG': 'ESG',
      'STRATEGIC': 'Strategic',
      'RISK': 'Risk'
    };
    return categoryMap[type] || 'Other';
  }
}

export const aiReviewService = new AIReviewService();
