# Docker Setup Guide

## Prerequisites

- Docker and Docker Compose installed on your system
- At least 4GB of available RAM
- Ports 8080, 8000, and 5436 available

## Quick Start

1. **<PERSON><PERSON> and navigate to the project:**
   ```bash
   git clone <your-repo-url>
   cd due-diligence-nexus-platform
   ```

2. **Set up environment files:**
   ```bash
   # Copy environment files
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env

   # Edit the environment files as needed
   # Important: Update DATABASE_URL in backend/.env to match docker-compose settings
   ```

3. **Build and start all services:**
   ```bash
   docker-compose up --build
   ```

4. **Access the application:**
   - Frontend: http://localhost:8080
   - Backend API: http://localhost:8000
   - API Health Check: http://localhost:8000/api/health
   - Database: localhost:5436 (postgres/password)

## Service Details

### Services Included:
- **Frontend**: React/Vite application with development server
- **Backend**: Node.js/TypeScript API with Express (development mode)
- **PostgreSQL**: Database server
- **Redis**: Cache and session store

### Ports:
- `8080`: Frontend application
- `8000`: Backend API
- `5436`: PostgreSQL database

### Volumes:
- `postgres-data`: PostgreSQL data persistence
- `redis-data`: Redis data persistence
- Development volumes for hot reloading

## Development Commands

### Start services in detached mode:
```bash
docker-compose up -d
```

### View logs:
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Stop services:
```bash
docker-compose down
```

### Rebuild specific service:
```bash
# Rebuild backend
docker-compose build backend

# Rebuild frontend
docker-compose build frontend
```

### Database operations:
```bash
# Run Prisma migrations
docker-compose exec backend npx prisma migrate dev

# Generate Prisma client
docker-compose exec backend npx prisma generate

# Seed database
docker-compose exec backend npm run db:seed

# Access database
docker-compose exec postgres psql -U postgres -d ddnexus_db
```

### Access service shells:
```bash
# Backend container
docker-compose exec backend sh

# Frontend container (nginx)
docker-compose exec frontend sh

# Database container
docker-compose exec postgres bash
```

## Production Deployment

### Environment Variables to Change:
1. **Backend (.env):**
   - `JWT_SECRET`: Use a strong, unique secret
   - `JWT_REFRESH_SECRET`: Use a different strong secret
   - `DATABASE_URL`: Update with production database credentials
   - `REDIS_URL`: Update with production Redis URL
   - `NODE_ENV=production`

2. **Frontend (.env):**
   - `VITE_API_URL`: Update to production backend URL
   - `VITE_NODE_ENV=production`
   - `VITE_ENABLE_DEBUG=false`

### Security Considerations:
- Change default database passwords
- Use strong JWT secrets
- Configure proper CORS origins
- Set up SSL/TLS certificates
- Configure firewall rules
- Use environment-specific secrets management

## Troubleshooting

### Common Issues:

1. **Port conflicts:**
   ```bash
   # Check what's using the ports
   lsof -i :8080
   lsof -i :8000
   lsof -i :5436
   ```

2. **Database connection issues:**
   ```bash
   # Check if PostgreSQL is healthy
   docker-compose ps postgres
   
   # Check database logs
   docker-compose logs postgres
   ```

3. **Build failures:**
   ```bash
   # Clean build (removes cache)
   docker-compose build --no-cache
   
   # Remove all containers and rebuild
   docker-compose down
   docker system prune -f
   docker-compose up --build
   ```

4. **Permission issues:**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Health Checks:
All services include health checks. Check status with:
```bash
docker-compose ps
```

Healthy services will show "Up (healthy)" status.

## Development vs Production

### Development:
- Uses `docker-compose up` for easy debugging
- Includes development tools and hot reloading
- Exposes all ports for direct access

### Production:
- Use `docker-compose -f docker-compose.prod.yml up -d`
- Implement proper secrets management
- Use reverse proxy (nginx/traefik) for SSL termination
- Set up monitoring and logging
- Configure backup strategies for volumes
