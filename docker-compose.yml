services:
  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: ddnexus-backend:latest
    container_name: ddnexus-backend
    ports:
      - "7081:8000"
    env_file:
      - ./backend/.env
    environment:
      - DOCKER_CONTAINER=true
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - app-network
    dns:
      - 8.8.8.8
      - 1.1.1.1
    command: >
      sh -c "
        cd /app &&
        echo 'Waiting for database to be ready...' &&
        sleep 5 &&
        npx prisma generate &&
        npx prisma migrate deploy &&
        npm run db:seed &&
        npm run dev
      "

  # Frontend React/Vite application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    image: ddnexus-frontend:latest
    container_name: ddnexus-frontend
    ports:
      - "7080:8080"
    environment:
      - VITE_API_URL=http://localhost:8000
      - NODE_ENV=production
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL database
  postgres:
    image: postgres:15
    container_name: ddnexus-postgres
    restart: unless-stopped
#    ports:
 #     - "5436:5432"
    volumes:
      - ddnexus_pgdata:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=z8B7bXhahMg8EApH3wA
      - POSTGRES_DB=ddnexus_db
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  ddnexus_pgdata:

networks:
  app-network:
    driver: bridge
